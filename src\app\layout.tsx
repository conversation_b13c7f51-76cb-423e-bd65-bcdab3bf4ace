import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/components/providers/AuthProvider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'SmartForm Defender - Lead Generation Without the Spam',
  description: 'Protect your lead forms from spam and fake submissions with AI-powered validation and real-time filtering.',
  keywords: 'lead generation, spam protection, form validation, CRM integration, HubSpot',
  authors: [{ name: 'SmartForm Defender' }],
  openGraph: {
    title: 'SmartForm Defender - Lead Generation Without the Spam',
    description: 'Protect your lead forms from spam and fake submissions with AI-powered validation and real-time filtering.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SmartForm Defender - Lead Generation Without the Spam',
    description: 'Protect your lead forms from spam and fake submissions with AI-powered validation and real-time filtering.',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  );
}
