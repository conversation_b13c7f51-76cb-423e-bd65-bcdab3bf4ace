/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/validate/email/route";
exports.ids = ["app/api/validate/email/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Femail%2Froute&page=%2Fapi%2Fvalidate%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Femail%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Femail%2Froute&page=%2Fapi%2Fvalidate%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Femail%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_validate_email_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/validate/email/route.ts */ \"(rsc)/./src/app/api/validate/email/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/validate/email/route\",\n        pathname: \"/api/validate/email\",\n        filename: \"route\",\n        bundlePath: \"app/api/validate/email/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\validate\\\\email\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_validate_email_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Femail%2Froute&page=%2Fapi%2Fvalidate%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Femail%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/validate/email/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/validate/email/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./src/lib/validation.ts\");\n\n\nasync function POST(request) {\n    try {\n        const { email } = await request.json();\n        if (!email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email is required'\n            }, {\n                status: 400\n            });\n        }\n        const isValid = (0,_lib_validation__WEBPACK_IMPORTED_MODULE_1__.validateEmail)(email);\n        // Additional validation logic could go here\n        // For example, checking against disposable email services\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            valid: isValid,\n            message: isValid ? 'Email is valid' : 'Invalid email format'\n        });\n    } catch (error) {\n        console.error('Email validation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/validate/email/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validation.ts":
/*!*******************************!*\
  !*** ./src/lib/validation.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSpamScore: () => (/* binding */ calculateSpamScore),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   phoneSchema: () => (/* binding */ phoneSchema),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateFormData: () => (/* binding */ validateFormData),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n// Validation schemas\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email();\nconst phoneSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/);\n// Utility functions\nfunction generateApiKey() {\n    return 'sfd_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\nfunction validateEmail(email) {\n    return emailSchema.safeParse(email).success;\n}\nfunction validatePhone(phone) {\n    const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, '');\n    return phoneSchema.safeParse(cleanPhone).success;\n}\nasync function validateFormData(formData, project) {\n    const errors = {};\n    const warnings = {};\n    // Email validation\n    if (formData.email) {\n        if (!validateEmail(formData.email)) {\n            errors.email = 'Invalid email format';\n        } else if (project.enableEmailValidation) {\n            // Additional email validation would go here\n            const emailValidation = await validateEmailAdvanced(formData.email);\n            if (!emailValidation.valid) {\n                errors.email = emailValidation.message;\n            }\n        }\n    }\n    // Phone validation\n    if (formData.phone) {\n        if (!validatePhone(formData.phone)) {\n            errors.phone = 'Invalid phone format';\n        } else if (project.enablePhoneValidation) {\n            const phoneValidation = await validatePhoneAdvanced(formData.phone);\n            if (!phoneValidation.valid) {\n                errors.phone = phoneValidation.message;\n            }\n        }\n    }\n    // Required field validation\n    const requiredFields = [\n        'email',\n        'name'\n    ];\n    requiredFields.forEach((field)=>{\n        if (!formData[field] || formData[field].trim() === '') {\n            errors[field] = `${field} is required`;\n        }\n    });\n    // Content validation\n    if (formData.message && formData.message.length < 10) {\n        warnings.message = 'Message seems too short';\n    }\n    if (formData.name && formData.name.length < 2) {\n        warnings.name = 'Name seems too short';\n    }\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors,\n        warnings\n    };\n}\nasync function validateEmailAdvanced(email) {\n    try {\n        // In production, you would use a real email validation service\n        // For now, just do basic checks\n        // Check for disposable email domains\n        const disposableDomains = [\n            '10minutemail.com',\n            'tempmail.org',\n            'guerrillamail.com',\n            'mailinator.com'\n        ];\n        const domain = email.split('@')[1];\n        if (disposableDomains.includes(domain)) {\n            return {\n                valid: false,\n                message: 'Disposable email addresses are not allowed'\n            };\n        }\n        // Check for common typos in popular domains\n        const commonDomains = {\n            'gmial.com': 'gmail.com',\n            'gmai.com': 'gmail.com',\n            'yahooo.com': 'yahoo.com',\n            'hotmial.com': 'hotmail.com'\n        };\n        if (commonDomains[domain]) {\n            return {\n                valid: false,\n                message: `Did you mean ${email.replace(domain, commonDomains[domain])}?`\n            };\n        }\n        return {\n            valid: true,\n            message: 'Email is valid'\n        };\n    } catch (error) {\n        return {\n            valid: true,\n            message: 'Could not validate email'\n        };\n    }\n}\nasync function validatePhoneAdvanced(phone) {\n    try {\n        // In production, you would use a phone validation service\n        const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, '');\n        // Basic length check\n        if (cleanPhone.length < 7 || cleanPhone.length > 15) {\n            return {\n                valid: false,\n                message: 'Phone number length is invalid'\n            };\n        }\n        return {\n            valid: true,\n            message: 'Phone number is valid'\n        };\n    } catch (error) {\n        return {\n            valid: true,\n            message: 'Could not validate phone number'\n        };\n    }\n}\nfunction calculateSpamScore(data) {\n    let score = 0;\n    // Basic spam detection logic\n    if (data.email && data.email.includes('test')) score += 0.2;\n    if (data.message && data.message.length < 10) score += 0.3;\n    if (data.name && data.name.length < 2) score += 0.2;\n    return Math.min(score, 1);\n}\nfunction formatDate(date) {\n    return date.toLocaleDateString();\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat().format(num);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validation.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Femail%2Froute&page=%2Fapi%2Fvalidate%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Femail%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();