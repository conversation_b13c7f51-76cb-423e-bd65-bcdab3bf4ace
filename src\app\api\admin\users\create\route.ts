import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import bcrypt from 'bcryptjs';

const createUserSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  company: z.string().optional(),
  phone: z.string().optional(),
  role: z.enum(['USER', 'ADMIN']),
  plan: z.enum(['FREE', 'PRO', 'ENTERPRISE']),
  password: z.string().min(6, 'Password must be at least 6 characters long')
});

export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdmin();

    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    // Check if email is already taken
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    });

    if (existingUser) {
      return NextResponse.json({ 
        error: 'Email already in use' 
      }, { status: 400 });
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12);

    // Create the user
    const newUser = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        company: validatedData.company || null,
        phone: validatedData.phone || null,
        role: validatedData.role,
        plan: validatedData.plan,
        password: hashedPassword,
        emailVerified: new Date() // Auto-verify admin-created users
      }
    });

    // Create a default project for the new user
    await prisma.project.create({
      data: {
        name: 'My First Project',
        domain: 'example.com',
        userId: newUser.id,
        isActive: true
      }
    });

    // Return user data without password
    const { password, ...userWithoutPassword } = newUser;
    
    return NextResponse.json({
      message: 'User created successfully',
      user: userWithoutPassword
    });

  } catch (error) {
    console.error('Admin user creation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid data', 
        details: error.errors 
      }, { status: 400 });
    }
    
    if (error instanceof Error) {
      if (error.message === 'Authentication required' || error.message === 'Admin access required') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        );
      }
    }

    return NextResponse.json({ 
      error: 'Failed to create user' 
    }, { status: 500 });
  }
}
