/**
 * ACCESSIBILITY-COMPLIANT FORM COMPONENTS
 * 
 * These components ensure proper contrast and prevent white text on white background issues.
 * All form inputs have explicit background and text colors for maximum accessibility.
 * 
 * USAGE: Import and use these components instead of raw HTML form elements
 * to maintain consistent styling and accessibility across the application.
 */

import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  helperText?: string;
  children: React.ReactNode;
}

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

// Base input styling with guaranteed accessibility
const baseInputClasses = "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500 disabled:bg-gray-100 disabled:text-gray-500";

const baseLabelClasses = "block text-sm font-medium text-gray-700 mb-1";

const errorClasses = "text-red-600 text-sm mt-1";

const helperTextClasses = "text-gray-500 text-sm mt-1";

export function FormInput({ label, error, helperText, className = '', ...props }: InputProps) {
  const inputClasses = `${baseInputClasses} ${error ? 'border-red-300 focus:ring-red-500' : ''} ${className}`;

  return (
    <div className="space-y-1">
      {label && (
        <label className={baseLabelClasses} htmlFor={props.id}>
          {label}
        </label>
      )}
      <input
        {...props}
        className={inputClasses}
      />
      {error && <p className={errorClasses}>{error}</p>}
      {helperText && !error && <p className={helperTextClasses}>{helperText}</p>}
    </div>
  );
}

export function FormSelect({ label, error, helperText, className = '', children, ...props }: SelectProps) {
  const selectClasses = `${baseInputClasses} ${error ? 'border-red-300 focus:ring-red-500' : ''} ${className}`;

  return (
    <div className="space-y-1">
      {label && (
        <label className={baseLabelClasses} htmlFor={props.id}>
          {label}
        </label>
      )}
      <select
        {...props}
        className={selectClasses}
      >
        {children}
      </select>
      {error && <p className={errorClasses}>{error}</p>}
      {helperText && !error && <p className={helperTextClasses}>{helperText}</p>}
    </div>
  );
}

export function FormTextarea({ label, error, helperText, className = '', ...props }: TextareaProps) {
  const textareaClasses = `${baseInputClasses} ${error ? 'border-red-300 focus:ring-red-500' : ''} ${className}`;

  return (
    <div className="space-y-1">
      {label && (
        <label className={baseLabelClasses} htmlFor={props.id}>
          {label}
        </label>
      )}
      <textarea
        {...props}
        className={textareaClasses}
      />
      {error && <p className={errorClasses}>{error}</p>}
      {helperText && !error && <p className={helperTextClasses}>{helperText}</p>}
    </div>
  );
}

export function FormCheckbox({ 
  label, 
  error, 
  helperText, 
  className = '', 
  ...props 
}: InputProps) {
  return (
    <div className="space-y-1">
      <div className="flex items-center">
        <input
          {...props}
          type="checkbox"
          className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded bg-white ${className}`}
        />
        {label && (
          <label className="ml-2 block text-sm text-gray-900" htmlFor={props.id}>
            {label}
          </label>
        )}
      </div>
      {error && <p className={errorClasses}>{error}</p>}
      {helperText && !error && <p className={helperTextClasses}>{helperText}</p>}
    </div>
  );
}

// Form section wrapper for consistent styling
export function FormSection({ 
  title, 
  description, 
  children, 
  className = '' 
}: {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      {title && (
        <div className="mb-4">
          <h2 className="text-lg font-medium text-gray-900">{title}</h2>
          {description && (
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

// Button components with consistent styling
export function PrimaryButton({ 
  children, 
  className = '', 
  disabled = false,
  loading = false,
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement> & { loading?: boolean }) {
  return (
    <button
      {...props}
      disabled={disabled || loading}
      className={`px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
    >
      {loading ? 'Loading...' : children}
    </button>
  );
}

export function SecondaryButton({ 
  children, 
  className = '', 
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement>) {
  return (
    <button
      {...props}
      className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
    >
      {children}
    </button>
  );
}

export function DangerButton({ 
  children, 
  className = '', 
  disabled = false,
  loading = false,
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement> & { loading?: boolean }) {
  return (
    <button
      {...props}
      disabled={disabled || loading}
      className={`px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-red-500 ${className}`}
    >
      {loading ? 'Loading...' : children}
    </button>
  );
}
