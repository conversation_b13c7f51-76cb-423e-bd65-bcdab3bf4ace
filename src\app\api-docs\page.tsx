import { Shield, Code, Key, Database, Zap } from 'lucide-react';
import Link from 'next/link';

export default function ApiDocsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <Shield className="h-8 w-8 text-primary-600" />
                <span className="ml-2 text-xl font-bold text-gray-900">SmartForm Defender</span>
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/docs" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  ← Back to Docs
                </Link>
                <Link href="/dashboard" className="btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">API Reference</h3>
              <nav className="space-y-2">
                <a href="#authentication" className="block text-primary-600 hover:text-primary-700 py-1">
                  Authentication
                </a>
                <a href="#validation" className="block text-gray-600 hover:text-gray-900 py-1">
                  Form Validation
                </a>
                <a href="#submissions" className="block text-gray-600 hover:text-gray-900 py-1">
                  Submissions
                </a>
                <a href="#integrations" className="block text-gray-600 hover:text-gray-900 py-1">
                  Integrations
                </a>
                <a href="#analytics" className="block text-gray-600 hover:text-gray-900 py-1">
                  Analytics
                </a>
                <a href="#webhooks" className="block text-gray-600 hover:text-gray-900 py-1">
                  Webhooks
                </a>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              {/* Header */}
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  SmartForm Defender API
                </h1>
                <p className="text-xl text-gray-600">
                  Complete REST API reference for integrating SmartForm Defender into your applications
                </p>
              </div>

              {/* Base URL */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Base URL</h3>
                <code className="text-blue-800 bg-blue-100 px-3 py-1 rounded">
                  https://api.smartformdefender.com/v1
                </code>
              </div>

              {/* Authentication */}
              <section id="authentication" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Key className="h-6 w-6 text-primary-600 mr-2" />
                  Authentication
                </h2>
                
                <p className="text-gray-600 mb-6">
                  All API requests require authentication using your API key in the request headers.
                </p>

                <div className="bg-gray-900 rounded-lg p-4 mb-6 overflow-x-auto">
                  <code className="text-green-400 text-sm">
{`curl -H "X-API-Key: your_api_key_here" \\
     -H "Content-Type: application/json" \\
     https://api.smartformdefender.com/v1/validate`}
                  </code>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-yellow-800 text-sm">
                    <strong>Security Note:</strong> Keep your API key secure and never expose it in client-side code. 
                    Use environment variables or secure configuration management.
                  </p>
                </div>
              </section>

              {/* Form Validation */}
              <section id="validation" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Zap className="h-6 w-6 text-primary-600 mr-2" />
                  Form Validation
                </h2>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-3">POST /validate/submission</h3>
                <p className="text-gray-600 mb-4">
                  Validate a form submission for spam and return a confidence score.
                </p>

                <h4 className="font-semibold text-gray-900 mb-2">Request Body</h4>
                <div className="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                  <code className="text-green-400 text-sm">
{`{
  "projectId": "your_project_id",
  "formData": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "message": "I'm interested in your product"
  },
  "metadata": {
    "userAgent": "Mozilla/5.0...",
    "ipAddress": "***********",
    "timestamp": "2025-01-15T10:30:00Z"
  }
}`}
                  </code>
                </div>

                <h4 className="font-semibold text-gray-900 mb-2">Response</h4>
                <div className="bg-gray-900 rounded-lg p-4 mb-6 overflow-x-auto">
                  <code className="text-green-400 text-sm">
{`{
  "success": true,
  "data": {
    "isSpam": false,
    "confidence": 0.15,
    "reasons": [],
    "submissionId": "sub_1234567890",
    "validatedFields": {
      "email": {
        "isValid": true,
        "deliverable": true
      }
    }
  }
}`}
                  </code>
                </div>
              </section>

              {/* Submissions */}
              <section id="submissions" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Database className="h-6 w-6 text-primary-600 mr-2" />
                  Submissions
                </h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">GET /submissions</h3>
                    <p className="text-gray-600 mb-4">Retrieve form submissions for your project.</p>
                    
                    <h4 className="font-semibold text-gray-900 mb-2">Query Parameters</h4>
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <ul className="space-y-2 text-sm">
                        <li><code className="bg-gray-200 px-2 py-1 rounded">projectId</code> - Your project ID (required)</li>
                        <li><code className="bg-gray-200 px-2 py-1 rounded">limit</code> - Number of results (default: 50, max: 100)</li>
                        <li><code className="bg-gray-200 px-2 py-1 rounded">offset</code> - Pagination offset (default: 0)</li>
                        <li><code className="bg-gray-200 px-2 py-1 rounded">status</code> - Filter by status: 'spam', 'legitimate', 'pending'</li>
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">GET /submissions/:id</h3>
                    <p className="text-gray-600">Retrieve a specific submission by ID.</p>
                  </div>
                </div>
              </section>

              {/* Integrations */}
              <section id="integrations" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Integrations</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">GET /integrations</h3>
                    <p className="text-gray-600">List all configured integrations for your project.</p>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">POST /integrations</h3>
                    <p className="text-gray-600 mb-4">Create a new integration.</p>
                    
                    <div className="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                      <code className="text-green-400 text-sm">
{`{
  "type": "HUBSPOT",
  "name": "HubSpot CRM",
  "config": {
    "accessToken": "your_hubspot_token",
    "portalId": "12345678"
  }
}`}
                      </code>
                    </div>
                  </div>
                </div>
              </section>

              {/* Analytics */}
              <section id="analytics" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Analytics</h2>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">GET /analytics/summary</h3>
                  <p className="text-gray-600 mb-4">Get analytics summary for your project.</p>
                  
                  <h4 className="font-semibold text-gray-900 mb-2">Response</h4>
                  <div className="bg-gray-900 rounded-lg p-4 mb-6 overflow-x-auto">
                    <code className="text-green-400 text-sm">
{`{
  "success": true,
  "data": {
    "totalSubmissions": 1250,
    "spamBlocked": 875,
    "legitimateLeads": 375,
    "accuracy": 99.2,
    "timeRange": "last_30_days"
  }
}`}
                    </code>
                  </div>
                </div>
              </section>

              {/* Webhooks */}
              <section id="webhooks" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Webhooks</h2>
                
                <p className="text-gray-600 mb-6">
                  Configure webhooks to receive real-time notifications when submissions are processed.
                </p>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Webhook Events</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <ul className="space-y-2 text-sm">
                        <li><code className="bg-gray-200 px-2 py-1 rounded">submission.validated</code> - Fired when a submission is validated</li>
                        <li><code className="bg-gray-200 px-2 py-1 rounded">submission.spam_detected</code> - Fired when spam is detected</li>
                        <li><code className="bg-gray-200 px-2 py-1 rounded">integration.sync_complete</code> - Fired when CRM sync completes</li>
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Webhook Payload</h3>
                    <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                      <code className="text-green-400 text-sm">
{`{
  "event": "submission.validated",
  "timestamp": "2025-01-15T10:30:00Z",
  "data": {
    "submissionId": "sub_1234567890",
    "projectId": "proj_abcdef",
    "isSpam": false,
    "confidence": 0.15
  }
}`}
                      </code>
                    </div>
                  </div>
                </div>
              </section>

              {/* Rate Limits */}
              <section className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Rate Limits</h2>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Standard Plan</h3>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 1,000 requests/hour</li>
                      <li>• 100 requests/minute</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Professional Plan</h3>
                    <ul className="text-gray-600 text-sm space-y-1">
                      <li>• 10,000 requests/hour</li>
                      <li>• 500 requests/minute</li>
                    </ul>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-primary-400" />
              <span className="ml-2 text-xl font-bold">SmartForm Defender</span>
            </div>
            <div className="text-gray-400">
              © 2025 SmartForm Defender. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
