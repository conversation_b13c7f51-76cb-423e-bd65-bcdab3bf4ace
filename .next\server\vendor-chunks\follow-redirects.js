/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/follow-redirects";
exports.ids = ["vendor-chunks/follow-redirects"];
exports.modules = {

/***/ "(rsc)/./node_modules/follow-redirects/debug.js":
/*!************************************************!*\
  !*** ./node_modules/follow-redirects/debug.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var debug;\n\nmodule.exports = function () {\n  if (!debug) {\n    try {\n      /* eslint global-require: off */\n      debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/debug/src/index.js\")(\"follow-redirects\");\n    }\n    catch (error) { /* */ }\n    if (typeof debug !== \"function\") {\n      debug = function () { /* */ };\n    }\n  }\n  debug.apply(null, arguments);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZm9sbG93LXJlZGlyZWN0cy9kZWJ1Zy5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsbUJBQU8sQ0FBQyxzREFBTztBQUM3QjtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NtYXJ0Zm9ybS1kZWZlbmRlci8uL25vZGVfbW9kdWxlcy9mb2xsb3ctcmVkaXJlY3RzL2RlYnVnLmpzPzNkNjEiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGRlYnVnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICgpIHtcbiAgaWYgKCFkZWJ1Zykge1xuICAgIHRyeSB7XG4gICAgICAvKiBlc2xpbnQgZ2xvYmFsLXJlcXVpcmU6IG9mZiAqL1xuICAgICAgZGVidWcgPSByZXF1aXJlKFwiZGVidWdcIikoXCJmb2xsb3ctcmVkaXJlY3RzXCIpO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyb3IpIHsgLyogKi8gfVxuICAgIGlmICh0eXBlb2YgZGVidWcgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgZGVidWcgPSBmdW5jdGlvbiAoKSB7IC8qICovIH07XG4gICAgfVxuICB9XG4gIGRlYnVnLmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/follow-redirects/debug.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/follow-redirects/index.js":
/*!************************************************!*\
  !*** ./node_modules/follow-redirects/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var url = __webpack_require__(/*! url */ \"url\");\nvar URL = url.URL;\nvar http = __webpack_require__(/*! http */ \"http\");\nvar https = __webpack_require__(/*! https */ \"https\");\nvar Writable = (__webpack_require__(/*! stream */ \"stream\").Writable);\nvar assert = __webpack_require__(/*! assert */ \"assert\");\nvar debug = __webpack_require__(/*! ./debug */ \"(rsc)/./node_modules/follow-redirects/debug.js\");\n\n// Preventive platform detection\n// istanbul ignore next\n(function detectUnsupportedEnvironment() {\n  var looksLikeNode = typeof process !== \"undefined\";\n  var looksLikeBrowser = typeof window !== \"undefined\" && typeof document !== \"undefined\";\n  var looksLikeV8 = isFunction(Error.captureStackTrace);\n  if (!looksLikeNode && (looksLikeBrowser || !looksLikeV8)) {\n    console.warn(\"The follow-redirects package should be excluded from browser builds.\");\n  }\n}());\n\n// Whether to use the native URL object or the legacy url module\nvar useNativeURL = false;\ntry {\n  assert(new URL(\"\"));\n}\ncatch (error) {\n  useNativeURL = error.code === \"ERR_INVALID_URL\";\n}\n\n// URL fields to preserve in copy operations\nvar preservedUrlFields = [\n  \"auth\",\n  \"host\",\n  \"hostname\",\n  \"href\",\n  \"path\",\n  \"pathname\",\n  \"port\",\n  \"protocol\",\n  \"query\",\n  \"search\",\n  \"hash\",\n];\n\n// Create handlers that pass events from native requests\nvar events = [\"abort\", \"aborted\", \"connect\", \"error\", \"socket\", \"timeout\"];\nvar eventHandlers = Object.create(null);\nevents.forEach(function (event) {\n  eventHandlers[event] = function (arg1, arg2, arg3) {\n    this._redirectable.emit(event, arg1, arg2, arg3);\n  };\n});\n\n// Error types with codes\nvar InvalidUrlError = createErrorType(\n  \"ERR_INVALID_URL\",\n  \"Invalid URL\",\n  TypeError\n);\nvar RedirectionError = createErrorType(\n  \"ERR_FR_REDIRECTION_FAILURE\",\n  \"Redirected request failed\"\n);\nvar TooManyRedirectsError = createErrorType(\n  \"ERR_FR_TOO_MANY_REDIRECTS\",\n  \"Maximum number of redirects exceeded\",\n  RedirectionError\n);\nvar MaxBodyLengthExceededError = createErrorType(\n  \"ERR_FR_MAX_BODY_LENGTH_EXCEEDED\",\n  \"Request body larger than maxBodyLength limit\"\n);\nvar WriteAfterEndError = createErrorType(\n  \"ERR_STREAM_WRITE_AFTER_END\",\n  \"write after end\"\n);\n\n// istanbul ignore next\nvar destroy = Writable.prototype.destroy || noop;\n\n// An HTTP(S) request that can be redirected\nfunction RedirectableRequest(options, responseCallback) {\n  // Initialize the request\n  Writable.call(this);\n  this._sanitizeOptions(options);\n  this._options = options;\n  this._ended = false;\n  this._ending = false;\n  this._redirectCount = 0;\n  this._redirects = [];\n  this._requestBodyLength = 0;\n  this._requestBodyBuffers = [];\n\n  // Attach a callback if passed\n  if (responseCallback) {\n    this.on(\"response\", responseCallback);\n  }\n\n  // React to responses of native requests\n  var self = this;\n  this._onNativeResponse = function (response) {\n    try {\n      self._processResponse(response);\n    }\n    catch (cause) {\n      self.emit(\"error\", cause instanceof RedirectionError ?\n        cause : new RedirectionError({ cause: cause }));\n    }\n  };\n\n  // Perform the first request\n  this._performRequest();\n}\nRedirectableRequest.prototype = Object.create(Writable.prototype);\n\nRedirectableRequest.prototype.abort = function () {\n  destroyRequest(this._currentRequest);\n  this._currentRequest.abort();\n  this.emit(\"abort\");\n};\n\nRedirectableRequest.prototype.destroy = function (error) {\n  destroyRequest(this._currentRequest, error);\n  destroy.call(this, error);\n  return this;\n};\n\n// Writes buffered data to the current native request\nRedirectableRequest.prototype.write = function (data, encoding, callback) {\n  // Writing is not allowed if end has been called\n  if (this._ending) {\n    throw new WriteAfterEndError();\n  }\n\n  // Validate input and shift parameters if necessary\n  if (!isString(data) && !isBuffer(data)) {\n    throw new TypeError(\"data should be a string, Buffer or Uint8Array\");\n  }\n  if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Ignore empty buffers, since writing them doesn't invoke the callback\n  // https://github.com/nodejs/node/issues/22066\n  if (data.length === 0) {\n    if (callback) {\n      callback();\n    }\n    return;\n  }\n  // Only write when we don't exceed the maximum body length\n  if (this._requestBodyLength + data.length <= this._options.maxBodyLength) {\n    this._requestBodyLength += data.length;\n    this._requestBodyBuffers.push({ data: data, encoding: encoding });\n    this._currentRequest.write(data, encoding, callback);\n  }\n  // Error when we exceed the maximum body length\n  else {\n    this.emit(\"error\", new MaxBodyLengthExceededError());\n    this.abort();\n  }\n};\n\n// Ends the current native request\nRedirectableRequest.prototype.end = function (data, encoding, callback) {\n  // Shift parameters if necessary\n  if (isFunction(data)) {\n    callback = data;\n    data = encoding = null;\n  }\n  else if (isFunction(encoding)) {\n    callback = encoding;\n    encoding = null;\n  }\n\n  // Write data if needed and end\n  if (!data) {\n    this._ended = this._ending = true;\n    this._currentRequest.end(null, null, callback);\n  }\n  else {\n    var self = this;\n    var currentRequest = this._currentRequest;\n    this.write(data, encoding, function () {\n      self._ended = true;\n      currentRequest.end(null, null, callback);\n    });\n    this._ending = true;\n  }\n};\n\n// Sets a header value on the current native request\nRedirectableRequest.prototype.setHeader = function (name, value) {\n  this._options.headers[name] = value;\n  this._currentRequest.setHeader(name, value);\n};\n\n// Clears a header value on the current native request\nRedirectableRequest.prototype.removeHeader = function (name) {\n  delete this._options.headers[name];\n  this._currentRequest.removeHeader(name);\n};\n\n// Global timeout for all underlying requests\nRedirectableRequest.prototype.setTimeout = function (msecs, callback) {\n  var self = this;\n\n  // Destroys the socket on timeout\n  function destroyOnTimeout(socket) {\n    socket.setTimeout(msecs);\n    socket.removeListener(\"timeout\", socket.destroy);\n    socket.addListener(\"timeout\", socket.destroy);\n  }\n\n  // Sets up a timer to trigger a timeout event\n  function startTimer(socket) {\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n    }\n    self._timeout = setTimeout(function () {\n      self.emit(\"timeout\");\n      clearTimer();\n    }, msecs);\n    destroyOnTimeout(socket);\n  }\n\n  // Stops a timeout from triggering\n  function clearTimer() {\n    // Clear the timeout\n    if (self._timeout) {\n      clearTimeout(self._timeout);\n      self._timeout = null;\n    }\n\n    // Clean up all attached listeners\n    self.removeListener(\"abort\", clearTimer);\n    self.removeListener(\"error\", clearTimer);\n    self.removeListener(\"response\", clearTimer);\n    self.removeListener(\"close\", clearTimer);\n    if (callback) {\n      self.removeListener(\"timeout\", callback);\n    }\n    if (!self.socket) {\n      self._currentRequest.removeListener(\"socket\", startTimer);\n    }\n  }\n\n  // Attach callback if passed\n  if (callback) {\n    this.on(\"timeout\", callback);\n  }\n\n  // Start the timer if or when the socket is opened\n  if (this.socket) {\n    startTimer(this.socket);\n  }\n  else {\n    this._currentRequest.once(\"socket\", startTimer);\n  }\n\n  // Clean up on events\n  this.on(\"socket\", destroyOnTimeout);\n  this.on(\"abort\", clearTimer);\n  this.on(\"error\", clearTimer);\n  this.on(\"response\", clearTimer);\n  this.on(\"close\", clearTimer);\n\n  return this;\n};\n\n// Proxy all other public ClientRequest methods\n[\n  \"flushHeaders\", \"getHeader\",\n  \"setNoDelay\", \"setSocketKeepAlive\",\n].forEach(function (method) {\n  RedirectableRequest.prototype[method] = function (a, b) {\n    return this._currentRequest[method](a, b);\n  };\n});\n\n// Proxy all public ClientRequest properties\n[\"aborted\", \"connection\", \"socket\"].forEach(function (property) {\n  Object.defineProperty(RedirectableRequest.prototype, property, {\n    get: function () { return this._currentRequest[property]; },\n  });\n});\n\nRedirectableRequest.prototype._sanitizeOptions = function (options) {\n  // Ensure headers are always present\n  if (!options.headers) {\n    options.headers = {};\n  }\n\n  // Since http.request treats host as an alias of hostname,\n  // but the url module interprets host as hostname plus port,\n  // eliminate the host property to avoid confusion.\n  if (options.host) {\n    // Use hostname if set, because it has precedence\n    if (!options.hostname) {\n      options.hostname = options.host;\n    }\n    delete options.host;\n  }\n\n  // Complete the URL object when necessary\n  if (!options.pathname && options.path) {\n    var searchPos = options.path.indexOf(\"?\");\n    if (searchPos < 0) {\n      options.pathname = options.path;\n    }\n    else {\n      options.pathname = options.path.substring(0, searchPos);\n      options.search = options.path.substring(searchPos);\n    }\n  }\n};\n\n\n// Executes the next native request (initial or redirect)\nRedirectableRequest.prototype._performRequest = function () {\n  // Load the native protocol\n  var protocol = this._options.protocol;\n  var nativeProtocol = this._options.nativeProtocols[protocol];\n  if (!nativeProtocol) {\n    throw new TypeError(\"Unsupported protocol \" + protocol);\n  }\n\n  // If specified, use the agent corresponding to the protocol\n  // (HTTP and HTTPS use different types of agents)\n  if (this._options.agents) {\n    var scheme = protocol.slice(0, -1);\n    this._options.agent = this._options.agents[scheme];\n  }\n\n  // Create the native request and set up its event handlers\n  var request = this._currentRequest =\n        nativeProtocol.request(this._options, this._onNativeResponse);\n  request._redirectable = this;\n  for (var event of events) {\n    request.on(event, eventHandlers[event]);\n  }\n\n  // RFC7230§5.3.1: When making a request directly to an origin server, […]\n  // a client MUST send only the absolute path […] as the request-target.\n  this._currentUrl = /^\\//.test(this._options.path) ?\n    url.format(this._options) :\n    // When making a request to a proxy, […]\n    // a client MUST send the target URI in absolute-form […].\n    this._options.path;\n\n  // End a redirected request\n  // (The first request must be ended explicitly with RedirectableRequest#end)\n  if (this._isRedirect) {\n    // Write the request entity and end\n    var i = 0;\n    var self = this;\n    var buffers = this._requestBodyBuffers;\n    (function writeNext(error) {\n      // Only write if this request has not been redirected yet\n      // istanbul ignore else\n      if (request === self._currentRequest) {\n        // Report any write errors\n        // istanbul ignore if\n        if (error) {\n          self.emit(\"error\", error);\n        }\n        // Write the next buffer if there are still left\n        else if (i < buffers.length) {\n          var buffer = buffers[i++];\n          // istanbul ignore else\n          if (!request.finished) {\n            request.write(buffer.data, buffer.encoding, writeNext);\n          }\n        }\n        // End the request if `end` has been called on us\n        else if (self._ended) {\n          request.end();\n        }\n      }\n    }());\n  }\n};\n\n// Processes a response from the current native request\nRedirectableRequest.prototype._processResponse = function (response) {\n  // Store the redirected response\n  var statusCode = response.statusCode;\n  if (this._options.trackRedirects) {\n    this._redirects.push({\n      url: this._currentUrl,\n      headers: response.headers,\n      statusCode: statusCode,\n    });\n  }\n\n  // RFC7231§6.4: The 3xx (Redirection) class of status code indicates\n  // that further action needs to be taken by the user agent in order to\n  // fulfill the request. If a Location header field is provided,\n  // the user agent MAY automatically redirect its request to the URI\n  // referenced by the Location field value,\n  // even if the specific status code is not understood.\n\n  // If the response is not a redirect; return it as-is\n  var location = response.headers.location;\n  if (!location || this._options.followRedirects === false ||\n      statusCode < 300 || statusCode >= 400) {\n    response.responseUrl = this._currentUrl;\n    response.redirects = this._redirects;\n    this.emit(\"response\", response);\n\n    // Clean up\n    this._requestBodyBuffers = [];\n    return;\n  }\n\n  // The response is a redirect, so abort the current request\n  destroyRequest(this._currentRequest);\n  // Discard the remainder of the response to avoid waiting for data\n  response.destroy();\n\n  // RFC7231§6.4: A client SHOULD detect and intervene\n  // in cyclical redirections (i.e., \"infinite\" redirection loops).\n  if (++this._redirectCount > this._options.maxRedirects) {\n    throw new TooManyRedirectsError();\n  }\n\n  // Store the request headers if applicable\n  var requestHeaders;\n  var beforeRedirect = this._options.beforeRedirect;\n  if (beforeRedirect) {\n    requestHeaders = Object.assign({\n      // The Host header was set by nativeProtocol.request\n      Host: response.req.getHeader(\"host\"),\n    }, this._options.headers);\n  }\n\n  // RFC7231§6.4: Automatic redirection needs to done with\n  // care for methods not known to be safe, […]\n  // RFC7231§6.4.2–3: For historical reasons, a user agent MAY change\n  // the request method from POST to GET for the subsequent request.\n  var method = this._options.method;\n  if ((statusCode === 301 || statusCode === 302) && this._options.method === \"POST\" ||\n      // RFC7231§6.4.4: The 303 (See Other) status code indicates that\n      // the server is redirecting the user agent to a different resource […]\n      // A user agent can perform a retrieval request targeting that URI\n      // (a GET or HEAD request if using HTTP) […]\n      (statusCode === 303) && !/^(?:GET|HEAD)$/.test(this._options.method)) {\n    this._options.method = \"GET\";\n    // Drop a possible entity and headers related to it\n    this._requestBodyBuffers = [];\n    removeMatchingHeaders(/^content-/i, this._options.headers);\n  }\n\n  // Drop the Host header, as the redirect might lead to a different host\n  var currentHostHeader = removeMatchingHeaders(/^host$/i, this._options.headers);\n\n  // If the redirect is relative, carry over the host of the last request\n  var currentUrlParts = parseUrl(this._currentUrl);\n  var currentHost = currentHostHeader || currentUrlParts.host;\n  var currentUrl = /^\\w+:/.test(location) ? this._currentUrl :\n    url.format(Object.assign(currentUrlParts, { host: currentHost }));\n\n  // Create the redirected request\n  var redirectUrl = resolveUrl(location, currentUrl);\n  debug(\"redirecting to\", redirectUrl.href);\n  this._isRedirect = true;\n  spreadUrlObject(redirectUrl, this._options);\n\n  // Drop confidential headers when redirecting to a less secure protocol\n  // or to a different domain that is not a superdomain\n  if (redirectUrl.protocol !== currentUrlParts.protocol &&\n     redirectUrl.protocol !== \"https:\" ||\n     redirectUrl.host !== currentHost &&\n     !isSubdomain(redirectUrl.host, currentHost)) {\n    removeMatchingHeaders(/^(?:(?:proxy-)?authorization|cookie)$/i, this._options.headers);\n  }\n\n  // Evaluate the beforeRedirect callback\n  if (isFunction(beforeRedirect)) {\n    var responseDetails = {\n      headers: response.headers,\n      statusCode: statusCode,\n    };\n    var requestDetails = {\n      url: currentUrl,\n      method: method,\n      headers: requestHeaders,\n    };\n    beforeRedirect(this._options, responseDetails, requestDetails);\n    this._sanitizeOptions(this._options);\n  }\n\n  // Perform the redirected request\n  this._performRequest();\n};\n\n// Wraps the key/value object of protocols with redirect functionality\nfunction wrap(protocols) {\n  // Default settings\n  var exports = {\n    maxRedirects: 21,\n    maxBodyLength: 10 * 1024 * 1024,\n  };\n\n  // Wrap each protocol\n  var nativeProtocols = {};\n  Object.keys(protocols).forEach(function (scheme) {\n    var protocol = scheme + \":\";\n    var nativeProtocol = nativeProtocols[protocol] = protocols[scheme];\n    var wrappedProtocol = exports[scheme] = Object.create(nativeProtocol);\n\n    // Executes a request, following redirects\n    function request(input, options, callback) {\n      // Parse parameters, ensuring that input is an object\n      if (isURL(input)) {\n        input = spreadUrlObject(input);\n      }\n      else if (isString(input)) {\n        input = spreadUrlObject(parseUrl(input));\n      }\n      else {\n        callback = options;\n        options = validateUrl(input);\n        input = { protocol: protocol };\n      }\n      if (isFunction(options)) {\n        callback = options;\n        options = null;\n      }\n\n      // Set defaults\n      options = Object.assign({\n        maxRedirects: exports.maxRedirects,\n        maxBodyLength: exports.maxBodyLength,\n      }, input, options);\n      options.nativeProtocols = nativeProtocols;\n      if (!isString(options.host) && !isString(options.hostname)) {\n        options.hostname = \"::1\";\n      }\n\n      assert.equal(options.protocol, protocol, \"protocol mismatch\");\n      debug(\"options\", options);\n      return new RedirectableRequest(options, callback);\n    }\n\n    // Executes a GET request, following redirects\n    function get(input, options, callback) {\n      var wrappedRequest = wrappedProtocol.request(input, options, callback);\n      wrappedRequest.end();\n      return wrappedRequest;\n    }\n\n    // Expose the properties on the wrapped protocol\n    Object.defineProperties(wrappedProtocol, {\n      request: { value: request, configurable: true, enumerable: true, writable: true },\n      get: { value: get, configurable: true, enumerable: true, writable: true },\n    });\n  });\n  return exports;\n}\n\nfunction noop() { /* empty */ }\n\nfunction parseUrl(input) {\n  var parsed;\n  // istanbul ignore else\n  if (useNativeURL) {\n    parsed = new URL(input);\n  }\n  else {\n    // Ensure the URL is valid and absolute\n    parsed = validateUrl(url.parse(input));\n    if (!isString(parsed.protocol)) {\n      throw new InvalidUrlError({ input });\n    }\n  }\n  return parsed;\n}\n\nfunction resolveUrl(relative, base) {\n  // istanbul ignore next\n  return useNativeURL ? new URL(relative, base) : parseUrl(url.resolve(base, relative));\n}\n\nfunction validateUrl(input) {\n  if (/^\\[/.test(input.hostname) && !/^\\[[:0-9a-f]+\\]$/i.test(input.hostname)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  if (/^\\[/.test(input.host) && !/^\\[[:0-9a-f]+\\](:\\d+)?$/i.test(input.host)) {\n    throw new InvalidUrlError({ input: input.href || input });\n  }\n  return input;\n}\n\nfunction spreadUrlObject(urlObject, target) {\n  var spread = target || {};\n  for (var key of preservedUrlFields) {\n    spread[key] = urlObject[key];\n  }\n\n  // Fix IPv6 hostname\n  if (spread.hostname.startsWith(\"[\")) {\n    spread.hostname = spread.hostname.slice(1, -1);\n  }\n  // Ensure port is a number\n  if (spread.port !== \"\") {\n    spread.port = Number(spread.port);\n  }\n  // Concatenate path\n  spread.path = spread.search ? spread.pathname + spread.search : spread.pathname;\n\n  return spread;\n}\n\nfunction removeMatchingHeaders(regex, headers) {\n  var lastValue;\n  for (var header in headers) {\n    if (regex.test(header)) {\n      lastValue = headers[header];\n      delete headers[header];\n    }\n  }\n  return (lastValue === null || typeof lastValue === \"undefined\") ?\n    undefined : String(lastValue).trim();\n}\n\nfunction createErrorType(code, message, baseClass) {\n  // Create constructor\n  function CustomError(properties) {\n    // istanbul ignore else\n    if (isFunction(Error.captureStackTrace)) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n    Object.assign(this, properties || {});\n    this.code = code;\n    this.message = this.cause ? message + \": \" + this.cause.message : message;\n  }\n\n  // Attach constructor and set default properties\n  CustomError.prototype = new (baseClass || Error)();\n  Object.defineProperties(CustomError.prototype, {\n    constructor: {\n      value: CustomError,\n      enumerable: false,\n    },\n    name: {\n      value: \"Error [\" + code + \"]\",\n      enumerable: false,\n    },\n  });\n  return CustomError;\n}\n\nfunction destroyRequest(request, error) {\n  for (var event of events) {\n    request.removeListener(event, eventHandlers[event]);\n  }\n  request.on(\"error\", noop);\n  request.destroy(error);\n}\n\nfunction isSubdomain(subdomain, domain) {\n  assert(isString(subdomain) && isString(domain));\n  var dot = subdomain.length - domain.length - 1;\n  return dot > 0 && subdomain[dot] === \".\" && subdomain.endsWith(domain);\n}\n\nfunction isString(value) {\n  return typeof value === \"string\" || value instanceof String;\n}\n\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\nfunction isBuffer(value) {\n  return typeof value === \"object\" && (\"length\" in value);\n}\n\nfunction isURL(value) {\n  return URL && value instanceof URL;\n}\n\n// Exports\nmodule.exports = wrap({ http: http, https: https });\nmodule.exports.wrap = wrap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/follow-redirects/index.js\n");

/***/ })

};
;