"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/integrations/[id]/test/route";
exports.ids = ["app/api/integrations/[id]/test/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/integrations/[id]/test/route.ts */ \"(rsc)/./src/app/api/integrations/[id]/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/integrations/[id]/test/route\",\n        pathname: \"/api/integrations/[id]/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/integrations/[id]/test/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\integrations\\\\[id]\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/integrations/[id]/test/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/integrations/[id]/test/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/integrations/[id]/test/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_integrations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/integrations */ \"(rsc)/./src/lib/integrations/index.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\nasync function POST(request, { params }) {\n    try {\n        // Validate API key\n        const apiKey = request.headers.get(\"x-api-key\");\n        const project = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.validateApiKey)(apiKey);\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid API key\"\n            }, {\n                status: 401\n            });\n        }\n        // Get integration\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.integration.findFirst({\n            where: {\n                id: params.id,\n                projectId: project.id\n            }\n        });\n        if (!integration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Integration not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Test the integration\n        const result = await _lib_integrations__WEBPACK_IMPORTED_MODULE_2__.IntegrationManager.testIntegration({\n            type: integration.type,\n            config: JSON.parse(integration.config)\n        });\n        // Update integration status based on test result\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.integration.update({\n            where: {\n                id: integration.id\n            },\n            data: {\n                lastTested: new Date(),\n                isActive: result.success\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: result.success,\n            message: result.message,\n            data: result.data,\n            provider: result.provider,\n            testedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Integration test error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/integrations/[id]/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\nasync function validateApiKey(apiKey) {\n    try {\n        if (!apiKey || !apiKey.startsWith(\"sfd_\")) {\n            return null;\n        }\n        // Find the API key and get the associated user's projects\n        const apiKeyRecord = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.findFirst({\n            where: {\n                key: apiKey,\n                isActive: true\n            },\n            include: {\n                user: {\n                    include: {\n                        projects: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!apiKeyRecord || !apiKeyRecord.user.projects.length) {\n            return null;\n        }\n        // Update last used timestamp\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.update({\n            where: {\n                id: apiKeyRecord.id\n            },\n            data: {\n                lastUsed: new Date()\n            }\n        });\n        // Return the first active project (in a real app, you might want to specify which project)\n        return apiKeyRecord.user.projects[0];\n    } catch (error) {\n        console.error(\"API key validation error:\", error);\n        return null;\n    }\n}\nfunction generateApiKey() {\n    return \"sfd_\" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/hubspot.ts":
/*!*****************************************!*\
  !*** ./src/lib/integrations/hubspot.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HubSpotIntegration: () => (/* binding */ HubSpotIntegration)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nclass HubSpotIntegration {\n    constructor(config){\n        this.baseUrl = \"https://api.hubapi.com\";\n        this.config = config;\n    }\n    getAuthHeaders() {\n        if (this.config.accessToken) {\n            return {\n                \"Authorization\": `Bearer ${this.config.accessToken}`,\n                \"Content-Type\": \"application/json\"\n            };\n        } else if (this.config.apiKey) {\n            return {\n                \"Content-Type\": \"application/json\"\n            };\n        }\n        throw new Error(\"No valid authentication method provided\");\n    }\n    getApiUrl(endpoint) {\n        if (this.config.apiKey) {\n            const separator = endpoint.includes(\"?\") ? \"&\" : \"?\";\n            return `${this.baseUrl}${endpoint}${separator}hapikey=${this.config.apiKey}`;\n        }\n        return `${this.baseUrl}${endpoint}`;\n    }\n    async testConnection() {\n        try {\n            const url = this.getApiUrl(\"/contacts/v1/lists/all/contacts/all\");\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers,\n                params: {\n                    count: 1\n                }\n            });\n            return {\n                success: true,\n                message: \"HubSpot connection successful\",\n                data: {\n                    portalId: response.data?.[\"portal-id\"] || \"unknown\",\n                    hasContacts: response.data?.contacts?.length > 0\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `HubSpot connection failed: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createContact(contactData) {\n        try {\n            // Prepare contact properties for HubSpot API\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl(\"/contacts/v1/contact\");\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: \"Contact created successfully in HubSpot\",\n                data: {\n                    vid: response.data.vid,\n                    portalId: response.data[\"portal-id\"],\n                    isNew: response.data[\"is-new\"]\n                }\n            };\n        } catch (error) {\n            // Handle duplicate contact error\n            if (error.response?.status === 409) {\n                return await this.updateExistingContact(contactData, error.response.data);\n            }\n            return {\n                success: false,\n                message: `Failed to create HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async updateExistingContact(contactData, errorData) {\n        try {\n            // Extract existing contact ID from error response\n            const existingContactId = errorData.identityProfile?.vid;\n            if (!existingContactId) {\n                return {\n                    success: false,\n                    message: \"Contact already exists but could not retrieve contact ID\"\n                };\n            }\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl(`/contacts/v1/contact/vid/${existingContactId}/profile`);\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: existingContactId.toString(),\n                message: \"Existing contact updated successfully in HubSpot\",\n                data: {\n                    vid: existingContactId,\n                    isNew: false\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to update existing HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    formatContactProperties(contactData) {\n        const properties = [];\n        // Required fields\n        if (contactData.email) {\n            properties.push({\n                property: \"email\",\n                value: contactData.email\n            });\n        }\n        // Optional fields\n        if (contactData.firstname) {\n            properties.push({\n                property: \"firstname\",\n                value: contactData.firstname\n            });\n        }\n        if (contactData.lastname) {\n            properties.push({\n                property: \"lastname\",\n                value: contactData.lastname\n            });\n        }\n        if (contactData.company) {\n            properties.push({\n                property: \"company\",\n                value: contactData.company\n            });\n        }\n        if (contactData.phone) {\n            properties.push({\n                property: \"phone\",\n                value: contactData.phone\n            });\n        }\n        if (contactData.website) {\n            properties.push({\n                property: \"website\",\n                value: contactData.website\n            });\n        }\n        if (contactData.message) {\n            properties.push({\n                property: \"message\",\n                value: contactData.message\n            });\n        }\n        // Lead tracking\n        properties.push({\n            property: \"lead_source\",\n            value: contactData.lead_source || \"SmartForm Defender\"\n        });\n        properties.push({\n            property: \"hs_lead_status\",\n            value: contactData.hs_lead_status || \"NEW\"\n        });\n        // Add timestamp\n        properties.push({\n            property: \"createdate\",\n            value: new Date().getTime().toString()\n        });\n        return properties;\n    }\n    async getContact(email) {\n        try {\n            const url = this.getApiUrl(`/contacts/v1/contact/email/${encodeURIComponent(email)}/profile`);\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: \"Contact retrieved successfully\",\n                data: response.data\n            };\n        } catch (error) {\n            if (error.response?.status === 404) {\n                return {\n                    success: false,\n                    message: \"Contact not found in HubSpot\"\n                };\n            }\n            return {\n                success: false,\n                message: `Failed to retrieve HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createDeal(contactId, dealData) {\n        try {\n            const url = this.getApiUrl(\"/deals/v1/deal\");\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: [\n                    {\n                        name: \"dealname\",\n                        value: dealData.dealName || \"SmartForm Lead\"\n                    },\n                    {\n                        name: \"dealstage\",\n                        value: dealData.dealStage || \"appointmentscheduled\"\n                    },\n                    {\n                        name: \"pipeline\",\n                        value: dealData.pipeline || \"default\"\n                    },\n                    {\n                        name: \"amount\",\n                        value: dealData.amount || \"0\"\n                    },\n                    {\n                        name: \"closedate\",\n                        value: dealData.closeDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime()\n                    },\n                    {\n                        name: \"source\",\n                        value: \"SmartForm Defender\"\n                    }\n                ],\n                associations: {\n                    associatedVids: [\n                        parseInt(contactId)\n                    ]\n                }\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                message: \"Deal created successfully in HubSpot\",\n                data: {\n                    dealId: response.data.dealId,\n                    portalId: response.data.portalId\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to create HubSpot deal: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/hubspot.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/index.ts":
/*!***************************************!*\
  !*** ./src/lib/integrations/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationManager: () => (/* binding */ IntegrationManager)\n/* harmony export */ });\n/* harmony import */ var _hubspot__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hubspot */ \"(rsc)/./src/lib/integrations/hubspot.ts\");\n\nclass IntegrationManager {\n    static async sendToIntegration(integration, leadData) {\n        try {\n            switch(integration.type){\n                case \"HUBSPOT\":\n                    return await this.sendToHubSpot(integration.config, leadData);\n                case \"SALESFORCE\":\n                    return await this.sendToSalesforce(integration.config, leadData);\n                case \"MAILCHIMP\":\n                    return await this.sendToMailchimp(integration.config, leadData);\n                case \"ZAPIER\":\n                    return await this.sendToZapier(integration.config, leadData);\n                case \"WEBHOOK\":\n                    return await this.sendToWebhook(integration.config, leadData);\n                default:\n                    return {\n                        success: false,\n                        message: `Unsupported integration type: ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Integration error: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n    static async sendToHubSpot(config, leadData) {\n        const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(config);\n        // Parse name into first and last name if needed\n        const { firstName, lastName } = this.parseName(leadData.name, leadData.firstName, leadData.lastName);\n        const contactData = {\n            email: leadData.email,\n            firstname: firstName,\n            lastname: lastName,\n            company: leadData.company,\n            phone: leadData.phone,\n            website: leadData.website,\n            message: leadData.message,\n            lead_source: leadData.source || \"SmartForm Defender\",\n            hs_lead_status: \"NEW\"\n        };\n        const result = await hubspot.createContact(contactData);\n        return {\n            success: result.success,\n            message: result.message,\n            contactId: result.contactId,\n            data: result.data,\n            provider: \"HUBSPOT\"\n        };\n    }\n    static async sendToSalesforce(config, leadData) {\n        // Placeholder for Salesforce integration\n        return {\n            success: false,\n            message: \"Salesforce integration not implemented yet\",\n            provider: \"SALESFORCE\"\n        };\n    }\n    static async sendToMailchimp(config, leadData) {\n        // Placeholder for Mailchimp integration\n        return {\n            success: false,\n            message: \"Mailchimp integration not implemented yet\",\n            provider: \"MAILCHIMP\"\n        };\n    }\n    static async sendToZapier(config, leadData) {\n        try {\n            const response = await fetch(config.webhookUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: \"SmartForm Defender\"\n                })\n            });\n            if (response.ok) {\n                return {\n                    success: true,\n                    message: \"Lead sent to Zapier successfully\",\n                    provider: \"ZAPIER\"\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Zapier webhook failed: ${response.statusText}`,\n                    provider: \"ZAPIER\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Zapier webhook error: ${error.message}`,\n                provider: \"ZAPIER\"\n            };\n        }\n    }\n    static async sendToWebhook(config, leadData) {\n        try {\n            const headers = {\n                \"Content-Type\": \"application/json\",\n                ...config.headers\n            };\n            const response = await fetch(config.url, {\n                method: config.method || \"POST\",\n                headers,\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: \"SmartForm Defender\"\n                })\n            });\n            if (response.ok) {\n                const responseData = await response.text();\n                return {\n                    success: true,\n                    message: \"Lead sent to webhook successfully\",\n                    data: responseData,\n                    provider: \"WEBHOOK\"\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Webhook failed: ${response.statusText}`,\n                    provider: \"WEBHOOK\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Webhook error: ${error.message}`,\n                provider: \"WEBHOOK\"\n            };\n        }\n    }\n    static parseName(fullName, firstName, lastName) {\n        if (firstName && lastName) {\n            return {\n                firstName,\n                lastName\n            };\n        }\n        if (fullName) {\n            const parts = fullName.trim().split(\" \");\n            return {\n                firstName: parts[0] || \"\",\n                lastName: parts.slice(1).join(\" \") || \"\"\n            };\n        }\n        return {\n            firstName: firstName || \"\",\n            lastName: lastName || \"\"\n        };\n    }\n    static async testIntegration(integration) {\n        try {\n            switch(integration.type){\n                case \"HUBSPOT\":\n                    const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(integration.config);\n                    const result = await hubspot.testConnection();\n                    return {\n                        success: result.success,\n                        message: result.message,\n                        data: result.data,\n                        provider: \"HUBSPOT\"\n                    };\n                default:\n                    return {\n                        success: false,\n                        message: `Test not implemented for ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Test failed: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2ludGVncmF0aW9ucy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUErRjtBQTZCeEYsTUFBTUM7SUFDWCxhQUFhQyxrQkFDWEMsV0FBOEIsRUFDOUJDLFFBQWtCLEVBQ1k7UUFDOUIsSUFBSTtZQUNGLE9BQVFELFlBQVlFLElBQUk7Z0JBQ3RCLEtBQUs7b0JBQ0gsT0FBTyxNQUFNLElBQUksQ0FBQ0MsYUFBYSxDQUFDSCxZQUFZSSxNQUFNLEVBQUVIO2dCQUV0RCxLQUFLO29CQUNILE9BQU8sTUFBTSxJQUFJLENBQUNJLGdCQUFnQixDQUFDTCxZQUFZSSxNQUFNLEVBQUVIO2dCQUV6RCxLQUFLO29CQUNILE9BQU8sTUFBTSxJQUFJLENBQUNLLGVBQWUsQ0FBQ04sWUFBWUksTUFBTSxFQUFFSDtnQkFFeEQsS0FBSztvQkFDSCxPQUFPLE1BQU0sSUFBSSxDQUFDTSxZQUFZLENBQUNQLFlBQVlJLE1BQU0sRUFBRUg7Z0JBRXJELEtBQUs7b0JBQ0gsT0FBTyxNQUFNLElBQUksQ0FBQ08sYUFBYSxDQUFDUixZQUFZSSxNQUFNLEVBQUVIO2dCQUV0RDtvQkFDRSxPQUFPO3dCQUNMUSxTQUFTO3dCQUNUQyxTQUFTLENBQUMsOEJBQThCLEVBQUVWLFlBQVlFLElBQUksQ0FBQyxDQUFDO3dCQUM1RFMsVUFBVVgsWUFBWUUsSUFBSTtvQkFDNUI7WUFDSjtRQUNGLEVBQUUsT0FBT1UsT0FBWTtZQUNuQixPQUFPO2dCQUNMSCxTQUFTO2dCQUNUQyxTQUFTLENBQUMsbUJBQW1CLEVBQUVFLE1BQU1GLE9BQU8sQ0FBQyxDQUFDO2dCQUM5Q0MsVUFBVVgsWUFBWUUsSUFBSTtZQUM1QjtRQUNGO0lBQ0Y7SUFFQSxhQUFxQkMsY0FDbkJDLE1BQXFCLEVBQ3JCSCxRQUFrQixFQUNZO1FBQzlCLE1BQU1ZLFVBQVUsSUFBSWhCLHdEQUFrQkEsQ0FBQ087UUFFdkMsZ0RBQWdEO1FBQ2hELE1BQU0sRUFBRVUsU0FBUyxFQUFFQyxRQUFRLEVBQUUsR0FBRyxJQUFJLENBQUNDLFNBQVMsQ0FBQ2YsU0FBU2dCLElBQUksRUFBRWhCLFNBQVNhLFNBQVMsRUFBRWIsU0FBU2MsUUFBUTtRQUVuRyxNQUFNRyxjQUE4QjtZQUNsQ0MsT0FBT2xCLFNBQVNrQixLQUFLO1lBQ3JCQyxXQUFXTjtZQUNYTyxVQUFVTjtZQUNWTyxTQUFTckIsU0FBU3FCLE9BQU87WUFDekJDLE9BQU90QixTQUFTc0IsS0FBSztZQUNyQkMsU0FBU3ZCLFNBQVN1QixPQUFPO1lBQ3pCZCxTQUFTVCxTQUFTUyxPQUFPO1lBQ3pCZSxhQUFheEIsU0FBU3lCLE1BQU0sSUFBSTtZQUNoQ0MsZ0JBQWdCO1FBQ2xCO1FBRUEsTUFBTUMsU0FBUyxNQUFNZixRQUFRZ0IsYUFBYSxDQUFDWDtRQUUzQyxPQUFPO1lBQ0xULFNBQVNtQixPQUFPbkIsT0FBTztZQUN2QkMsU0FBU2tCLE9BQU9sQixPQUFPO1lBQ3ZCb0IsV0FBV0YsT0FBT0UsU0FBUztZQUMzQkMsTUFBTUgsT0FBT0csSUFBSTtZQUNqQnBCLFVBQVU7UUFDWjtJQUNGO0lBRUEsYUFBcUJOLGlCQUNuQkQsTUFBVyxFQUNYSCxRQUFrQixFQUNZO1FBQzlCLHlDQUF5QztRQUN6QyxPQUFPO1lBQ0xRLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxVQUFVO1FBQ1o7SUFDRjtJQUVBLGFBQXFCTCxnQkFDbkJGLE1BQVcsRUFDWEgsUUFBa0IsRUFDWTtRQUM5Qix3Q0FBd0M7UUFDeEMsT0FBTztZQUNMUSxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsVUFBVTtRQUNaO0lBQ0Y7SUFFQSxhQUFxQkosYUFDbkJILE1BQVcsRUFDWEgsUUFBa0IsRUFDWTtRQUM5QixJQUFJO1lBQ0YsTUFBTStCLFdBQVcsTUFBTUMsTUFBTTdCLE9BQU84QixVQUFVLEVBQUU7Z0JBQzlDQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkIsR0FBR3RDLFFBQVE7b0JBQ1h1QyxXQUFXLElBQUlDLE9BQU9DLFdBQVc7b0JBQ2pDaEIsUUFBUTtnQkFDVjtZQUNGO1lBRUEsSUFBSU0sU0FBU1csRUFBRSxFQUFFO2dCQUNmLE9BQU87b0JBQ0xsQyxTQUFTO29CQUNUQyxTQUFTO29CQUNUQyxVQUFVO2dCQUNaO1lBQ0YsT0FBTztnQkFDTCxPQUFPO29CQUNMRixTQUFTO29CQUNUQyxTQUFTLENBQUMsdUJBQXVCLEVBQUVzQixTQUFTWSxVQUFVLENBQUMsQ0FBQztvQkFDeERqQyxVQUFVO2dCQUNaO1lBQ0Y7UUFDRixFQUFFLE9BQU9DLE9BQVk7WUFDbkIsT0FBTztnQkFDTEgsU0FBUztnQkFDVEMsU0FBUyxDQUFDLHNCQUFzQixFQUFFRSxNQUFNRixPQUFPLENBQUMsQ0FBQztnQkFDakRDLFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFFQSxhQUFxQkgsY0FDbkJKLE1BQVcsRUFDWEgsUUFBa0IsRUFDWTtRQUM5QixJQUFJO1lBQ0YsTUFBTW1DLFVBQWtDO2dCQUN0QyxnQkFBZ0I7Z0JBQ2hCLEdBQUdoQyxPQUFPZ0MsT0FBTztZQUNuQjtZQUVBLE1BQU1KLFdBQVcsTUFBTUMsTUFBTTdCLE9BQU95QyxHQUFHLEVBQUU7Z0JBQ3ZDVixRQUFRL0IsT0FBTytCLE1BQU0sSUFBSTtnQkFDekJDO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CLEdBQUd0QyxRQUFRO29CQUNYdUMsV0FBVyxJQUFJQyxPQUFPQyxXQUFXO29CQUNqQ2hCLFFBQVE7Z0JBQ1Y7WUFDRjtZQUVBLElBQUlNLFNBQVNXLEVBQUUsRUFBRTtnQkFDZixNQUFNRyxlQUFlLE1BQU1kLFNBQVNlLElBQUk7Z0JBQ3hDLE9BQU87b0JBQ0x0QyxTQUFTO29CQUNUQyxTQUFTO29CQUNUcUIsTUFBTWU7b0JBQ05uQyxVQUFVO2dCQUNaO1lBQ0YsT0FBTztnQkFDTCxPQUFPO29CQUNMRixTQUFTO29CQUNUQyxTQUFTLENBQUMsZ0JBQWdCLEVBQUVzQixTQUFTWSxVQUFVLENBQUMsQ0FBQztvQkFDakRqQyxVQUFVO2dCQUNaO1lBQ0Y7UUFDRixFQUFFLE9BQU9DLE9BQVk7WUFDbkIsT0FBTztnQkFDTEgsU0FBUztnQkFDVEMsU0FBUyxDQUFDLGVBQWUsRUFBRUUsTUFBTUYsT0FBTyxDQUFDLENBQUM7Z0JBQzFDQyxVQUFVO1lBQ1o7UUFDRjtJQUNGO0lBRUEsT0FBZUssVUFBVWdDLFFBQWlCLEVBQUVsQyxTQUFrQixFQUFFQyxRQUFpQixFQUFFO1FBQ2pGLElBQUlELGFBQWFDLFVBQVU7WUFDekIsT0FBTztnQkFBRUQ7Z0JBQVdDO1lBQVM7UUFDL0I7UUFFQSxJQUFJaUMsVUFBVTtZQUNaLE1BQU1DLFFBQVFELFNBQVNFLElBQUksR0FBR0MsS0FBSyxDQUFDO1lBQ3BDLE9BQU87Z0JBQ0xyQyxXQUFXbUMsS0FBSyxDQUFDLEVBQUUsSUFBSTtnQkFDdkJsQyxVQUFVa0MsTUFBTUcsS0FBSyxDQUFDLEdBQUdDLElBQUksQ0FBQyxRQUFRO1lBQ3hDO1FBQ0Y7UUFFQSxPQUFPO1lBQ0x2QyxXQUFXQSxhQUFhO1lBQ3hCQyxVQUFVQSxZQUFZO1FBQ3hCO0lBQ0Y7SUFFQSxhQUFhdUMsZ0JBQWdCdEQsV0FBOEIsRUFBZ0M7UUFDekYsSUFBSTtZQUNGLE9BQVFBLFlBQVlFLElBQUk7Z0JBQ3RCLEtBQUs7b0JBQ0gsTUFBTVcsVUFBVSxJQUFJaEIsd0RBQWtCQSxDQUFDRyxZQUFZSSxNQUFNO29CQUN6RCxNQUFNd0IsU0FBUyxNQUFNZixRQUFRMEMsY0FBYztvQkFDM0MsT0FBTzt3QkFDTDlDLFNBQVNtQixPQUFPbkIsT0FBTzt3QkFDdkJDLFNBQVNrQixPQUFPbEIsT0FBTzt3QkFDdkJxQixNQUFNSCxPQUFPRyxJQUFJO3dCQUNqQnBCLFVBQVU7b0JBQ1o7Z0JBRUY7b0JBQ0UsT0FBTzt3QkFDTEYsU0FBUzt3QkFDVEMsU0FBUyxDQUFDLHlCQUF5QixFQUFFVixZQUFZRSxJQUFJLENBQUMsQ0FBQzt3QkFDdkRTLFVBQVVYLFlBQVlFLElBQUk7b0JBQzVCO1lBQ0o7UUFDRixFQUFFLE9BQU9VLE9BQVk7WUFDbkIsT0FBTztnQkFDTEgsU0FBUztnQkFDVEMsU0FBUyxDQUFDLGFBQWEsRUFBRUUsTUFBTUYsT0FBTyxDQUFDLENBQUM7Z0JBQ3hDQyxVQUFVWCxZQUFZRSxJQUFJO1lBQzVCO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc21hcnRmb3JtLWRlZmVuZGVyLy4vc3JjL2xpYi9pbnRlZ3JhdGlvbnMvaW5kZXgudHM/ZWJjYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdWJTcG90SW50ZWdyYXRpb24sIEh1YlNwb3RDb25maWcsIEh1YlNwb3RDb250YWN0LCBIdWJTcG90UmVzcG9uc2UgfSBmcm9tICcuL2h1YnNwb3QnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEludGVncmF0aW9uQ29uZmlnIHtcbiAgdHlwZTogc3RyaW5nO1xuICBjb25maWc6IGFueTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBMZWFkRGF0YSB7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIG5hbWU/OiBzdHJpbmc7XG4gIGZpcnN0TmFtZT86IHN0cmluZztcbiAgbGFzdE5hbWU/OiBzdHJpbmc7XG4gIGNvbXBhbnk/OiBzdHJpbmc7XG4gIHBob25lPzogc3RyaW5nO1xuICBtZXNzYWdlPzogc3RyaW5nO1xuICB3ZWJzaXRlPzogc3RyaW5nO1xuICBzb3VyY2U/OiBzdHJpbmc7XG4gIGZvcm1EYXRhPzogUmVjb3JkPHN0cmluZywgYW55Pjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBJbnRlZ3JhdGlvblJlc3BvbnNlIHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBjb250YWN0SWQ/OiBzdHJpbmc7XG4gIGRlYWxJZD86IHN0cmluZztcbiAgZGF0YT86IGFueTtcbiAgcHJvdmlkZXI6IHN0cmluZztcbn1cblxuZXhwb3J0IGNsYXNzIEludGVncmF0aW9uTWFuYWdlciB7XG4gIHN0YXRpYyBhc3luYyBzZW5kVG9JbnRlZ3JhdGlvbihcbiAgICBpbnRlZ3JhdGlvbjogSW50ZWdyYXRpb25Db25maWcsXG4gICAgbGVhZERhdGE6IExlYWREYXRhXG4gICk6IFByb21pc2U8SW50ZWdyYXRpb25SZXNwb25zZT4ge1xuICAgIHRyeSB7XG4gICAgICBzd2l0Y2ggKGludGVncmF0aW9uLnR5cGUpIHtcbiAgICAgICAgY2FzZSAnSFVCU1BPVCc6XG4gICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuc2VuZFRvSHViU3BvdChpbnRlZ3JhdGlvbi5jb25maWcsIGxlYWREYXRhKTtcbiAgICAgICAgXG4gICAgICAgIGNhc2UgJ1NBTEVTRk9SQ0UnOlxuICAgICAgICAgIHJldHVybiBhd2FpdCB0aGlzLnNlbmRUb1NhbGVzZm9yY2UoaW50ZWdyYXRpb24uY29uZmlnLCBsZWFkRGF0YSk7XG4gICAgICAgIFxuICAgICAgICBjYXNlICdNQUlMQ0hJTVAnOlxuICAgICAgICAgIHJldHVybiBhd2FpdCB0aGlzLnNlbmRUb01haWxjaGltcChpbnRlZ3JhdGlvbi5jb25maWcsIGxlYWREYXRhKTtcbiAgICAgICAgXG4gICAgICAgIGNhc2UgJ1pBUElFUic6XG4gICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuc2VuZFRvWmFwaWVyKGludGVncmF0aW9uLmNvbmZpZywgbGVhZERhdGEpO1xuICAgICAgICBcbiAgICAgICAgY2FzZSAnV0VCSE9PSyc6XG4gICAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuc2VuZFRvV2ViaG9vayhpbnRlZ3JhdGlvbi5jb25maWcsIGxlYWREYXRhKTtcbiAgICAgICAgXG4gICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgICAgbWVzc2FnZTogYFVuc3VwcG9ydGVkIGludGVncmF0aW9uIHR5cGU6ICR7aW50ZWdyYXRpb24udHlwZX1gLFxuICAgICAgICAgICAgcHJvdmlkZXI6IGludGVncmF0aW9uLnR5cGVcbiAgICAgICAgICB9O1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBgSW50ZWdyYXRpb24gZXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gLFxuICAgICAgICBwcm92aWRlcjogaW50ZWdyYXRpb24udHlwZVxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHN0YXRpYyBhc3luYyBzZW5kVG9IdWJTcG90KFxuICAgIGNvbmZpZzogSHViU3BvdENvbmZpZyxcbiAgICBsZWFkRGF0YTogTGVhZERhdGFcbiAgKTogUHJvbWlzZTxJbnRlZ3JhdGlvblJlc3BvbnNlPiB7XG4gICAgY29uc3QgaHVic3BvdCA9IG5ldyBIdWJTcG90SW50ZWdyYXRpb24oY29uZmlnKTtcblxuICAgIC8vIFBhcnNlIG5hbWUgaW50byBmaXJzdCBhbmQgbGFzdCBuYW1lIGlmIG5lZWRlZFxuICAgIGNvbnN0IHsgZmlyc3ROYW1lLCBsYXN0TmFtZSB9ID0gdGhpcy5wYXJzZU5hbWUobGVhZERhdGEubmFtZSwgbGVhZERhdGEuZmlyc3ROYW1lLCBsZWFkRGF0YS5sYXN0TmFtZSk7XG5cbiAgICBjb25zdCBjb250YWN0RGF0YTogSHViU3BvdENvbnRhY3QgPSB7XG4gICAgICBlbWFpbDogbGVhZERhdGEuZW1haWwsXG4gICAgICBmaXJzdG5hbWU6IGZpcnN0TmFtZSxcbiAgICAgIGxhc3RuYW1lOiBsYXN0TmFtZSxcbiAgICAgIGNvbXBhbnk6IGxlYWREYXRhLmNvbXBhbnksXG4gICAgICBwaG9uZTogbGVhZERhdGEucGhvbmUsXG4gICAgICB3ZWJzaXRlOiBsZWFkRGF0YS53ZWJzaXRlLFxuICAgICAgbWVzc2FnZTogbGVhZERhdGEubWVzc2FnZSxcbiAgICAgIGxlYWRfc291cmNlOiBsZWFkRGF0YS5zb3VyY2UgfHwgJ1NtYXJ0Rm9ybSBEZWZlbmRlcicsXG4gICAgICBoc19sZWFkX3N0YXR1czogJ05FVydcbiAgICB9O1xuXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgaHVic3BvdC5jcmVhdGVDb250YWN0KGNvbnRhY3REYXRhKTtcblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiByZXN1bHQuc3VjY2VzcyxcbiAgICAgIG1lc3NhZ2U6IHJlc3VsdC5tZXNzYWdlLFxuICAgICAgY29udGFjdElkOiByZXN1bHQuY29udGFjdElkLFxuICAgICAgZGF0YTogcmVzdWx0LmRhdGEsXG4gICAgICBwcm92aWRlcjogJ0hVQlNQT1QnXG4gICAgfTtcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGFzeW5jIHNlbmRUb1NhbGVzZm9yY2UoXG4gICAgY29uZmlnOiBhbnksXG4gICAgbGVhZERhdGE6IExlYWREYXRhXG4gICk6IFByb21pc2U8SW50ZWdyYXRpb25SZXNwb25zZT4ge1xuICAgIC8vIFBsYWNlaG9sZGVyIGZvciBTYWxlc2ZvcmNlIGludGVncmF0aW9uXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogJ1NhbGVzZm9yY2UgaW50ZWdyYXRpb24gbm90IGltcGxlbWVudGVkIHlldCcsXG4gICAgICBwcm92aWRlcjogJ1NBTEVTRk9SQ0UnXG4gICAgfTtcbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGFzeW5jIHNlbmRUb01haWxjaGltcChcbiAgICBjb25maWc6IGFueSxcbiAgICBsZWFkRGF0YTogTGVhZERhdGFcbiAgKTogUHJvbWlzZTxJbnRlZ3JhdGlvblJlc3BvbnNlPiB7XG4gICAgLy8gUGxhY2Vob2xkZXIgZm9yIE1haWxjaGltcCBpbnRlZ3JhdGlvblxuICAgIHJldHVybiB7XG4gICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgIG1lc3NhZ2U6ICdNYWlsY2hpbXAgaW50ZWdyYXRpb24gbm90IGltcGxlbWVudGVkIHlldCcsXG4gICAgICBwcm92aWRlcjogJ01BSUxDSElNUCdcbiAgICB9O1xuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgYXN5bmMgc2VuZFRvWmFwaWVyKFxuICAgIGNvbmZpZzogYW55LFxuICAgIGxlYWREYXRhOiBMZWFkRGF0YVxuICApOiBQcm9taXNlPEludGVncmF0aW9uUmVzcG9uc2U+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChjb25maWcud2ViaG9va1VybCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAuLi5sZWFkRGF0YSxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICBzb3VyY2U6ICdTbWFydEZvcm0gRGVmZW5kZXInXG4gICAgICAgIH0pXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICBtZXNzYWdlOiAnTGVhZCBzZW50IHRvIFphcGllciBzdWNjZXNzZnVsbHknLFxuICAgICAgICAgIHByb3ZpZGVyOiAnWkFQSUVSJ1xuICAgICAgICB9O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBtZXNzYWdlOiBgWmFwaWVyIHdlYmhvb2sgZmFpbGVkOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCxcbiAgICAgICAgICBwcm92aWRlcjogJ1pBUElFUidcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogYFphcGllciB3ZWJob29rIGVycm9yOiAke2Vycm9yLm1lc3NhZ2V9YCxcbiAgICAgICAgcHJvdmlkZXI6ICdaQVBJRVInXG4gICAgICB9O1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGFzeW5jIHNlbmRUb1dlYmhvb2soXG4gICAgY29uZmlnOiBhbnksXG4gICAgbGVhZERhdGE6IExlYWREYXRhXG4gICk6IFByb21pc2U8SW50ZWdyYXRpb25SZXNwb25zZT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAuLi5jb25maWcuaGVhZGVyc1xuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChjb25maWcudXJsLCB7XG4gICAgICAgIG1ldGhvZDogY29uZmlnLm1ldGhvZCB8fCAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnMsXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAuLi5sZWFkRGF0YSxcbiAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICBzb3VyY2U6ICdTbWFydEZvcm0gRGVmZW5kZXInXG4gICAgICAgIH0pXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlRGF0YSA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIG1lc3NhZ2U6ICdMZWFkIHNlbnQgdG8gd2ViaG9vayBzdWNjZXNzZnVsbHknLFxuICAgICAgICAgIGRhdGE6IHJlc3BvbnNlRGF0YSxcbiAgICAgICAgICBwcm92aWRlcjogJ1dFQkhPT0snXG4gICAgICAgIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICAgIG1lc3NhZ2U6IGBXZWJob29rIGZhaWxlZDogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWAsXG4gICAgICAgICAgcHJvdmlkZXI6ICdXRUJIT09LJ1xuICAgICAgICB9O1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiBgV2ViaG9vayBlcnJvcjogJHtlcnJvci5tZXNzYWdlfWAsXG4gICAgICAgIHByb3ZpZGVyOiAnV0VCSE9PSydcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgcGFyc2VOYW1lKGZ1bGxOYW1lPzogc3RyaW5nLCBmaXJzdE5hbWU/OiBzdHJpbmcsIGxhc3ROYW1lPzogc3RyaW5nKSB7XG4gICAgaWYgKGZpcnN0TmFtZSAmJiBsYXN0TmFtZSkge1xuICAgICAgcmV0dXJuIHsgZmlyc3ROYW1lLCBsYXN0TmFtZSB9O1xuICAgIH1cblxuICAgIGlmIChmdWxsTmFtZSkge1xuICAgICAgY29uc3QgcGFydHMgPSBmdWxsTmFtZS50cmltKCkuc3BsaXQoJyAnKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGZpcnN0TmFtZTogcGFydHNbMF0gfHwgJycsXG4gICAgICAgIGxhc3ROYW1lOiBwYXJ0cy5zbGljZSgxKS5qb2luKCcgJykgfHwgJydcbiAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIGZpcnN0TmFtZTogZmlyc3ROYW1lIHx8ICcnLFxuICAgICAgbGFzdE5hbWU6IGxhc3ROYW1lIHx8ICcnXG4gICAgfTtcbiAgfVxuXG4gIHN0YXRpYyBhc3luYyB0ZXN0SW50ZWdyYXRpb24oaW50ZWdyYXRpb246IEludGVncmF0aW9uQ29uZmlnKTogUHJvbWlzZTxJbnRlZ3JhdGlvblJlc3BvbnNlPiB7XG4gICAgdHJ5IHtcbiAgICAgIHN3aXRjaCAoaW50ZWdyYXRpb24udHlwZSkge1xuICAgICAgICBjYXNlICdIVUJTUE9UJzpcbiAgICAgICAgICBjb25zdCBodWJzcG90ID0gbmV3IEh1YlNwb3RJbnRlZ3JhdGlvbihpbnRlZ3JhdGlvbi5jb25maWcpO1xuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGh1YnNwb3QudGVzdENvbm5lY3Rpb24oKTtcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgc3VjY2VzczogcmVzdWx0LnN1Y2Nlc3MsXG4gICAgICAgICAgICBtZXNzYWdlOiByZXN1bHQubWVzc2FnZSxcbiAgICAgICAgICAgIGRhdGE6IHJlc3VsdC5kYXRhLFxuICAgICAgICAgICAgcHJvdmlkZXI6ICdIVUJTUE9UJ1xuICAgICAgICAgIH07XG4gICAgICAgIFxuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICAgIG1lc3NhZ2U6IGBUZXN0IG5vdCBpbXBsZW1lbnRlZCBmb3IgJHtpbnRlZ3JhdGlvbi50eXBlfWAsXG4gICAgICAgICAgICBwcm92aWRlcjogaW50ZWdyYXRpb24udHlwZVxuICAgICAgICAgIH07XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6IGBUZXN0IGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWAsXG4gICAgICAgIHByb3ZpZGVyOiBpbnRlZ3JhdGlvbi50eXBlXG4gICAgICB9O1xuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbIkh1YlNwb3RJbnRlZ3JhdGlvbiIsIkludGVncmF0aW9uTWFuYWdlciIsInNlbmRUb0ludGVncmF0aW9uIiwiaW50ZWdyYXRpb24iLCJsZWFkRGF0YSIsInR5cGUiLCJzZW5kVG9IdWJTcG90IiwiY29uZmlnIiwic2VuZFRvU2FsZXNmb3JjZSIsInNlbmRUb01haWxjaGltcCIsInNlbmRUb1phcGllciIsInNlbmRUb1dlYmhvb2siLCJzdWNjZXNzIiwibWVzc2FnZSIsInByb3ZpZGVyIiwiZXJyb3IiLCJodWJzcG90IiwiZmlyc3ROYW1lIiwibGFzdE5hbWUiLCJwYXJzZU5hbWUiLCJuYW1lIiwiY29udGFjdERhdGEiLCJlbWFpbCIsImZpcnN0bmFtZSIsImxhc3RuYW1lIiwiY29tcGFueSIsInBob25lIiwid2Vic2l0ZSIsImxlYWRfc291cmNlIiwic291cmNlIiwiaHNfbGVhZF9zdGF0dXMiLCJyZXN1bHQiLCJjcmVhdGVDb250YWN0IiwiY29udGFjdElkIiwiZGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJ3ZWJob29rVXJsIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwidGltZXN0YW1wIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwib2siLCJzdGF0dXNUZXh0IiwidXJsIiwicmVzcG9uc2VEYXRhIiwidGV4dCIsImZ1bGxOYW1lIiwicGFydHMiLCJ0cmltIiwic3BsaXQiLCJzbGljZSIsImpvaW4iLCJ0ZXN0SW50ZWdyYXRpb24iLCJ0ZXN0Q29ubmVjdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydGZvcm0tZGVmZW5kZXIvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();