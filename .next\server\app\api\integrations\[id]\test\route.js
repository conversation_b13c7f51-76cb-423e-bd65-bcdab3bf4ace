/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/integrations/[id]/test/route";
exports.ids = ["app/api/integrations/[id]/test/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/integrations/[id]/test/route.ts */ \"(rsc)/./src/app/api/integrations/[id]/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/integrations/[id]/test/route\",\n        pathname: \"/api/integrations/[id]/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/integrations/[id]/test/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\integrations\\\\[id]\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZpbnRlZ3JhdGlvbnMlMkYlNUJpZCU1RCUyRnRlc3QlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRmludGVncmF0aW9ucyUyRiU1QmlkJTVEJTJGdGVzdCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRmludGVncmF0aW9ucyUyRiU1QmlkJTVEJTJGdGVzdCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNqZXNzZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNTbWFydEZvcm1EZWZlbmRlciU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1VzZXJzJTVDamVzc2UlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDU21hcnRGb3JtRGVmZW5kZXImaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQ21FO0FBQ2hKO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxVc2Vyc1xcXFxqZXNzZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxTbWFydEZvcm1EZWZlbmRlclxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxpbnRlZ3JhdGlvbnNcXFxcW2lkXVxcXFx0ZXN0XFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9pbnRlZ3JhdGlvbnMvW2lkXS90ZXN0L3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvaW50ZWdyYXRpb25zL1tpZF0vdGVzdFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvaW50ZWdyYXRpb25zL1tpZF0vdGVzdC9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkM6XFxcXFVzZXJzXFxcXGplc3NlXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXFNtYXJ0Rm9ybURlZmVuZGVyXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXGludGVncmF0aW9uc1xcXFxbaWRdXFxcXHRlc3RcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_3__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma),\n    providers: [\n        // Only include Google provider if credentials are properly configured\n        ...process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET && process.env.GOOGLE_CLIENT_ID !== 'your-google-client-id' ? [\n            (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                clientId: process.env.GOOGLE_CLIENT_ID,\n                clientSecret: process.env.GOOGLE_CLIENT_SECRET\n            })\n        ] : [],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_5___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    image: user.image\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup',\n        error: '/auth/error'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                try {\n                    // Get user role from database\n                    const dbUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            id: user.id\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    token.role = dbUser?.role || 'USER';\n                } catch (error) {\n                    console.error('Error fetching user role:', error);\n                    token.role = 'USER';\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    secret: \"development-secret-key-change-in-production\"\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/integrations/[id]/test/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/integrations/[id]/test/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_integrations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/integrations */ \"(rsc)/./src/lib/integrations/index.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n\n\n\nasync function POST(request, { params }) {\n    try {\n        const { id } = await params;\n        // Validate session authentication\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.email) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        // Get user\n        const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n            where: {\n                email: session.user.email\n            }\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        // Get integration and verify ownership\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.integration.findFirst({\n            where: {\n                id: id,\n                userId: user.id\n            }\n        });\n        if (!integration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Integration not found'\n            }, {\n                status: 404\n            });\n        }\n        // Test the integration\n        const result = await _lib_integrations__WEBPACK_IMPORTED_MODULE_3__.IntegrationManager.testIntegration({\n            type: integration.type,\n            config: JSON.parse(integration.config)\n        });\n        // Update integration status based on test result\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.integration.update({\n            where: {\n                id: integration.id\n            },\n            data: {\n                lastTested: new Date(),\n                isActive: result.success\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: result.success,\n            message: result.message,\n            data: result.data,\n            provider: result.provider,\n            testedAt: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error('Integration test error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/integrations/[id]/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/hubspot.ts":
/*!*****************************************!*\
  !*** ./src/lib/integrations/hubspot.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HubSpotIntegration: () => (/* binding */ HubSpotIntegration)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nclass HubSpotIntegration {\n    constructor(config){\n        this.baseUrl = 'https://api.hubapi.com';\n        this.config = config;\n    }\n    getAuthHeaders() {\n        if (this.config.accessToken) {\n            return {\n                'Authorization': `Bearer ${this.config.accessToken}`,\n                'Content-Type': 'application/json'\n            };\n        } else if (this.config.apiKey) {\n            return {\n                'Content-Type': 'application/json'\n            };\n        }\n        throw new Error('No valid authentication method provided');\n    }\n    getApiUrl(endpoint) {\n        if (this.config.apiKey) {\n            const separator = endpoint.includes('?') ? '&' : '?';\n            return `${this.baseUrl}${endpoint}${separator}hapikey=${this.config.apiKey}`;\n        }\n        return `${this.baseUrl}${endpoint}`;\n    }\n    async testConnection() {\n        try {\n            const url = this.getApiUrl('/contacts/v1/lists/all/contacts/all');\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers,\n                params: {\n                    count: 1\n                }\n            });\n            return {\n                success: true,\n                message: 'HubSpot connection successful',\n                data: {\n                    portalId: response.data?.['portal-id'] || 'unknown',\n                    hasContacts: response.data?.contacts?.length > 0\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `HubSpot connection failed: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createContact(contactData) {\n        try {\n            // Prepare contact properties for HubSpot API\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl('/contacts/v1/contact');\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: 'Contact created successfully in HubSpot',\n                data: {\n                    vid: response.data.vid,\n                    portalId: response.data['portal-id'],\n                    isNew: response.data['is-new']\n                }\n            };\n        } catch (error) {\n            // Handle duplicate contact error\n            if (error.response?.status === 409) {\n                return await this.updateExistingContact(contactData, error.response.data);\n            }\n            return {\n                success: false,\n                message: `Failed to create HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async updateExistingContact(contactData, errorData) {\n        try {\n            // Extract existing contact ID from error response\n            const existingContactId = errorData.identityProfile?.vid;\n            if (!existingContactId) {\n                return {\n                    success: false,\n                    message: 'Contact already exists but could not retrieve contact ID'\n                };\n            }\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl(`/contacts/v1/contact/vid/${existingContactId}/profile`);\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: existingContactId.toString(),\n                message: 'Existing contact updated successfully in HubSpot',\n                data: {\n                    vid: existingContactId,\n                    isNew: false\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to update existing HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    formatContactProperties(contactData) {\n        const properties = [];\n        // Required fields\n        if (contactData.email) {\n            properties.push({\n                property: 'email',\n                value: contactData.email\n            });\n        }\n        // Optional fields\n        if (contactData.firstname) {\n            properties.push({\n                property: 'firstname',\n                value: contactData.firstname\n            });\n        }\n        if (contactData.lastname) {\n            properties.push({\n                property: 'lastname',\n                value: contactData.lastname\n            });\n        }\n        if (contactData.company) {\n            properties.push({\n                property: 'company',\n                value: contactData.company\n            });\n        }\n        if (contactData.phone) {\n            properties.push({\n                property: 'phone',\n                value: contactData.phone\n            });\n        }\n        if (contactData.website) {\n            properties.push({\n                property: 'website',\n                value: contactData.website\n            });\n        }\n        if (contactData.message) {\n            properties.push({\n                property: 'message',\n                value: contactData.message\n            });\n        }\n        // Lead tracking\n        properties.push({\n            property: 'lead_source',\n            value: contactData.lead_source || 'SmartForm Defender'\n        });\n        properties.push({\n            property: 'hs_lead_status',\n            value: contactData.hs_lead_status || 'NEW'\n        });\n        // Add timestamp\n        properties.push({\n            property: 'createdate',\n            value: new Date().getTime().toString()\n        });\n        return properties;\n    }\n    async getContact(email) {\n        try {\n            const url = this.getApiUrl(`/contacts/v1/contact/email/${encodeURIComponent(email)}/profile`);\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: 'Contact retrieved successfully',\n                data: response.data\n            };\n        } catch (error) {\n            if (error.response?.status === 404) {\n                return {\n                    success: false,\n                    message: 'Contact not found in HubSpot'\n                };\n            }\n            return {\n                success: false,\n                message: `Failed to retrieve HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createDeal(contactId, dealData) {\n        try {\n            const url = this.getApiUrl('/deals/v1/deal');\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: [\n                    {\n                        name: 'dealname',\n                        value: dealData.dealName || 'SmartForm Lead'\n                    },\n                    {\n                        name: 'dealstage',\n                        value: dealData.dealStage || 'appointmentscheduled'\n                    },\n                    {\n                        name: 'pipeline',\n                        value: dealData.pipeline || 'default'\n                    },\n                    {\n                        name: 'amount',\n                        value: dealData.amount || '0'\n                    },\n                    {\n                        name: 'closedate',\n                        value: dealData.closeDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime()\n                    },\n                    {\n                        name: 'source',\n                        value: 'SmartForm Defender'\n                    }\n                ],\n                associations: {\n                    associatedVids: [\n                        parseInt(contactId)\n                    ]\n                }\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                message: 'Deal created successfully in HubSpot',\n                data: {\n                    dealId: response.data.dealId,\n                    portalId: response.data.portalId\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to create HubSpot deal: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/hubspot.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/index.ts":
/*!***************************************!*\
  !*** ./src/lib/integrations/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationManager: () => (/* binding */ IntegrationManager)\n/* harmony export */ });\n/* harmony import */ var _hubspot__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hubspot */ \"(rsc)/./src/lib/integrations/hubspot.ts\");\n\nclass IntegrationManager {\n    static async sendToIntegration(integration, leadData) {\n        try {\n            switch(integration.type){\n                case 'HUBSPOT':\n                    return await this.sendToHubSpot(integration.config, leadData);\n                case 'SALESFORCE':\n                    return await this.sendToSalesforce(integration.config, leadData);\n                case 'MAILCHIMP':\n                    return await this.sendToMailchimp(integration.config, leadData);\n                case 'ZAPIER':\n                    return await this.sendToZapier(integration.config, leadData);\n                case 'WEBHOOK':\n                    return await this.sendToWebhook(integration.config, leadData);\n                default:\n                    return {\n                        success: false,\n                        message: `Unsupported integration type: ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Integration error: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n    static async sendToHubSpot(config, leadData) {\n        const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(config);\n        // Parse name into first and last name if needed\n        const { firstName, lastName } = this.parseName(leadData.name, leadData.firstName, leadData.lastName);\n        const contactData = {\n            email: leadData.email,\n            firstname: firstName,\n            lastname: lastName,\n            company: leadData.company,\n            phone: leadData.phone,\n            website: leadData.website,\n            message: leadData.message,\n            lead_source: leadData.source || 'SmartForm Defender',\n            hs_lead_status: 'NEW'\n        };\n        const result = await hubspot.createContact(contactData);\n        return {\n            success: result.success,\n            message: result.message,\n            contactId: result.contactId,\n            data: result.data,\n            provider: 'HUBSPOT'\n        };\n    }\n    static async sendToSalesforce(config, leadData) {\n        // Placeholder for Salesforce integration\n        return {\n            success: false,\n            message: 'Salesforce integration not implemented yet',\n            provider: 'SALESFORCE'\n        };\n    }\n    static async sendToMailchimp(config, leadData) {\n        // Placeholder for Mailchimp integration\n        return {\n            success: false,\n            message: 'Mailchimp integration not implemented yet',\n            provider: 'MAILCHIMP'\n        };\n    }\n    static async sendToZapier(config, leadData) {\n        try {\n            const response = await fetch(config.webhookUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: 'SmartForm Defender'\n                })\n            });\n            if (response.ok) {\n                return {\n                    success: true,\n                    message: 'Lead sent to Zapier successfully',\n                    provider: 'ZAPIER'\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Zapier webhook failed: ${response.statusText}`,\n                    provider: 'ZAPIER'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Zapier webhook error: ${error.message}`,\n                provider: 'ZAPIER'\n            };\n        }\n    }\n    static async sendToWebhook(config, leadData) {\n        try {\n            const headers = {\n                'Content-Type': 'application/json',\n                ...config.headers\n            };\n            const response = await fetch(config.url, {\n                method: config.method || 'POST',\n                headers,\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: 'SmartForm Defender'\n                })\n            });\n            if (response.ok) {\n                const responseData = await response.text();\n                return {\n                    success: true,\n                    message: 'Lead sent to webhook successfully',\n                    data: responseData,\n                    provider: 'WEBHOOK'\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Webhook failed: ${response.statusText}`,\n                    provider: 'WEBHOOK'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Webhook error: ${error.message}`,\n                provider: 'WEBHOOK'\n            };\n        }\n    }\n    static parseName(fullName, firstName, lastName) {\n        if (firstName && lastName) {\n            return {\n                firstName,\n                lastName\n            };\n        }\n        if (fullName) {\n            const parts = fullName.trim().split(' ');\n            return {\n                firstName: parts[0] || '',\n                lastName: parts.slice(1).join(' ') || ''\n            };\n        }\n        return {\n            firstName: firstName || '',\n            lastName: lastName || ''\n        };\n    }\n    static async testIntegration(integration) {\n        try {\n            switch(integration.type){\n                case 'HUBSPOT':\n                    const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(integration.config);\n                    const result = await hubspot.testConnection();\n                    return {\n                        success: result.success,\n                        message: result.message,\n                        data: result.data,\n                        provider: 'HUBSPOT'\n                    };\n                default:\n                    return {\n                        success: false,\n                        message: `Test not implemented for ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Test failed: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();