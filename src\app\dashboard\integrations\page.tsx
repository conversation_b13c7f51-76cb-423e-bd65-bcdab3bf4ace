'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

export default function IntegrationsPage() {
  const [integrations, setIntegrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');

  useEffect(() => {
    if (projectId) {
      loadIntegrations();
    }
  }, [projectId]);

  const loadIntegrations = async () => {
    try {
      const response = await fetch(`/api/integrations?projectId=${projectId}`);
      const data = await response.json();
      setIntegrations(data.data || []);
    } catch (error) {
      console.error('Failed to load integrations:', error);
    } finally {
      setLoading(false);
    }
  };

  const testIntegration = async (integrationId: string) => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}/test`, {
        method: 'POST'
      });

      const result = await response.json();
      alert(`Test Result: ${result.message}`);
      loadIntegrations(); // Refresh data
    } catch (error) {
      alert('Test failed: ' + error.message);
    }
  };

  const toggleIntegration = async (integrationId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/integrations/${integrationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ isActive: !currentStatus })
      });

      if (response.ok) {
        loadIntegrations(); // Refresh the list
      }
    } catch (error) {
      console.error('Failed to update integration status:', error);
    }
  };

  const deleteIntegration = async (id: string, name: string) => {
    if (!confirm(`Are you sure you want to delete the "${name}" integration? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/integrations/${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        loadIntegrations(); // Refresh the list
      } else {
        alert('Failed to delete integration');
      }
    } catch (error) {
      console.error('Failed to delete integration:', error);
      alert('Failed to delete integration');
    }
  };

  const getIntegrationIcon = (type: string) => {
    switch (type) {
      case 'HUBSPOT':
        return '🧡';
      case 'SALESFORCE':
        return '☁️';
      case 'MAILCHIMP':
        return '🐵';
      case 'ZAPIER':
        return '⚡';
      case 'WEBHOOK':
        return '🔗';
      default:
        return '🔌';
    }
  };

  if (!projectId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">No Project Selected</h1>
          <p className="text-gray-600 mb-4">Please select a project to manage integrations.</p>
          <Link href="/dashboard/projects" className="text-blue-600 hover:text-blue-500">
            ← Back to Projects
          </Link>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-500 text-sm">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mt-2">Integrations</h1>
              <p className="text-gray-600">Manage your CRM and automation integrations</p>
            </div>
            <Link
              href={`/dashboard/integrations/new?projectId=${projectId}`}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Add Integration
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Integrations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {integrations.map((integration: any) => (
            <div key={integration.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">{getIntegrationIcon(integration.type)}</span>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{integration.name}</h3>
                      <span className="text-sm text-gray-500">{integration.type}</span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-2 ${
                      integration.isActive ? 'bg-green-500' : 'bg-gray-400'
                    }`}></div>
                    <span className="text-sm text-gray-600">
                      {integration.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Last Tested:</span>
                    <span className="text-gray-900">
                      {integration.lastTested 
                        ? new Date(integration.lastTested).toLocaleDateString()
                        : 'Never'
                      }
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Status:</span>
                    <span className={`${
                      integration.status === 'CONNECTED' ? 'text-green-600' :
                      integration.status === 'ERROR' ? 'text-red-600' :
                      'text-yellow-600'
                    }`}>
                      {integration.status || 'Unknown'}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Created:</span>
                    <span className="text-gray-900">
                      {new Date(integration.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                {/* Configuration Preview */}
                <div className="bg-gray-50 rounded p-3 mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Configuration</h4>
                  <div className="text-xs text-gray-600 space-y-1">
                    {integration.type === 'HUBSPOT' && (
                      <>
                        <div>Portal ID: {integration.config.portalId || 'Not set'}</div>
                        <div>Auth Type: {integration.config.accessToken ? 'OAuth' : 'API Key'}</div>
                      </>
                    )}
                    {integration.type === 'WEBHOOK' && (
                      <>
                        <div>URL: {integration.config.url || 'Not set'}</div>
                        <div>Method: {integration.config.method || 'POST'}</div>
                      </>
                    )}
                    {integration.type === 'ZAPIER' && (
                      <div>Webhook URL: {integration.config.webhookUrl ? 'Configured' : 'Not set'}</div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="grid grid-cols-2 gap-2">
                  <button
                    onClick={() => testIntegration(integration.id)}
                    className="px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                  >
                    Test
                  </button>
                  <Link
                    href={`/dashboard/integrations/${integration.id}/edit`}
                    className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-center"
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => toggleIntegration(integration.id, integration.isActive)}
                    className={`px-3 py-2 text-sm rounded ${
                      integration.isActive
                        ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {integration.isActive ? 'Disable' : 'Enable'}
                  </button>
                  <button
                    onClick={() => deleteIntegration(integration.id, integration.name)}
                    className="px-3 py-2 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {integrations.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No integrations</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by adding your first integration.</p>
            <div className="mt-6">
              <Link
                href={`/dashboard/integrations/new?projectId=${projectId}`}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add Integration
              </Link>
            </div>
          </div>
        )}

        {/* Available Integrations */}
        <div className="mt-12">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Available Integrations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { type: 'HUBSPOT', name: 'HubSpot', icon: '🧡', description: 'CRM and marketing automation' },
              { type: 'SALESFORCE', name: 'Salesforce', icon: '☁️', description: 'World\'s #1 CRM platform' },
              { type: 'MAILCHIMP', name: 'Mailchimp', icon: '🐵', description: 'Email marketing platform' },
              { type: 'ZAPIER', name: 'Zapier', icon: '⚡', description: 'Connect 5000+ apps' },
              { type: 'WEBHOOK', name: 'Custom Webhook', icon: '🔗', description: 'Send data to any endpoint' }
            ].map((provider) => (
              <div key={provider.type} className="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow">
                <div className="text-center">
                  <span className="text-3xl mb-2 block">{provider.icon}</span>
                  <h3 className="font-medium text-gray-900">{provider.name}</h3>
                  <p className="text-sm text-gray-500 mt-1">{provider.description}</p>
                  <Link
                    href={`/dashboard/integrations/new?type=${provider.type}&projectId=${projectId}`}
                    className="mt-3 inline-block px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Add {provider.name}
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Help Section */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Integration Help</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-1">Setup Process</h4>
              <p>Each integration requires specific credentials and configuration. Follow our setup guides for each platform.</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Testing</h4>
              <p>Always test your integrations after setup to ensure leads are being sent correctly to your CRM.</p>
            </div>
            <div>
              <h4 className="font-medium mb-1">Troubleshooting</h4>
              <p>Check the integration status and test results if leads aren't appearing in your CRM system.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
