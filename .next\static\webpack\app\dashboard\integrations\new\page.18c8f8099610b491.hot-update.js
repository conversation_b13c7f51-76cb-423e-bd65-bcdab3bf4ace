"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/integrations/new/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/integrations/new/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/integrations/new/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewIntegrationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewIntegrationPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        name: '',\n        config: {}\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHubSpotInfo, setShowHubSpotInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const integrationTypes = [\n        {\n            value: 'HUBSPOT',\n            label: 'HubSpot CRM',\n            icon: '🧡'\n        },\n        {\n            value: 'SALESFORCE',\n            label: 'Salesforce',\n            icon: '☁️'\n        },\n        {\n            value: 'MAILCHIMP',\n            label: 'Mailchimp',\n            icon: '🐵'\n        },\n        {\n            value: 'ZAPIER',\n            label: 'Zapier',\n            icon: '⚡'\n        },\n        {\n            value: 'WEBHOOK',\n            label: 'Custom Webhook',\n            icon: '🔗'\n        }\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const response = await fetch('/api/integrations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'X-API-Key': 'sfd_demo_key_12345'\n                },\n                body: JSON.stringify({\n                    projectId: 'demo-project-id',\n                    ...formData\n                })\n            });\n            if (response.ok) {\n                router.push('/dashboard/integrations');\n            } else {\n                const error = await response.json();\n                alert(\"Failed to create integration: \".concat(error.error));\n            }\n        } catch (error) {\n            alert('Failed to create integration');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const renderConfigForm = ()=>{\n        switch(formData.type){\n            case 'HUBSPOT':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Access Token\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.accessToken || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    accessToken: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Your HubSpot access token\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Portal ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.portalId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    portalId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Your HubSpot portal ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this);\n            case 'SALESFORCE':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Client ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.clientId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    clientId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce client ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Client Secret\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.clientSecret || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    clientSecret: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce client secret\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Refresh Token\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.refreshToken || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    refreshToken: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce refresh token\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, this);\n            case 'MAILCHIMP':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"API Key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.apiKey || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    apiKey: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Mailchimp API key\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"List ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.listId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    listId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Mailchimp list ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, this);\n            case 'ZAPIER':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Webhook URL\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.config.webhookUrl || '',\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            config: {\n                                                ...prev.config,\n                                                webhookUrl: e.target.value\n                                            }\n                                        })),\n                                className: \"input-field\",\n                                placeholder: \"https://hooks.zapier.com/hooks/catch/...\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, this);\n            case 'WEBHOOK':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Webhook URL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formData.config.url || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    url: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"https://your-webhook-endpoint.com/webhook\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"HTTP Method\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.config.method || 'POST',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    method: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"POST\",\n                                            children: \"POST\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PUT\",\n                                            children: \"PUT\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PATCH\",\n                                            children: \"PATCH\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Authorization Header (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.authHeader || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    authHeader: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Bearer your-token or Basic base64-credentials\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-500\",\n                    children: \"Please select an integration type to configure\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Add New Integration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Connect SmartForm Defender to your favorite tools\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"Choose Integration Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: integrationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 \".concat(formData.type === type.value ? 'border-blue-500 bg-blue-50' : 'border-gray-300'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"type\",\n                                                    value: type.value,\n                                                    checked: formData.type === type.value,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                type: e.target.value,\n                                                                config: {}\n                                                            })),\n                                                    className: \"sr-only\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: type.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: type.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, type.value, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        formData.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"Integration Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Integration Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.name,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    })),\n                                            className: \"input-field\",\n                                            placeholder: \"e.g., Production HubSpot, Marketing Webhook\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                renderConfigForm()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this),\n                        formData.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>router.back(),\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                    children: loading ? 'Creating...' : 'Create Integration'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(NewIntegrationPage, \"G/I1Bp5x6ZM+ggsZwhhlgbv8C1s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewIntegrationPage;\nvar _c;\n$RefreshReg$(_c, \"NewIntegrationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ludGVncmF0aW9ucy9uZXcvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFaUM7QUFDVztBQUNlO0FBRTVDLFNBQVNHOztJQUN0QixNQUFNQyxTQUFTSCwwREFBU0E7SUFDeEIsTUFBTSxDQUFDSSxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFDO1FBQ3ZDTyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsUUFBUSxDQUFDO0lBQ1g7SUFDQSxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxpQkFBaUJDLG1CQUFtQixHQUFHYiwrQ0FBUUEsQ0FBQztJQUV2RCxNQUFNYyxtQkFBbUI7UUFDdkI7WUFBRUMsT0FBTztZQUFXQyxPQUFPO1lBQWVDLE1BQU07UUFBSztRQUNyRDtZQUFFRixPQUFPO1lBQWNDLE9BQU87WUFBY0MsTUFBTTtRQUFLO1FBQ3ZEO1lBQUVGLE9BQU87WUFBYUMsT0FBTztZQUFhQyxNQUFNO1FBQUs7UUFDckQ7WUFBRUYsT0FBTztZQUFVQyxPQUFPO1lBQVVDLE1BQU07UUFBSTtRQUM5QztZQUFFRixPQUFPO1lBQVdDLE9BQU87WUFBa0JDLE1BQU07UUFBSztLQUN6RDtJQUVELE1BQU1DLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFDaEJULFdBQVc7UUFFWCxJQUFJO1lBQ0YsTUFBTVUsV0FBVyxNQUFNQyxNQUFNLHFCQUFxQjtnQkFDaERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixhQUFhO2dCQUNmO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxXQUFXO29CQUNYLEdBQUd2QixRQUFRO2dCQUNiO1lBQ0Y7WUFFQSxJQUFJZ0IsU0FBU1EsRUFBRSxFQUFFO2dCQUNmekIsT0FBTzBCLElBQUksQ0FBQztZQUNkLE9BQU87Z0JBQ0wsTUFBTUMsUUFBUSxNQUFNVixTQUFTVyxJQUFJO2dCQUNqQ0MsTUFBTSxpQ0FBNkMsT0FBWkYsTUFBTUEsS0FBSztZQUNwRDtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkRSxNQUFNO1FBQ1IsU0FBVTtZQUNSdEIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNdUIsbUJBQW1CO1FBQ3ZCLE9BQVE3QixTQUFTRSxJQUFJO1lBQ25CLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUM0QjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ25CO29DQUFNb0IsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNDO29DQUNDOUIsTUFBSztvQ0FDTFEsT0FBT1YsU0FBU0ksTUFBTSxDQUFDNkIsV0FBVyxJQUFJO29DQUN0Q0MsVUFBVSxDQUFDcEIsSUFBTWIsWUFBWWtDLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1AvQixRQUFRO29EQUFFLEdBQUcrQixLQUFLL0IsTUFBTTtvREFBRTZCLGFBQWFuQixFQUFFc0IsTUFBTSxDQUFDMUIsS0FBSztnREFBQzs0Q0FDeEQ7b0NBQ0FxQixXQUFVO29DQUNWTSxhQUFZO29DQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7c0NBR1osOERBQUNSOzs4Q0FDQyw4REFBQ25CO29DQUFNb0IsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNDO29DQUNDOUIsTUFBSztvQ0FDTFEsT0FBT1YsU0FBU0ksTUFBTSxDQUFDbUMsUUFBUSxJQUFJO29DQUNuQ0wsVUFBVSxDQUFDcEIsSUFBTWIsWUFBWWtDLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1AvQixRQUFRO29EQUFFLEdBQUcrQixLQUFLL0IsTUFBTTtvREFBRW1DLFVBQVV6QixFQUFFc0IsTUFBTSxDQUFDMUIsS0FBSztnREFBQzs0Q0FDckQ7b0NBQ0FxQixXQUFVO29DQUNWTSxhQUFZO29DQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNbEIsS0FBSztnQkFDSCxxQkFDRSw4REFBQ1I7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDQztvQ0FDQzlCLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ29DLFFBQVEsSUFBSTtvQ0FDbkNOLFVBQVUsQ0FBQ3BCLElBQU1iLFlBQVlrQyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQL0IsUUFBUTtvREFBRSxHQUFHK0IsS0FBSy9CLE1BQU07b0RBQUVvQyxVQUFVMUIsRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7Z0RBQUM7NENBQ3JEO29DQUNBcUIsV0FBVTtvQ0FDVk0sYUFBWTtvQ0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUdaLDhEQUFDUjs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDQztvQ0FDQzlCLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ3FDLFlBQVksSUFBSTtvQ0FDdkNQLFVBQVUsQ0FBQ3BCLElBQU1iLFlBQVlrQyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQL0IsUUFBUTtvREFBRSxHQUFHK0IsS0FBSy9CLE1BQU07b0RBQUVxQyxjQUFjM0IsRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7Z0RBQUM7NENBQ3pEO29DQUNBcUIsV0FBVTtvQ0FDVk0sYUFBWTtvQ0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUdaLDhEQUFDUjs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDQztvQ0FDQzlCLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ3NDLFlBQVksSUFBSTtvQ0FDdkNSLFVBQVUsQ0FBQ3BCLElBQU1iLFlBQVlrQyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQL0IsUUFBUTtvREFBRSxHQUFHK0IsS0FBSy9CLE1BQU07b0RBQUVzQyxjQUFjNUIsRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7Z0RBQUM7NENBQ3pEO29DQUNBcUIsV0FBVTtvQ0FDVk0sYUFBWTtvQ0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTWxCLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNSO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDbkI7b0NBQU1vQixXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ0M7b0NBQ0M5QixNQUFLO29DQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUN1QyxNQUFNLElBQUk7b0NBQ2pDVCxVQUFVLENBQUNwQixJQUFNYixZQUFZa0MsQ0FBQUEsT0FBUztnREFDcEMsR0FBR0EsSUFBSTtnREFDUC9CLFFBQVE7b0RBQUUsR0FBRytCLEtBQUsvQixNQUFNO29EQUFFdUMsUUFBUTdCLEVBQUVzQixNQUFNLENBQUMxQixLQUFLO2dEQUFDOzRDQUNuRDtvQ0FDQXFCLFdBQVU7b0NBQ1ZNLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OztzQ0FHWiw4REFBQ1I7OzhDQUNDLDhEQUFDbkI7b0NBQU1vQixXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ0M7b0NBQ0M5QixNQUFLO29DQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUN3QyxNQUFNLElBQUk7b0NBQ2pDVixVQUFVLENBQUNwQixJQUFNYixZQUFZa0MsQ0FBQUEsT0FBUztnREFDcEMsR0FBR0EsSUFBSTtnREFDUC9CLFFBQVE7b0RBQUUsR0FBRytCLEtBQUsvQixNQUFNO29EQUFFd0MsUUFBUTlCLEVBQUVzQixNQUFNLENBQUMxQixLQUFLO2dEQUFDOzRDQUNuRDtvQ0FDQXFCLFdBQVU7b0NBQ1ZNLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1sQixLQUFLO2dCQUNILHFCQUNFLDhEQUFDUjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7OzBDQUNDLDhEQUFDbkI7Z0NBQU1vQixXQUFVOzBDQUErQzs7Ozs7OzBDQUdoRSw4REFBQ0M7Z0NBQ0M5QixNQUFLO2dDQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUN5QyxVQUFVLElBQUk7Z0NBQ3JDWCxVQUFVLENBQUNwQixJQUFNYixZQUFZa0MsQ0FBQUEsT0FBUzs0Q0FDcEMsR0FBR0EsSUFBSTs0Q0FDUC9CLFFBQVE7Z0RBQUUsR0FBRytCLEtBQUsvQixNQUFNO2dEQUFFeUMsWUFBWS9CLEVBQUVzQixNQUFNLENBQUMxQixLQUFLOzRDQUFDO3dDQUN2RDtnQ0FDQXFCLFdBQVU7Z0NBQ1ZNLGFBQVk7Z0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTWxCLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNSO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7OzhDQUNDLDhEQUFDbkI7b0NBQU1vQixXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ0M7b0NBQ0M5QixNQUFLO29DQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUMwQyxHQUFHLElBQUk7b0NBQzlCWixVQUFVLENBQUNwQixJQUFNYixZQUFZa0MsQ0FBQUEsT0FBUztnREFDcEMsR0FBR0EsSUFBSTtnREFDUC9CLFFBQVE7b0RBQUUsR0FBRytCLEtBQUsvQixNQUFNO29EQUFFMEMsS0FBS2hDLEVBQUVzQixNQUFNLENBQUMxQixLQUFLO2dEQUFDOzRDQUNoRDtvQ0FDQXFCLFdBQVU7b0NBQ1ZNLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OztzQ0FHWiw4REFBQ1I7OzhDQUNDLDhEQUFDbkI7b0NBQU1vQixXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ2dCO29DQUNDckMsT0FBT1YsU0FBU0ksTUFBTSxDQUFDYyxNQUFNLElBQUk7b0NBQ2pDZ0IsVUFBVSxDQUFDcEIsSUFBTWIsWUFBWWtDLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1AvQixRQUFRO29EQUFFLEdBQUcrQixLQUFLL0IsTUFBTTtvREFBRWMsUUFBUUosRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7Z0RBQUM7NENBQ25EO29DQUNBcUIsV0FBVTs7c0RBRVYsOERBQUNpQjs0Q0FBT3RDLE9BQU07c0RBQU87Ozs7OztzREFDckIsOERBQUNzQzs0Q0FBT3RDLE9BQU07c0RBQU07Ozs7OztzREFDcEIsOERBQUNzQzs0Q0FBT3RDLE9BQU07c0RBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHMUIsOERBQUNvQjs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDQztvQ0FDQzlCLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQzZDLFVBQVUsSUFBSTtvQ0FDckNmLFVBQVUsQ0FBQ3BCLElBQU1iLFlBQVlrQyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQL0IsUUFBUTtvREFBRSxHQUFHK0IsS0FBSy9CLE1BQU07b0RBQUU2QyxZQUFZbkMsRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7Z0RBQUM7NENBQ3ZEO29DQUNBcUIsV0FBVTtvQ0FDVk0sYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTXRCO2dCQUNFLHFCQUNFLDhEQUFDUDtvQkFBSUMsV0FBVTs4QkFBaUM7Ozs7OztRQUl0RDtJQUNGO0lBRUEscUJBQ0UsOERBQUNsQyxtRUFBZUE7a0JBQ2QsNEVBQUNpQztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDbUI7NEJBQUduQixXQUFVO3NDQUFtQzs7Ozs7O3NDQUNqRCw4REFBQ29COzRCQUFFcEIsV0FBVTtzQ0FBcUI7Ozs7Ozs7Ozs7Ozs4QkFHcEMsOERBQUNxQjtvQkFBS0MsVUFBVXhDO29CQUFja0IsV0FBVTs7c0NBRXRDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN1QjtvQ0FBR3ZCLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDWnRCLGlCQUFpQjhDLEdBQUcsQ0FBQyxDQUFDckQscUJBQ3JCLDhEQUFDUzs0Q0FFQ29CLFdBQVcsb0ZBSVYsT0FIQy9CLFNBQVNFLElBQUksS0FBS0EsS0FBS1EsS0FBSyxHQUN4QiwrQkFDQTs7OERBR04sOERBQUNzQjtvREFDQzlCLE1BQUs7b0RBQ0xDLE1BQUs7b0RBQ0xPLE9BQU9SLEtBQUtRLEtBQUs7b0RBQ2pCOEMsU0FBU3hELFNBQVNFLElBQUksS0FBS0EsS0FBS1EsS0FBSztvREFDckN3QixVQUFVLENBQUNwQixJQUFNYixZQUFZa0MsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFakMsTUFBTVksRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7Z0VBQUVOLFFBQVEsQ0FBQzs0REFBRTtvREFDbEYyQixXQUFVOzs7Ozs7OERBRVosOERBQUMwQjtvREFBSzFCLFdBQVU7OERBQWlCN0IsS0FBS1UsSUFBSTs7Ozs7OzhEQUMxQyw4REFBQzZDO29EQUFLMUIsV0FBVTs4REFBcUM3QixLQUFLUyxLQUFLOzs7Ozs7OzJDQWhCMURULEtBQUtRLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBdUJ0QlYsU0FBU0UsSUFBSSxrQkFDWiw4REFBQzRCOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3VCO29DQUFHdkIsV0FBVTs4Q0FBeUM7Ozs7Ozs4Q0FDdkQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BCOzRDQUFNb0IsV0FBVTtzREFBK0M7Ozs7OztzREFHaEUsOERBQUNDOzRDQUNDOUIsTUFBSzs0Q0FDTFEsT0FBT1YsU0FBU0csSUFBSTs0Q0FDcEIrQixVQUFVLENBQUNwQixJQUFNYixZQUFZa0MsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFaEMsTUFBTVcsRUFBRXNCLE1BQU0sQ0FBQzFCLEtBQUs7b0RBQUM7NENBQ3RFcUIsV0FBVTs0Q0FDVk0sYUFBWTs0Q0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7O2dDQUdYVDs7Ozs7Ozt3QkFLSjdCLFNBQVNFLElBQUksa0JBQ1osOERBQUM0Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMyQjtvQ0FDQ3hELE1BQUs7b0NBQ0x5RCxTQUFTLElBQU01RCxPQUFPNkQsSUFBSTtvQ0FDMUI3QixXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUMyQjtvQ0FDQ3hELE1BQUs7b0NBQ0wyRCxVQUFVeEQ7b0NBQ1YwQixXQUFVOzhDQUVUMUIsVUFBVSxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTNDO0dBMVZ3QlA7O1FBQ1BGLHNEQUFTQTs7O0tBREZFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGplc3NlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFNtYXJ0Rm9ybURlZmVuZGVyXFxzcmNcXGFwcFxcZGFzaGJvYXJkXFxpbnRlZ3JhdGlvbnNcXG5ld1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgRGFzaGJvYXJkTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9EYXNoYm9hcmRMYXlvdXQnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOZXdJbnRlZ3JhdGlvblBhZ2UoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICB0eXBlOiAnJyxcbiAgICBuYW1lOiAnJyxcbiAgICBjb25maWc6IHt9XG4gIH0pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93SHViU3BvdEluZm8sIHNldFNob3dIdWJTcG90SW5mb10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgaW50ZWdyYXRpb25UeXBlcyA9IFtcbiAgICB7IHZhbHVlOiAnSFVCU1BPVCcsIGxhYmVsOiAnSHViU3BvdCBDUk0nLCBpY29uOiAn8J+noScgfSxcbiAgICB7IHZhbHVlOiAnU0FMRVNGT1JDRScsIGxhYmVsOiAnU2FsZXNmb3JjZScsIGljb246ICfimIHvuI8nIH0sXG4gICAgeyB2YWx1ZTogJ01BSUxDSElNUCcsIGxhYmVsOiAnTWFpbGNoaW1wJywgaWNvbjogJ/CfkLUnIH0sXG4gICAgeyB2YWx1ZTogJ1pBUElFUicsIGxhYmVsOiAnWmFwaWVyJywgaWNvbjogJ+KaoScgfSxcbiAgICB7IHZhbHVlOiAnV0VCSE9PSycsIGxhYmVsOiAnQ3VzdG9tIFdlYmhvb2snLCBpY29uOiAn8J+UlycgfVxuICBdO1xuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2ludGVncmF0aW9ucycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICdYLUFQSS1LZXknOiAnc2ZkX2RlbW9fa2V5XzEyMzQ1J1xuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgcHJvamVjdElkOiAnZGVtby1wcm9qZWN0LWlkJyxcbiAgICAgICAgICAuLi5mb3JtRGF0YVxuICAgICAgICB9KVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZC9pbnRlZ3JhdGlvbnMnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBhbGVydChgRmFpbGVkIHRvIGNyZWF0ZSBpbnRlZ3JhdGlvbjogJHtlcnJvci5lcnJvcn1gKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgYWxlcnQoJ0ZhaWxlZCB0byBjcmVhdGUgaW50ZWdyYXRpb24nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbmRlckNvbmZpZ0Zvcm0gPSAoKSA9PiB7XG4gICAgc3dpdGNoIChmb3JtRGF0YS50eXBlKSB7XG4gICAgICBjYXNlICdIVUJTUE9UJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgQWNjZXNzIFRva2VuXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy5hY2Nlc3NUb2tlbiB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGFjY2Vzc1Rva2VuOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgSHViU3BvdCBhY2Nlc3MgdG9rZW5cIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIFBvcnRhbCBJRFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy5wb3J0YWxJZCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIHBvcnRhbElkOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgSHViU3BvdCBwb3J0YWwgSURcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcblxuICAgICAgY2FzZSAnU0FMRVNGT1JDRSc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIENsaWVudCBJRFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy5jbGllbnRJZCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGNsaWVudElkOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgU2FsZXNmb3JjZSBjbGllbnQgSURcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIENsaWVudCBTZWNyZXRcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmNsaWVudFNlY3JldCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGNsaWVudFNlY3JldDogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIFNhbGVzZm9yY2UgY2xpZW50IHNlY3JldFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgUmVmcmVzaCBUb2tlblxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb25maWcucmVmcmVzaFRva2VuIHx8ICcnfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgIGNvbmZpZzogeyAuLi5wcmV2LmNvbmZpZywgcmVmcmVzaFRva2VuOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgU2FsZXNmb3JjZSByZWZyZXNoIHRva2VuXCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG5cbiAgICAgIGNhc2UgJ01BSUxDSElNUCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIEFQSSBLZXlcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmFwaUtleSB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGFwaUtleTogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIE1haWxjaGltcCBBUEkga2V5XCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICBMaXN0IElEXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmxpc3RJZCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGxpc3RJZDogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIE1haWxjaGltcCBsaXN0IElEXCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG5cbiAgICAgIGNhc2UgJ1pBUElFUic6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIFdlYmhvb2sgVVJMXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb25maWcud2ViaG9va1VybCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIHdlYmhvb2tVcmw6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGRcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9ob29rcy56YXBpZXIuY29tL2hvb2tzL2NhdGNoLy4uLlwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuXG4gICAgICBjYXNlICdXRUJIT09LJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgV2ViaG9vayBVUkxcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInVybFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy51cmwgfHwgJyd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgY29uZmlnOiB7IC4uLnByZXYuY29uZmlnLCB1cmw6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGRcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly95b3VyLXdlYmhvb2stZW5kcG9pbnQuY29tL3dlYmhvb2tcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIEhUVFAgTWV0aG9kXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLm1ldGhvZCB8fCAnUE9TVCd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgY29uZmlnOiB7IC4uLnByZXYuY29uZmlnLCBtZXRob2Q6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBPU1RcIj5QT1NUPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBVVFwiPlBVVDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQQVRDSFwiPlBBVENIPC9vcHRpb24+XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uIEhlYWRlciAoT3B0aW9uYWwpXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmF1dGhIZWFkZXIgfHwgJyd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgY29uZmlnOiB7IC4uLnByZXYuY29uZmlnLCBhdXRoSGVhZGVyOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJlYXJlciB5b3VyLXRva2VuIG9yIEJhc2ljIGJhc2U2NC1jcmVkZW50aWFsc1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcblxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgUGxlYXNlIHNlbGVjdCBhbiBpbnRlZ3JhdGlvbiB0eXBlIHRvIGNvbmZpZ3VyZVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxEYXNoYm9hcmRMYXlvdXQ+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+QWRkIE5ldyBJbnRlZ3JhdGlvbjwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0yXCI+Q29ubmVjdCBTbWFydEZvcm0gRGVmZW5kZXIgdG8geW91ciBmYXZvcml0ZSB0b29sczwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgey8qIEludGVncmF0aW9uIFR5cGUgU2VsZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5DaG9vc2UgSW50ZWdyYXRpb24gVHlwZTwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge2ludGVncmF0aW9uVHlwZXMubWFwKCh0eXBlKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgICBrZXk9e3R5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBmbGV4IGl0ZW1zLWNlbnRlciBwLTQgYm9yZGVyIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS01MCAke1xuICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS50eXBlID09PSB0eXBlLnZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIGJnLWJsdWUtNTAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJ0eXBlXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3R5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLnR5cGUgPT09IHR5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCB0eXBlOiBlLnRhcmdldC52YWx1ZSwgY29uZmlnOiB7fSB9KSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHlcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1yLTNcIj57dHlwZS5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPnt0eXBlLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEludGVncmF0aW9uIE5hbWUgKi99XG4gICAgICAgICAge2Zvcm1EYXRhLnR5cGUgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+SW50ZWdyYXRpb24gRGV0YWlsczwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgSW50ZWdyYXRpb24gTmFtZVxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIFByb2R1Y3Rpb24gSHViU3BvdCwgTWFya2V0aW5nIFdlYmhvb2tcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAge3JlbmRlckNvbmZpZ0Zvcm0oKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogU3VibWl0IEJ1dHRvbiAqL31cbiAgICAgICAgICB7Zm9ybURhdGEudHlwZSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdDcmVhdGluZy4uLicgOiAnQ3JlYXRlIEludGVncmF0aW9uJ31cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsIkRhc2hib2FyZExheW91dCIsIk5ld0ludGVncmF0aW9uUGFnZSIsInJvdXRlciIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJ0eXBlIiwibmFtZSIsImNvbmZpZyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2hvd0h1YlNwb3RJbmZvIiwic2V0U2hvd0h1YlNwb3RJbmZvIiwiaW50ZWdyYXRpb25UeXBlcyIsInZhbHVlIiwibGFiZWwiLCJpY29uIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInByb2plY3RJZCIsIm9rIiwicHVzaCIsImVycm9yIiwianNvbiIsImFsZXJ0IiwicmVuZGVyQ29uZmlnRm9ybSIsImRpdiIsImNsYXNzTmFtZSIsImlucHV0IiwiYWNjZXNzVG9rZW4iLCJvbkNoYW5nZSIsInByZXYiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIiwicG9ydGFsSWQiLCJjbGllbnRJZCIsImNsaWVudFNlY3JldCIsInJlZnJlc2hUb2tlbiIsImFwaUtleSIsImxpc3RJZCIsIndlYmhvb2tVcmwiLCJ1cmwiLCJzZWxlY3QiLCJvcHRpb24iLCJhdXRoSGVhZGVyIiwiaDEiLCJwIiwiZm9ybSIsIm9uU3VibWl0IiwiaDIiLCJtYXAiLCJjaGVja2VkIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJiYWNrIiwiZGlzYWJsZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/integrations/new/page.tsx\n"));

/***/ })

});