const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('Admin user already exists');
      console.log('Email: <EMAIL>');
      console.log('Password: admin');
      console.log('Role: ADMIN');
      return;
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin', 12);

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'System Administrator',
        password: hashedPassword,
        role: 'ADMIN',
        plan: 'ENTERPRISE',
        company: 'SmartForm Defender',
        emailVerified: new Date(),
      }
    });

    // Create a default project for admin
    const adminProject = await prisma.project.create({
      data: {
        name: 'Admin Dashboard',
        domain: 'admin.smartformdefender.com',
        description: 'Administrative project for system management',
        userId: adminUser.id,
        isActive: true,
        spamThreshold: 0.5,
        enableEmailValidation: true,
        enablePhoneValidation: true,
        enableGeoValidation: false,
        allowedCountries: '',
        blockedCountries: ''
      }
    });

    // Create an API key for the admin project
    await prisma.apiKey.create({
      data: {
        name: 'Admin API Key',
        key: 'sfd_admin_key_' + Math.random().toString(36).substring(2, 15),
        userId: adminUser.id,
        isActive: true
      }
    });

    console.log('✅ Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin');
    console.log('Role: ADMIN');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
