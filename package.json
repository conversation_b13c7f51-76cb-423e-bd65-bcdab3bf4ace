{"name": "smartform-defender", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node scripts/seed.js"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.19.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.0", "date-fns": "^3.6.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.441.0", "next": "^15.3.4", "next-auth": "^4.24.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.52.0", "recharts": "^2.12.0", "tailwind-merge": "^2.5.0", "zod": "^3.23.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "^15.3.4", "postcss": "^8.4.0", "prettier": "^3.3.0", "prettier-plugin-tailwindcss": "^0.6.0", "prisma": "^5.19.0", "tailwindcss": "^3.4.0", "tsx": "^4.20.3", "typescript": "^5.5.0"}, "engines": {"node": ">=18.0.0"}}