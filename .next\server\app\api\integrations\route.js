"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/integrations/route";
exports.ids = ["app/api/integrations/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2Froute&page=%2Fapi%2Fintegrations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2Froute&page=%2Fapi%2Fintegrations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/integrations/route.ts */ \"(rsc)/./src/app/api/integrations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/integrations/route\",\n        pathname: \"/api/integrations\",\n        filename: \"route\",\n        bundlePath: \"app/api/integrations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\integrations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/integrations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2Froute&page=%2Fapi%2Fintegrations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/integrations/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/integrations/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// GET /api/integrations - List integrations for a project\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const projectId = searchParams.get(\"projectId\");\n        if (!projectId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Project ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        const integrations = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findMany({\n            where: {\n                projectId\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            }\n        });\n        // Parse config JSON for response\n        const integrationsWithConfig = integrations.map((integration)=>({\n                ...integration,\n                config: integration.config ? JSON.parse(integration.config) : {}\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: integrationsWithConfig\n        });\n    } catch (error) {\n        console.error(\"Integrations fetch error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch integrations\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/integrations - Create new integration\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { projectId, type, name, config, isActive = true } = body;\n        if (!projectId || !type || !name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Project ID, type, and name are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate integration type\n        const validTypes = [\n            \"HUBSPOT\",\n            \"SALESFORCE\",\n            \"MAILCHIMP\",\n            \"ZAPIER\",\n            \"WEBHOOK\"\n        ];\n        if (!validTypes.includes(type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid integration type\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate config based on type\n        const validationResult = validateIntegrationConfig(type, config);\n        if (!validationResult.valid) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: validationResult.message\n            }, {\n                status: 400\n            });\n        }\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.create({\n            data: {\n                projectId,\n                type,\n                name,\n                config: JSON.stringify(config || {}),\n                isActive,\n                lastSync: null\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...integration,\n                config: integration.config ? JSON.parse(integration.config) : {}\n            }\n        });\n    } catch (error) {\n        console.error(\"Integration creation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create integration\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction validateIntegrationConfig(type, config) {\n    switch(type){\n        case \"HUBSPOT\":\n            if (!config?.accessToken && !config?.apiKey) {\n                return {\n                    valid: false,\n                    message: \"HubSpot integration requires accessToken or apiKey\"\n                };\n            }\n            break;\n        case \"SALESFORCE\":\n            if (!config?.clientId || !config?.clientSecret || !config?.refreshToken) {\n                return {\n                    valid: false,\n                    message: \"Salesforce integration requires clientId, clientSecret, and refreshToken\"\n                };\n            }\n            break;\n        case \"MAILCHIMP\":\n            if (!config?.apiKey || !config?.listId) {\n                return {\n                    valid: false,\n                    message: \"Mailchimp integration requires apiKey and listId\"\n                };\n            }\n            break;\n        case \"ZAPIER\":\n            if (!config?.webhookUrl) {\n                return {\n                    valid: false,\n                    message: \"Zapier integration requires webhookUrl\"\n                };\n            }\n            break;\n        case \"WEBHOOK\":\n            if (!config?.url) {\n                return {\n                    valid: false,\n                    message: \"Webhook integration requires url\"\n                };\n            }\n            break;\n        default:\n            return {\n                valid: false,\n                message: \"Unknown integration type\"\n            };\n    }\n    return {\n        valid: true,\n        message: \"Valid configuration\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/integrations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydGZvcm0tZGVmZW5kZXIvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2Froute&page=%2Fapi%2Fintegrations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();