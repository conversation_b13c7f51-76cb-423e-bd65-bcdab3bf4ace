import { prisma } from './prisma';
import { generateApiKey } from './auth';

export async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');

    // Create demo user
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        id: 'demo-user-id',
        email: '<EMAIL>',
        name: 'Demo User',
        plan: 'PROFESSIONAL'
      }
    });

    console.log('✅ Created demo user');

    // Create demo project
    const project = await prisma.project.upsert({
      where: { id: 'demo-project-id' },
      update: {},
      create: {
        id: 'demo-project-id',
        name: 'Demo Website',
        domain: 'demo.example.com',
        description: 'Demo project for testing SmartForm Defender',
        userId: user.id,
        isActive: true,
        spamThreshold: 0.7,
        enableEmailValidation: true,
        enablePhoneValidation: true,
        enableGeoValidation: false,
        allowedCountries: '',
        blockedCountries: ''
      }
    });

    // Create API key for the project
    const apiKey = await prisma.apiKey.upsert({
      where: { key: 'sfd_demo_key_12345' },
      update: {},
      create: {
        name: 'Demo API Key',
        key: 'sfd_demo_key_12345',
        userId: user.id,
        isActive: true
      }
    });

    console.log('✅ Created demo project');

    // Create sample integrations
    const hubspotIntegration = await prisma.integration.upsert({
      where: { id: 'demo-hubspot-integration' },
      update: {
        isActive: true,
        config: JSON.stringify({
          accessToken: 'demo-hubspot-token',
          portalId: '12345678'
        })
      },
      create: {
        id: 'demo-hubspot-integration',
        projectId: project.id,
        userId: user.id,
        type: 'HUBSPOT',
        name: 'HubSpot CRM',
        config: JSON.stringify({
          accessToken: 'demo_hubspot_token',
          portalId: '12345678'
        }),
        isActive: true
      }
    });

    const webhookIntegration = await prisma.integration.upsert({
      where: { id: 'demo-webhook-integration' },
      update: {
        isActive: true,
        config: JSON.stringify({
          url: 'http://localhost:3000/api/webhook-test',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer demo-token'
          }
        })
      },
      create: {
        id: 'demo-webhook-integration',
        projectId: project.id,
        userId: user.id,
        type: 'WEBHOOK',
        name: 'Custom Webhook',
        config: JSON.stringify({
          url: 'http://localhost:3000/api/webhook-test',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer demo-token'
          }
        }),
        isActive: true
      }
    });

    console.log('✅ Created sample integrations');

    // Create sample submissions
    const sampleSubmissions = [
      {
        email: '<EMAIL>',
        name: 'John Doe',
        company: 'Acme Corp',
        phone: '******-0123',
        spamScore: 0.1,
        isSpam: false,
        isValid: true,
        status: 'VALID',
        country: 'US',
        city: 'New York'
      },
      {
        email: '<EMAIL>',
        name: 'Jane Smith',
        company: 'TechCorp',
        phone: '******-0456',
        spamScore: 0.2,
        isSpam: false,
        isValid: true,
        status: 'VALID',
        country: 'US',
        city: 'San Francisco'
      },
      {
        email: '<EMAIL>',
        name: 'Spam Bot',
        company: 'Spam Inc',
        phone: '',
        spamScore: 0.9,
        isSpam: true,
        isValid: false,
        status: 'SPAM',
        country: 'Unknown',
        city: 'Unknown'
      },
      {
        email: '<EMAIL>',
        name: 'Test User',
        company: '',
        phone: '******-0789',
        spamScore: 0.6,
        isSpam: false,
        isValid: true,
        status: 'PENDING',
        country: 'CA',
        city: 'Toronto'
      },
      {
        email: '<EMAIL>',
        name: 'Marketing Team',
        company: 'Big Company',
        phone: '******-0999',
        spamScore: 0.3,
        isSpam: false,
        isValid: true,
        status: 'VALID',
        country: 'US',
        city: 'Chicago'
      }
    ];

    for (const submissionData of sampleSubmissions) {
      await prisma.submission.create({
        data: {
          projectId: project.id,
          formData: JSON.stringify({
            email: submissionData.email,
            name: submissionData.name,
            company: submissionData.company,
            phone: submissionData.phone,
            message: 'This is a sample message for testing purposes.'
          }),
          email: submissionData.email,
          phone: submissionData.phone || null,
          name: submissionData.name,
          company: submissionData.company || null,
          spamScore: submissionData.spamScore,
          isSpam: submissionData.isSpam,
          isValid: submissionData.isValid,
          validationResults: JSON.stringify({
            email: { valid: true, message: 'Email is valid' },
            phone: { valid: true, message: 'Phone is valid' }
          }),
          ipAddress: '192.168.1.' + Math.floor(Math.random() * 255),
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          fingerprint: JSON.stringify({
            canvas: 'demo_canvas_fingerprint',
            screen: '1920x1080',
            timezone: 'America/New_York',
            language: 'en-US'
          }),
          country: submissionData.country,
          city: submissionData.city,
          status: submissionData.status,
          sentToCrm: submissionData.status === 'VALID',
          crmResponse: submissionData.status === 'VALID' ? JSON.stringify({
            provider: 'hubspot',
            status: 'sent',
            contactId: 'demo_contact_' + Math.random().toString(36).substring(7)
          }) : null,
          createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
        }
      });
    }

    console.log('✅ Created sample submissions');

    // Create analytics data for the last 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);

      const totalSubmissions = Math.floor(Math.random() * 20) + 5;
      const spamBlocked = Math.floor(totalSubmissions * (Math.random() * 0.3));
      const validLeads = totalSubmissions - spamBlocked - Math.floor(Math.random() * 3);
      const conversionRate = totalSubmissions > 0 ? (validLeads / totalSubmissions) : 0;

      const analyticsId = `analytics-${project.id}-${date.toISOString().split('T')[0]}`;

      await prisma.analytics.upsert({
        where: { id: analyticsId },
        update: {},
        create: {
          id: analyticsId,
          date,
          projectId: project.id,
          totalSubmissions,
          spamBlocked,
          validLeads,
          conversionRate,
          hourlyData: JSON.stringify({
            '00': Math.floor(totalSubmissions * 0.1),
            '06': Math.floor(totalSubmissions * 0.2),
            '12': Math.floor(totalSubmissions * 0.4),
            '18': Math.floor(totalSubmissions * 0.3)
          })
        }
      });
    }

    console.log('✅ Created analytics data');

    console.log('🎉 Database seeding completed successfully!');
    console.log(`Demo project API key: ${apiKey.key}`);
    console.log(`Demo user email: ${user.email}`);

    return {
      user,
      project,
      apiKey,
      integrations: [hubspotIntegration, webhookIntegration]
    };

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}
