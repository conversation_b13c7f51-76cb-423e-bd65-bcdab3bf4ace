'use client';

import { useState } from 'react';

export default function HubSpotTestPage() {
  const [results, setResults] = useState<any>({});
  const [loading, setLoading] = useState<string>('');
  const [hubspotConfig, setHubspotConfig] = useState({
    accessToken: '',
    apiKey: '',
    portalId: ''
  });

  const testHubSpotConnection = async () => {
    setLoading('connection');
    try {
      const response = await fetch('/api/integrations/demo-hubspot-integration/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'sfd_demo_key_12345'
        }
      });

      const data = await response.json();
      setResults(prev => ({
        ...prev,
        connection: {
          status: response.status,
          data
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        connection: {
          status: 'error',
          data: error.message
        }
      }));
    } finally {
      setLoading('');
    }
  };

  const testLeadSubmission = async () => {
    setLoading('submission');
    try {
      const testData = {
        formData: {
          name: '<PERSON>e',
          email: '<EMAIL>',
          phone: '******-0123',
          company: 'Test Company Inc.',
          message: 'I am interested in your services. Please contact me.',
          website: 'https://testcompany.com'
        },
        fingerprint: {
          canvas: 'test_canvas_signature',
          screen: '1920x1080',
          timezone: 'America/New_York'
        },
        behaviorData: {
          mouseMovements: 75,
          keystrokes: 45,
          focusEvents: 8,
          timeOnPage: 45000
        },
        metadata: {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          language: 'en-US',
          referrer: 'https://google.com'
        }
      };

      const response = await fetch('/api/validate/submission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'sfd_demo_key_12345'
        },
        body: JSON.stringify(testData)
      });

      const data = await response.json();
      setResults(prev => ({
        ...prev,
        submission: {
          status: response.status,
          data
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        submission: {
          status: 'error',
          data: error.message
        }
      }));
    } finally {
      setLoading('');
    }
  };

  const updateHubSpotConfig = async () => {
    setLoading('config');
    try {
      const response = await fetch('/api/integrations/demo-hubspot-integration', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'sfd_demo_key_12345'
        },
        body: JSON.stringify({
          config: hubspotConfig
        })
      });

      const data = await response.json();
      setResults(prev => ({
        ...prev,
        config: {
          status: response.status,
          data
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        config: {
          status: 'error',
          data: error.message
        }
      }));
    } finally {
      setLoading('');
    }
  };

  const checkSubmissions = async () => {
    setLoading('submissions');
    try {
      const response = await fetch('/api/submissions?projectId=demo-project-id&limit=5', {
        headers: {
          'X-API-Key': 'sfd_demo_key_12345'
        }
      });

      const data = await response.json();
      setResults(prev => ({
        ...prev,
        submissions: {
          status: response.status,
          data
        }
      }));
    } catch (error) {
      setResults(prev => ({
        ...prev,
        submissions: {
          status: 'error',
          data: error.message
        }
      }));
    } finally {
      setLoading('');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">HubSpot Integration Testing</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Configuration Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">HubSpot Configuration</h2>
            
            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Access Token (OAuth)
                </label>
                <input
                  type="password"
                  value={hubspotConfig.accessToken}
                  onChange={(e) => setHubspotConfig(prev => ({ ...prev, accessToken: e.target.value }))}
                  className="input-field"
                  placeholder="pat-na1-..."
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API Key (Legacy)
                </label>
                <input
                  type="password"
                  value={hubspotConfig.apiKey}
                  onChange={(e) => setHubspotConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                  className="input-field"
                  placeholder="demo_hubspot_token"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Portal ID
                </label>
                <input
                  type="text"
                  value={hubspotConfig.portalId}
                  onChange={(e) => setHubspotConfig(prev => ({ ...prev, portalId: e.target.value }))}
                  className="input-field"
                  placeholder="12345678"
                />
              </div>
            </div>

            <div className="space-y-3">
              <button
                onClick={updateHubSpotConfig}
                disabled={loading === 'config'}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {loading === 'config' ? 'Updating...' : 'Update Configuration'}
              </button>

              <button
                onClick={testHubSpotConnection}
                disabled={loading === 'connection'}
                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                {loading === 'connection' ? 'Testing...' : 'Test HubSpot Connection'}
              </button>

              <button
                onClick={testLeadSubmission}
                disabled={loading === 'submission'}
                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
              >
                {loading === 'submission' ? 'Submitting...' : 'Test Lead Submission'}
              </button>

              <button
                onClick={checkSubmissions}
                disabled={loading === 'submissions'}
                className="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
              >
                {loading === 'submissions' ? 'Loading...' : 'Check Recent Submissions'}
              </button>
            </div>
          </div>

          {/* Results Panel */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {Object.entries(results).map(([test, result]: [string, any]) => (
                <div key={test} className="border rounded p-3">
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-sm text-gray-600 capitalize">{test}</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.status === 200 ? 'bg-green-100 text-green-800' :
                      result.status === 'error' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Documentation */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">HubSpot Integration Guide</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Setup Instructions</h3>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Create a HubSpot developer account</li>
                <li>Create a private app or use OAuth</li>
                <li>Get your access token or API key</li>
                <li>Find your Portal ID in HubSpot settings</li>
                <li>Update the configuration above</li>
                <li>Test the connection</li>
                <li>Submit a test lead</li>
              </ol>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-2">Features</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Automatic contact creation</li>
                <li>• Duplicate contact handling</li>
                <li>• Lead source tracking</li>
                <li>• Custom field mapping</li>
                <li>• Error handling & retry logic</li>
                <li>• Real-time status updates</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
