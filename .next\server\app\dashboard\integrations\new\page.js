/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/integrations/new/page";
exports.ids = ["app/dashboard/integrations/new/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fintegrations%2Fnew%2Fpage&page=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&appPaths=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fintegrations%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fintegrations%2Fnew%2Fpage&page=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&appPaths=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fintegrations%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/integrations/new/page.tsx */ \"(rsc)/./src/app/dashboard/integrations/new/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'integrations',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/integrations/new/page\",\n        pathname: \"/dashboard/integrations/new\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fintegrations%2Fnew%2Fpage&page=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&appPaths=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fintegrations%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(rsc)/./src/components/providers/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2plc3NlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1NtYXJ0Rm9ybURlZmVuZGVyJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2plc3NlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1NtYXJ0Rm9ybURlZmVuZGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDamVzc2UlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDU21hcnRGb3JtRGVmZW5kZXIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDQXV0aFByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa01BQXFMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxqZXNzZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxTbWFydEZvcm1EZWZlbmRlclxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnNcXFxcQXV0aFByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cintegrations%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cintegrations%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/integrations/new/page.tsx */ \"(rsc)/./src/app/dashboard/integrations/new/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2plc3NlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1NtYXJ0Rm9ybURlZmVuZGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDaW50ZWdyYXRpb25zJTVDJTVDbmV3JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUF1SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcamVzc2VcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcU21hcnRGb3JtRGVmZW5kZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcaW50ZWdyYXRpb25zXFxcXG5ld1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cintegrations%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/integrations/new/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/integrations/new/page.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\dashboard\\integrations\\new\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"490333976f1c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGplc3NlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFNtYXJ0Rm9ybURlZmVuZGVyXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0OTAzMzM5NzZmMWNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/AuthProvider */ \"(rsc)/./src/components/providers/AuthProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'SmartForm Defender - Lead Generation Without the Spam',\n    description: 'Protect your lead forms from spam and fake submissions with AI-powered validation and real-time filtering.',\n    keywords: 'lead generation, spam protection, form validation, CRM integration, HubSpot',\n    authors: [\n        {\n            name: 'SmartForm Defender'\n        }\n    ],\n    openGraph: {\n        title: 'SmartForm Defender - Lead Generation Without the Spam',\n        description: 'Protect your lead forms from spam and fake submissions with AI-powered validation and real-time filtering.',\n        type: 'website',\n        locale: 'en_US'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'SmartForm Defender - Lead Generation Without the Spam',\n        description: 'Protect your lead forms from spam and fake submissions with AI-powered validation and real-time filtering.'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\components\\providers\\AuthProvider.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/AuthProvider.tsx */ \"(ssr)/./src/components/providers/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2plc3NlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1NtYXJ0Rm9ybURlZmVuZGVyJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2plc3NlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1NtYXJ0Rm9ybURlZmVuZGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDamVzc2UlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDU21hcnRGb3JtRGVmZW5kZXIlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDcHJvdmlkZXJzJTVDJTVDQXV0aFByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa01BQXFMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBdXRoUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxqZXNzZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxTbWFydEZvcm1EZWZlbmRlclxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnNcXFxcQXV0aFByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cintegrations%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cintegrations%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/integrations/new/page.tsx */ \"(ssr)/./src/app/dashboard/integrations/new/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2plc3NlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q1NtYXJ0Rm9ybURlZmVuZGVyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDaW50ZWdyYXRpb25zJTVDJTVDbmV3JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUF1SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcamVzc2VcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcU21hcnRGb3JtRGVmZW5kZXJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcaW50ZWdyYXRpb25zXFxcXG5ld1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjesse%5C%5CDocuments%5C%5Caugment-projects%5C%5CSmartFormDefender%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cintegrations%5C%5Cnew%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/integrations/new/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/integrations/new/page.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewIntegrationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NewIntegrationPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        name: '',\n        config: {}\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHubSpotInfo, setShowHubSpotInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [projectId, setProjectId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Get project ID from URL parameters or redirect to projects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewIntegrationPage.useEffect\": ()=>{\n            const projectIdParam = searchParams.get('projectId');\n            const typeParam = searchParams.get('type');\n            if (projectIdParam) {\n                setProjectId(projectIdParam);\n            } else {\n                // If no project ID, redirect to projects page\n                router.push('/dashboard/projects');\n            }\n            // Pre-select integration type if provided\n            if (typeParam) {\n                setFormData({\n                    \"NewIntegrationPage.useEffect\": (prev)=>({\n                            ...prev,\n                            type: typeParam\n                        })\n                }[\"NewIntegrationPage.useEffect\"]);\n            }\n        }\n    }[\"NewIntegrationPage.useEffect\"], [\n        searchParams,\n        router\n    ]);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewIntegrationPage.useEffect\": ()=>{\n            if (status === 'loading') return;\n            if (!session) {\n                router.push('/auth/signin?callbackUrl=/dashboard/integrations/new');\n                return;\n            }\n        }\n    }[\"NewIntegrationPage.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    const integrationTypes = [\n        {\n            value: 'HUBSPOT',\n            label: 'HubSpot CRM',\n            icon: '🧡'\n        },\n        {\n            value: 'SALESFORCE',\n            label: 'Salesforce',\n            icon: '☁️'\n        },\n        {\n            value: 'MAILCHIMP',\n            label: 'Mailchimp',\n            icon: '🐵'\n        },\n        {\n            value: 'ZAPIER',\n            label: 'Zapier',\n            icon: '⚡'\n        },\n        {\n            value: 'WEBHOOK',\n            label: 'Custom Webhook',\n            icon: '🔗'\n        }\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!projectId) {\n            alert('Project ID is required');\n            return;\n        }\n        if (!session) {\n            alert('You must be logged in to create integrations');\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch('/api/integrations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    projectId,\n                    ...formData\n                })\n            });\n            if (response.ok) {\n                router.push(`/dashboard/integrations?projectId=${projectId}`);\n            } else {\n                const error = await response.json();\n                alert(`Failed to create integration: ${error.error}`);\n            }\n        } catch (error) {\n            console.error('Integration creation error:', error);\n            alert('Failed to create integration');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const renderConfigForm = ()=>{\n        switch(formData.type){\n            case 'HUBSPOT':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-blue-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Need help getting your HubSpot credentials?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowHubSpotInfo(true),\n                                        className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                                        children: \"View Instructions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Access Token\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.accessToken || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    accessToken: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Your HubSpot access token\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Portal ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.portalId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    portalId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Your HubSpot portal ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this);\n            case 'SALESFORCE':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Client ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.clientId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    clientId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce client ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Client Secret\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.clientSecret || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    clientSecret: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce client secret\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Refresh Token\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.refreshToken || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    refreshToken: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce refresh token\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this);\n            case 'MAILCHIMP':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"API Key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.apiKey || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    apiKey: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Mailchimp API key\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"List ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.listId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    listId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Mailchimp list ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 11\n                }, this);\n            case 'ZAPIER':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Webhook URL\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.config.webhookUrl || '',\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            config: {\n                                                ...prev.config,\n                                                webhookUrl: e.target.value\n                                            }\n                                        })),\n                                className: \"input-field\",\n                                placeholder: \"https://hooks.zapier.com/hooks/catch/...\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 11\n                }, this);\n            case 'WEBHOOK':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Webhook URL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formData.config.url || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    url: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"https://your-webhook-endpoint.com/webhook\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"HTTP Method\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.config.method || 'POST',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    method: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"POST\",\n                                            children: \"POST\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PUT\",\n                                            children: \"PUT\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PATCH\",\n                                            children: \"PATCH\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Authorization Header (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.authHeader || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    authHeader: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Bearer your-token or Basic base64-credentials\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-500\",\n                    children: \"Please select an integration type to configure\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Add New Integration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Connect SmartForm Defender to your favorite tools\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Choose Integration Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: integrationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: `relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${formData.type === type.value ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        name: \"type\",\n                                                        value: type.value,\n                                                        checked: formData.type === type.value,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    type: e.target.value,\n                                                                    config: {}\n                                                                })),\n                                                        className: \"sr-only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl mr-3\",\n                                                        children: type.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: type.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, type.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            formData.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-4\",\n                                        children: \"Integration Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Integration Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                className: \"input-field\",\n                                                placeholder: \"e.g., Production HubSpot, Marketing Webhook\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this),\n                                    renderConfigForm()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this),\n                            formData.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.back(),\n                                        className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                        children: loading ? 'Creating...' : 'Create Integration'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            showHubSpotInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"HubSpot Integration Setup\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowHubSpotInfo(false),\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-md font-medium text-gray-900 mb-3\",\n                                                children: \"\\uD83D\\uDD11 Getting Your Access Token\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Step 1:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Log in to your HubSpot account\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Step 2:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Go to Settings (gear icon) → Integrations → Private Apps\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Step 3:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ' Click \"Create a private app\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Step 4:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ' Give your app a name (e.g., \"SmartForm Defender\")'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Step 5:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ' In the \"Scopes\" tab, select these permissions:'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm text-gray-600 ml-4 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            className: \"bg-gray-200 px-1 rounded\",\n                                                                            children: \"forms\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \" (Read forms)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            className: \"bg-gray-200 px-1 rounded\",\n                                                                            children: \"crm.objects.contacts.write\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \" (Create contacts)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 462,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                            className: \"bg-gray-200 px-1 rounded\",\n                                                                            children: \"crm.objects.contacts.read\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \" (Read contacts)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Step 6:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ' Click \"Create app\" and copy the generated access token'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-md font-medium text-gray-900 mb-3\",\n                                                children: \"\\uD83C\\uDFE2 Finding Your Portal ID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 rounded-lg p-4 space-y-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Method 1:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" From your HubSpot URL\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 ml-4\",\n                                                            children: [\n                                                                \"Look at your HubSpot URL: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"bg-gray-200 px-1 rounded\",\n                                                                    children: \"app.hubspot.com/contacts/[PORTAL-ID]/contacts/list/view/all/\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 51\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-700 mt-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Method 2:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" From Account Settings\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 ml-4 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"• Go to Settings → Account Setup → Account Defaults\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"• Your Portal ID is displayed at the top of the page\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-yellow-400\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        fill: \"currentColor\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-sm font-medium text-yellow-800\",\n                                                            children: \"Security Note\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-yellow-700 mt-1\",\n                                                            children: \"Keep your access token secure and never share it publicly. SmartForm Defender encrypts and stores your credentials safely.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowHubSpotInfo(false),\n                                    className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n                                    children: \"Got it!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 515,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                lineNumber: 420,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n        lineNumber: 339,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/integrations/new/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _DashboardNav__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DashboardNav */ \"(ssr)/./src/components/DashboardNav.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DashboardNav__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9EYXNoYm9hcmRMYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRTBDO0FBTTNCLFNBQVNDLGdCQUFnQixFQUFFQyxRQUFRLEVBQXdCO0lBQ3hFLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0oscURBQVlBOzs7OztZQUNaRTs7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGplc3NlXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFNtYXJ0Rm9ybURlZmVuZGVyXFxzcmNcXGNvbXBvbmVudHNcXERhc2hib2FyZExheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgRGFzaGJvYXJkTmF2IGZyb20gJy4vRGFzaGJvYXJkTmF2JztcblxuaW50ZXJmYWNlIERhc2hib2FyZExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHsgY2hpbGRyZW4gfTogRGFzaGJvYXJkTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICA8RGFzaGJvYXJkTmF2IC8+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiRGFzaGJvYXJkTmF2IiwiRGFzaGJvYXJkTGF5b3V0IiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardNav.tsx":
/*!*****************************************!*\
  !*** ./src/components/DashboardNav.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardNav() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    // Close user menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"DashboardNav.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                    setShowUserMenu(false);\n                }\n            }\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"DashboardNav.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"DashboardNav.useEffect\"];\n        }\n    }[\"DashboardNav.useEffect\"], []);\n    const navigation = [\n        {\n            name: 'Overview',\n            href: '/dashboard',\n            icon: '📊'\n        },\n        {\n            name: 'Projects',\n            href: '/dashboard/projects',\n            icon: '📁'\n        },\n        {\n            name: 'Integrations',\n            href: '/dashboard/integrations',\n            icon: '🔗'\n        },\n        {\n            name: 'Submissions',\n            href: '/dashboard/submissions',\n            icon: '📝'\n        },\n        {\n            name: 'Analytics',\n            href: '/dashboard/analytics',\n            icon: '📈'\n        },\n        {\n            name: 'Settings',\n            href: '/dashboard/settings',\n            icon: '⚙️'\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === '/dashboard') {\n            return pathname === '/dashboard';\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_5__.LogoWithText, {\n                                        size: \"md\",\n                                        variant: \"default\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        className: `inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium ${isActive(item.href) ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:ml-6 sm:flex sm:items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/demo\",\n                                    className: \"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\",\n                                    children: \"Demo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-3 relative\",\n                                    ref: userMenuRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUserMenu(!showUserMenu),\n                                                className: \"bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only\",\n                                                        children: \"Open user menu\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    session?.user?.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-8 w-8 rounded-full\",\n                                                        src: session.user.image,\n                                                        alt: session.user.name || 'User'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: session?.user?.name?.charAt(0) || session?.user?.email?.charAt(0) || 'U'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                            lineNumber: 97,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"px-4 py-2 text-sm text-gray-700 border-b\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: session?.user?.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-500\",\n                                                                children: session?.user?.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/dashboard/settings\",\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        onClick: ()=>setShowUserMenu(false),\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/dashboard/billing\",\n                                                        className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        onClick: ()=>setShowUserMenu(false),\n                                                        children: \"Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    session?.user?.role === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/admin\",\n                                                        className: \"block px-4 py-2 text-sm text-red-700 hover:bg-red-50 border-t\",\n                                                        onClick: ()=>setShowUserMenu(false),\n                                                        children: \"Admin Portal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setShowUserMenu(false);\n                                                            (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.signOut)({\n                                                                callbackUrl: '/'\n                                                            });\n                                                        },\n                                                        className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        children: \"Sign out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sm:hidden flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Open main menu\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-6 w-6\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\DashboardNav.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardNav.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogoWithText: () => (/* binding */ LogoWithText),\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo({ className = '', size = 'md', variant = 'default' }) {\n    const sizeClasses = {\n        sm: 'h-6 w-6',\n        md: 'h-8 w-8',\n        lg: 'h-12 w-12'\n    };\n    const colorClasses = {\n        default: {\n            primary: 'text-primary-600',\n            secondary: 'text-primary-500',\n            accent: 'text-blue-400'\n        },\n        white: {\n            primary: 'text-white',\n            secondary: 'text-gray-100',\n            accent: 'text-blue-200'\n        },\n        dark: {\n            primary: 'text-gray-900',\n            secondary: 'text-gray-700',\n            accent: 'text-blue-600'\n        }\n    };\n    const colors = colorClasses[variant];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: `${sizeClasses[size]} ${className}`,\n        viewBox: \"0 0 40 40\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20 2L32 8V18C32 26.5 26.5 34 20 38C13.5 34 8 26.5 8 18V8L20 2Z\",\n                className: colors.primary,\n                fill: \"currentColor\",\n                fillOpacity: \"0.1\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1.5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20 6L28 10V18C28 24.5 24.5 30 20 33C15.5 30 12 24.5 12 18V10L20 6Z\",\n                className: colors.secondary,\n                fill: \"currentColor\",\n                fillOpacity: \"0.2\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1.2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"20\",\n                cy: \"18\",\n                r: \"6\",\n                className: colors.primary,\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"1.5\",\n                strokeDasharray: \"2 2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"20\",\n                cy: \"18\",\n                r: \"2\",\n                className: colors.accent,\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"16\",\n                cy: \"15\",\n                r: \"1\",\n                className: colors.accent,\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"24\",\n                cy: \"15\",\n                r: \"1\",\n                className: colors.accent,\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"16\",\n                cy: \"21\",\n                r: \"1\",\n                className: colors.accent,\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                cx: \"24\",\n                cy: \"21\",\n                r: \"1\",\n                className: colors.accent,\n                fill: \"currentColor\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M18 18L16 15M22 18L24 15M18 18L16 21M22 18L24 21\",\n                className: colors.accent,\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                strokeOpacity: \"0.6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M17 18L19 20L23 16\",\n                className: colors.primary,\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                fill: \"none\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M14 12L26 12M14 24L26 24\",\n                className: colors.secondary,\n                stroke: \"currentColor\",\n                strokeWidth: \"1\",\n                strokeOpacity: \"0.4\",\n                strokeLinecap: \"round\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\nfunction LogoWithText({ className = '', size = 'md', variant = 'default', showText = true }) {\n    const textSizeClasses = {\n        sm: 'text-lg',\n        md: 'text-xl',\n        lg: 'text-2xl'\n    };\n    const textColorClasses = {\n        default: 'text-gray-900',\n        white: 'text-white',\n        dark: 'text-gray-900'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex items-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                size: size,\n                variant: variant\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            showText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: `ml-2 font-bold ${textSizeClasses[size]} ${textColorClasses[variant]}`,\n                children: \"SmartForm Defender\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/AuthProvider.tsx":
/*!***************************************************!*\
  !*** ./src/components/providers/AuthProvider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\components\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFa0Q7QUFPM0MsU0FBU0MsYUFBYSxFQUFFQyxRQUFRLEVBQXFCO0lBQzFELHFCQUFPLDhEQUFDRiw0REFBZUE7a0JBQUVFOzs7Ozs7QUFDM0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcY29tcG9uZW50c1xccHJvdmlkZXJzXFxBdXRoUHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IEF1dGhQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8U2Vzc2lvblByb3ZpZGVyPntjaGlsZHJlbn08L1Nlc3Npb25Qcm92aWRlcj47XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fintegrations%2Fnew%2Fpage&page=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&appPaths=%2Fdashboard%2Fintegrations%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fintegrations%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();