{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/admin/stats/route": "app/api/admin/stats/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/admin/users/[id]/route": "app/api/admin/users/[id]/route.js", "/api/admin/users/[id]/change-password/route": "app/api/admin/users/[id]/change-password/route.js", "/admin/page": "app/admin/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/users/[id]/edit/page": "app/admin/users/[id]/edit/page.js", "/admin/settings/page": "app/admin/settings/page.js"}