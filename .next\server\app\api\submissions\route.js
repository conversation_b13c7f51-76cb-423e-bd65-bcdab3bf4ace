/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/submissions/route";
exports.ids = ["app/api/submissions/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubmissions%2Froute&page=%2Fapi%2Fsubmissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubmissions%2Froute&page=%2Fapi%2Fsubmissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_submissions_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/submissions/route.ts */ \"(rsc)/./src/app/api/submissions/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/submissions/route\",\n        pathname: \"/api/submissions\",\n        filename: \"route\",\n        bundlePath: \"app/api/submissions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\submissions\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_submissions_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubmissions%2Froute&page=%2Fapi%2Fsubmissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/submissions/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/submissions/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// GET /api/submissions - List submissions with filtering\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const projectId = searchParams.get('projectId');\n        const status = searchParams.get('status');\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '50');\n        const search = searchParams.get('search');\n        const skip = (page - 1) * limit;\n        // Build where clause\n        const where = {};\n        if (projectId) {\n            where.projectId = projectId;\n        }\n        if (status) {\n            where.status = status;\n        }\n        if (search) {\n            where.OR = [\n                {\n                    email: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    name: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                },\n                {\n                    company: {\n                        contains: search,\n                        mode: 'insensitive'\n                    }\n                }\n            ];\n        }\n        const [submissions, total] = await Promise.all([\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.findMany({\n                where,\n                include: {\n                    project: {\n                        select: {\n                            name: true,\n                            domain: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: 'desc'\n                },\n                skip,\n                take: limit\n            }),\n            _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.count({\n                where\n            })\n        ]);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            submissions,\n            pagination: {\n                page,\n                limit,\n                total,\n                pages: Math.ceil(total / limit)\n            }\n        });\n    } catch (error) {\n        console.error('Submissions fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch submissions'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/submissions - Create submission (for testing)\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { projectId, formData, email, phone, name, company, spamScore, isSpam, isValid, ipAddress, userAgent, country, city } = body;\n        const submission = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.create({\n            data: {\n                projectId,\n                formData: JSON.stringify(formData || {}),\n                email: email || null,\n                phone: phone || null,\n                name: name || null,\n                company: company || null,\n                spamScore: spamScore || 0,\n                isSpam: isSpam || false,\n                isValid: isValid || true,\n                validationResults: JSON.stringify({}),\n                ipAddress: ipAddress || 'unknown',\n                userAgent: userAgent || null,\n                fingerprint: JSON.stringify({}),\n                country: country || null,\n                city: city || null,\n                status: isSpam ? 'SPAM' : isValid ? 'VALID' : 'PENDING',\n                sentToCrm: false\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            submission\n        });\n    } catch (error) {\n        console.error('Submission creation error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create submission'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/submissions/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsubmissions%2Froute&page=%2Fapi%2Fsubmissions%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();