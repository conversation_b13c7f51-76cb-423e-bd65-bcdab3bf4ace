/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/projects/route";
exports.ids = ["app/api/projects/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_projects_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/projects/route.ts */ \"(rsc)/./src/app/api/projects/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/projects/route\",\n        pathname: \"/api/projects\",\n        filename: \"route\",\n        bundlePath: \"app/api/projects/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\projects\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_projects_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_3__.PrismaAdapter)(_lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma),\n    providers: [\n        // Only include Google provider if credentials are properly configured\n        ...process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET && process.env.GOOGLE_CLIENT_ID !== 'your-google-client-id' ? [\n            (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n                clientId: process.env.GOOGLE_CLIENT_ID,\n                clientSecret: process.env.GOOGLE_CLIENT_SECRET\n            })\n        ] : [],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_5___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    image: user.image\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt'\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup',\n        error: '/auth/error'\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                try {\n                    // Get user role from database\n                    const dbUser = await _lib_prisma__WEBPACK_IMPORTED_MODULE_4__.prisma.user.findUnique({\n                        where: {\n                            id: user.id\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    token.role = dbUser?.role || 'USER';\n                } catch (error) {\n                    console.error('Error fetching user role:', error);\n                    token.role = 'USER';\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    },\n    secret: \"development-secret-key-change-in-production\"\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/projects/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/projects/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-utils */ \"(rsc)/./src/lib/auth-utils.ts\");\n\n\n\n\n// GET /api/projects - List user's projects\nasync function GET(request) {\n    try {\n        const user = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_3__.requireAuth)();\n        const userId = user.id;\n        const projects = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.findMany({\n            where: {\n                userId\n            },\n            include: {\n                _count: {\n                    select: {\n                        submissions: true,\n                        integrations: true\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            projects\n        });\n    } catch (error) {\n        console.error('Projects fetch error:', error);\n        if (error instanceof Error && error.message === 'Authentication required') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch projects'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/projects - Create new project\nasync function POST(request) {\n    try {\n        const user = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_3__.requireAuth)();\n        const body = await request.json();\n        const { name, domain, description } = body;\n        if (!name || !domain) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Name and domain are required'\n            }, {\n                status: 400\n            });\n        }\n        const userId = user.id;\n        // Create project first\n        const project = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.project.create({\n            data: {\n                name,\n                domain,\n                description: description || '',\n                userId,\n                isActive: true,\n                spamThreshold: 0.7,\n                enableEmailValidation: true,\n                enablePhoneValidation: true,\n                enableGeoValidation: false,\n                allowedCountries: '',\n                blockedCountries: ''\n            }\n        });\n        // Generate and create API key for the project\n        const apiKeyValue = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.generateApiKey)();\n        const apiKey = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.apiKey.create({\n            data: {\n                name: `${name} API Key`,\n                key: apiKeyValue,\n                userId,\n                isActive: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            project: {\n                ...project,\n                apiKey: apiKey.key\n            }\n        });\n    } catch (error) {\n        console.error('Project creation error:', error);\n        if (error instanceof Error && error.message === 'Authentication required') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Authentication required'\n            }, {\n                status: 401\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create project'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/projects/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthenticatedUser: () => (/* binding */ getAuthenticatedUser),\n/* harmony export */   requireAdmin: () => (/* binding */ requireAdmin),\n/* harmony export */   requireAuth: () => (/* binding */ requireAuth)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"(rsc)/./node_modules/next-auth/next/index.js\");\n/* harmony import */ var _app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/api/auth/[...nextauth]/route */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\nasync function getAuthenticatedUser() {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.user?.id) {\n        return null;\n    }\n    return {\n        id: session.user.id,\n        email: session.user.email,\n        name: session.user.name,\n        image: session.user.image\n    };\n}\nasync function requireAuth() {\n    const user = await getAuthenticatedUser();\n    if (!user) {\n        throw new Error('Authentication required');\n    }\n    return user;\n}\nasync function requireAdmin() {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(_app_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session?.user?.id) {\n        throw new Error('Authentication required');\n    }\n    // Get user with role information\n    const { prisma } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\"));\n    const user = await prisma.user.findUnique({\n        where: {\n            id: session.user.id\n        },\n        select: {\n            id: true,\n            email: true,\n            name: true,\n            role: true\n        }\n    });\n    if (!user || user.role !== 'ADMIN') {\n        throw new Error('Admin access required');\n    }\n    return user;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgtdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBa0Q7QUFDZTtBQUcxRCxlQUFlRTtJQUNwQixNQUFNQyxVQUFVLE1BQU1ILGdFQUFnQkEsQ0FBQ0MscUVBQVdBO0lBRWxELElBQUksQ0FBQ0UsU0FBU0MsTUFBTUMsSUFBSTtRQUN0QixPQUFPO0lBQ1Q7SUFFQSxPQUFPO1FBQ0xBLElBQUlGLFFBQVFDLElBQUksQ0FBQ0MsRUFBRTtRQUNuQkMsT0FBT0gsUUFBUUMsSUFBSSxDQUFDRSxLQUFLO1FBQ3pCQyxNQUFNSixRQUFRQyxJQUFJLENBQUNHLElBQUk7UUFDdkJDLE9BQU9MLFFBQVFDLElBQUksQ0FBQ0ksS0FBSztJQUMzQjtBQUNGO0FBRU8sZUFBZUM7SUFDcEIsTUFBTUwsT0FBTyxNQUFNRjtJQUVuQixJQUFJLENBQUNFLE1BQU07UUFDVCxNQUFNLElBQUlNLE1BQU07SUFDbEI7SUFFQSxPQUFPTjtBQUNUO0FBRU8sZUFBZU87SUFDcEIsTUFBTVIsVUFBVSxNQUFNSCxnRUFBZ0JBLENBQUNDLHFFQUFXQTtJQUVsRCxJQUFJLENBQUNFLFNBQVNDLE1BQU1DLElBQUk7UUFDdEIsTUFBTSxJQUFJSyxNQUFNO0lBQ2xCO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU0sRUFBRUUsTUFBTSxFQUFFLEdBQUcsTUFBTSxxSUFBc0I7SUFDL0MsTUFBTVIsT0FBTyxNQUFNUSxPQUFPUixJQUFJLENBQUNTLFVBQVUsQ0FBQztRQUN4Q0MsT0FBTztZQUFFVCxJQUFJRixRQUFRQyxJQUFJLENBQUNDLEVBQUU7UUFBQztRQUM3QlUsUUFBUTtZQUFFVixJQUFJO1lBQU1DLE9BQU87WUFBTUMsTUFBTTtZQUFNUyxNQUFNO1FBQUs7SUFDMUQ7SUFFQSxJQUFJLENBQUNaLFFBQVFBLEtBQUtZLElBQUksS0FBSyxTQUFTO1FBQ2xDLE1BQU0sSUFBSU4sTUFBTTtJQUNsQjtJQUVBLE9BQU9OO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcbGliXFxhdXRoLXV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldFNlcnZlclNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvbmV4dCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUnO1xuaW1wb3J0IHsgTmV4dFJlcXVlc3QgfSBmcm9tICduZXh0L3NlcnZlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBdXRoZW50aWNhdGVkVXNlcigpIHtcbiAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpO1xuICBcbiAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gIFxuICByZXR1cm4ge1xuICAgIGlkOiBzZXNzaW9uLnVzZXIuaWQsXG4gICAgZW1haWw6IHNlc3Npb24udXNlci5lbWFpbCxcbiAgICBuYW1lOiBzZXNzaW9uLnVzZXIubmFtZSxcbiAgICBpbWFnZTogc2Vzc2lvbi51c2VyLmltYWdlLFxuICB9O1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVxdWlyZUF1dGgoKSB7XG4gIGNvbnN0IHVzZXIgPSBhd2FpdCBnZXRBdXRoZW50aWNhdGVkVXNlcigpO1xuXG4gIGlmICghdXNlcikge1xuICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQnKTtcbiAgfVxuXG4gIHJldHVybiB1c2VyO1xufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmVxdWlyZUFkbWluKCkge1xuICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucyk7XG5cbiAgaWYgKCFzZXNzaW9uPy51c2VyPy5pZCkge1xuICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQnKTtcbiAgfVxuXG4gIC8vIEdldCB1c2VyIHdpdGggcm9sZSBpbmZvcm1hdGlvblxuICBjb25zdCB7IHByaXNtYSB9ID0gYXdhaXQgaW1wb3J0KCdAL2xpYi9wcmlzbWEnKTtcbiAgY29uc3QgdXNlciA9IGF3YWl0IHByaXNtYS51c2VyLmZpbmRVbmlxdWUoe1xuICAgIHdoZXJlOiB7IGlkOiBzZXNzaW9uLnVzZXIuaWQgfSxcbiAgICBzZWxlY3Q6IHsgaWQ6IHRydWUsIGVtYWlsOiB0cnVlLCBuYW1lOiB0cnVlLCByb2xlOiB0cnVlIH1cbiAgfSk7XG5cbiAgaWYgKCF1c2VyIHx8IHVzZXIucm9sZSAhPT0gJ0FETUlOJykge1xuICAgIHRocm93IG5ldyBFcnJvcignQWRtaW4gYWNjZXNzIHJlcXVpcmVkJyk7XG4gIH1cblxuICByZXR1cm4gdXNlcjtcbn1cbiJdLCJuYW1lcyI6WyJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJnZXRBdXRoZW50aWNhdGVkVXNlciIsInNlc3Npb24iLCJ1c2VyIiwiaWQiLCJlbWFpbCIsIm5hbWUiLCJpbWFnZSIsInJlcXVpcmVBdXRoIiwiRXJyb3IiLCJyZXF1aXJlQWRtaW4iLCJwcmlzbWEiLCJmaW5kVW5pcXVlIiwid2hlcmUiLCJzZWxlY3QiLCJyb2xlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\nasync function validateApiKey(apiKey) {\n    try {\n        if (!apiKey || !apiKey.startsWith('sfd_')) {\n            return null;\n        }\n        // Find the API key and get the associated user's projects\n        const apiKeyRecord = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.findFirst({\n            where: {\n                key: apiKey,\n                isActive: true\n            },\n            include: {\n                user: {\n                    include: {\n                        projects: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!apiKeyRecord || !apiKeyRecord.user.projects.length) {\n            return null;\n        }\n        // Update last used timestamp\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.update({\n            where: {\n                id: apiKeyRecord.id\n            },\n            data: {\n                lastUsed: new Date()\n            }\n        });\n        // Return the first active project (in a real app, you might want to specify which project)\n        return apiKeyRecord.user.projects[0];\n    } catch (error) {\n        console.error('API key validation error:', error);\n        return null;\n    }\n}\nfunction generateApiKey() {\n    return 'sfd_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/bcryptjs","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/lru-cache","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprojects%2Froute&page=%2Fapi%2Fprojects%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();