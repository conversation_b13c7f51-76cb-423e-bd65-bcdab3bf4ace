# Accessibility Guidelines for SmartForm Defender

## Critical Issue: White Text on White Background

**PROBLEM**: Multiple instances of white text on white background have occurred in forms throughout the application, making text invisible and violating accessibility standards.

**SOLUTION**: Always use the pre-built form components from `src/components/ui/FormComponents.tsx` instead of raw HTML form elements.

## Required Form Component Usage

### ✅ CORRECT - Use Form Components
```tsx
import { FormInput, FormSelect, FormTextarea, FormCheckbox, FormSection } from '@/components/ui/FormComponents';

// Input field
<FormInput
  label="Integration Name"
  value={formData.name}
  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
  placeholder="Enter integration name"
  required
/>

// Select dropdown
<FormSelect
  label="HTTP Method"
  value={formData.method}
  onChange={(e) => setFormData(prev => ({ ...prev, method: e.target.value }))}
>
  <option value="POST">POST</option>
  <option value="PUT">PUT</option>
</FormSelect>

// Checkbox
<FormCheckbox
  label="Enable this integration"
  checked={formData.isActive}
  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
/>
```

### ❌ INCORRECT - Raw HTML Elements
```tsx
// DON'T DO THIS - Can cause white text on white background
<input
  type="text"
  className="w-full px-3 py-2 border border-gray-300 rounded-md"
  // Missing explicit text color and background color
/>
```

## Mandatory CSS Classes for Form Elements

If you MUST use raw HTML form elements, always include these classes:

```css
/* Required for all input elements */
bg-white text-gray-900 placeholder-gray-500

/* Complete example */
className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500"
```

## Form Structure Guidelines

### Use FormSection for Consistent Layout
```tsx
<FormSection title="Integration Details" description="Configure your integration settings">
  <FormInput label="Name" value={name} onChange={handleNameChange} />
  <FormSelect label="Type" value={type} onChange={handleTypeChange}>
    <option value="HUBSPOT">HubSpot</option>
    <option value="WEBHOOK">Webhook</option>
  </FormSelect>
</FormSection>
```

### Button Components
```tsx
import { PrimaryButton, SecondaryButton, DangerButton } from '@/components/ui/FormComponents';

<PrimaryButton type="submit" loading={saving}>
  Save Changes
</PrimaryButton>

<SecondaryButton onClick={() => router.back()}>
  Cancel
</SecondaryButton>

<DangerButton onClick={handleDelete} loading={deleting}>
  Delete Integration
</DangerButton>
```

## Accessibility Checklist

Before submitting any form-related code, verify:

- [ ] All form inputs use FormInput, FormSelect, FormTextarea, or FormCheckbox components
- [ ] If raw HTML is used, it includes `bg-white text-gray-900 placeholder-gray-500` classes
- [ ] All form fields have proper labels
- [ ] Error states are clearly visible with red text
- [ ] Focus states are properly styled
- [ ] Disabled states are visually distinct

## Color Contrast Requirements

- **Text on white background**: Use `text-gray-900` (dark gray/black)
- **Placeholder text**: Use `placeholder-gray-500` (medium gray)
- **Labels**: Use `text-gray-700` (dark gray)
- **Error text**: Use `text-red-600` (red)
- **Helper text**: Use `text-gray-500` (medium gray)

## Testing for Accessibility Issues

1. **Visual Test**: Ensure all text is clearly visible against its background
2. **Contrast Test**: Use browser dev tools to check color contrast ratios
3. **Keyboard Navigation**: Tab through all form elements
4. **Screen Reader Test**: Verify labels are properly associated with inputs

## Files That Must Use These Guidelines

- All pages in `/src/app/dashboard/`
- All components in `/src/components/`
- Any new form implementations
- Integration configuration forms
- User management forms
- Project configuration forms

## Enforcement

- Code reviews must check for proper form component usage
- Any white text on white background issues must be immediately fixed
- New forms must use the standardized components
- Legacy forms should be migrated to use these components when modified

## Contact

If you encounter accessibility issues or need clarification on these guidelines, refer to this document and the form components in `src/components/ui/FormComponents.tsx`.
