'use client';

import { useState } from 'react';
import { Trash2, AlertTriangle } from 'lucide-react';

interface DeleteUserDialogProps {
  user: {
    id: string;
    name: string;
    email: string;
  };
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (userId: string) => Promise<void>;
  isDeleting: boolean;
}

export default function DeleteUserDialog({
  user,
  isOpen,
  onClose,
  onConfirm,
  isDeleting
}: DeleteUserDialogProps) {
  if (!isOpen) return null;

  const handleConfirm = async () => {
    await onConfirm(user.id);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-6 h-6 text-red-600" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900">
              Delete User
            </h3>
            <p className="text-sm text-gray-500">
              This action cannot be undone
            </p>
          </div>
        </div>

        <div className="mb-6">
          <p className="text-sm text-gray-700 mb-2">
            Are you sure you want to delete this user? This will permanently remove:
          </p>
          <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
            <li>User account: <strong>{user.name}</strong> ({user.email})</li>
            <li>All associated projects</li>
            <li>All form submissions</li>
            <li>All integrations</li>
            <li>All user sessions</li>
          </ul>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            disabled={isDeleting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isDeleting}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 flex items-center"
          >
            {isDeleting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4 mr-2" />
                Delete User
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
