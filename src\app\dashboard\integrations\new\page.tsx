'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import DashboardLayout from '@/components/DashboardLayout';

export default function NewIntegrationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  const [formData, setFormData] = useState({
    type: '',
    name: '',
    config: {}
  });
  const [loading, setLoading] = useState(false);
  const [showHubSpotInfo, setShowHubSpotInfo] = useState(false);
  const [projectId, setProjectId] = useState<string | null>(null);

  // Get project ID from URL parameters or redirect to projects
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    const typeParam = searchParams.get('type');

    if (projectIdParam) {
      setProjectId(projectIdParam);
    } else {
      // If no project ID, redirect to projects page
      router.push('/dashboard/projects');
    }

    // Pre-select integration type if provided
    if (typeParam) {
      setFormData(prev => ({ ...prev, type: typeParam }));
    }
  }, [searchParams, router]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin?callbackUrl=/dashboard/integrations/new');
      return;
    }
  }, [session, status, router]);

  const integrationTypes = [
    { value: 'HUBSPOT', label: 'HubSpot CRM', icon: '🧡' },
    { value: 'SALESFORCE', label: 'Salesforce', icon: '☁️' },
    { value: 'MAILCHIMP', label: 'Mailchimp', icon: '🐵' },
    { value: 'ZAPIER', label: 'Zapier', icon: '⚡' },
    { value: 'WEBHOOK', label: 'Custom Webhook', icon: '🔗' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!projectId) {
      alert('Project ID is required');
      return;
    }

    if (!session) {
      alert('You must be logged in to create integrations');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/integrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          projectId,
          ...formData
        })
      });

      if (response.ok) {
        router.push(`/dashboard/integrations?projectId=${projectId}`);
      } else {
        const error = await response.json();
        alert(`Failed to create integration: ${error.error}`);
      }
    } catch (error) {
      console.error('Integration creation error:', error);
      alert('Failed to create integration');
    } finally {
      setLoading(false);
    }
  };

  const renderConfigForm = () => {
    switch (formData.type) {
      case 'HUBSPOT':
        return (
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      Need help getting your HubSpot credentials?
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => setShowHubSpotInfo(true)}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  View Instructions
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Access Token
              </label>
              <input
                type="password"
                value={formData.config.accessToken || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, accessToken: e.target.value }
                }))}
                className="input-field"
                placeholder="Your HubSpot access token"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Portal ID
              </label>
              <input
                type="text"
                value={formData.config.portalId || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, portalId: e.target.value }
                }))}
                className="input-field"
                placeholder="Your HubSpot portal ID"
                required
              />
            </div>
          </div>
        );

      case 'SALESFORCE':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Client ID
              </label>
              <input
                type="text"
                value={formData.config.clientId || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, clientId: e.target.value }
                }))}
                className="input-field"
                placeholder="Enter your Salesforce client ID"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Client Secret
              </label>
              <input
                type="password"
                value={formData.config.clientSecret || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, clientSecret: e.target.value }
                }))}
                className="input-field"
                placeholder="Enter your Salesforce client secret"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Refresh Token
              </label>
              <input
                type="password"
                value={formData.config.refreshToken || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, refreshToken: e.target.value }
                }))}
                className="input-field"
                placeholder="Enter your Salesforce refresh token"
                required
              />
            </div>
          </div>
        );

      case 'MAILCHIMP':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API Key
              </label>
              <input
                type="password"
                value={formData.config.apiKey || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, apiKey: e.target.value }
                }))}
                className="input-field"
                placeholder="Enter your Mailchimp API key"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                List ID
              </label>
              <input
                type="text"
                value={formData.config.listId || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, listId: e.target.value }
                }))}
                className="input-field"
                placeholder="Enter your Mailchimp list ID"
                required
              />
            </div>
          </div>
        );

      case 'ZAPIER':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Webhook URL
              </label>
              <input
                type="url"
                value={formData.config.webhookUrl || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, webhookUrl: e.target.value }
                }))}
                className="input-field"
                placeholder="https://hooks.zapier.com/hooks/catch/..."
                required
              />
            </div>
          </div>
        );

      case 'WEBHOOK':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Webhook URL
              </label>
              <input
                type="url"
                value={formData.config.url || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, url: e.target.value }
                }))}
                className="input-field"
                placeholder="https://your-webhook-endpoint.com/webhook"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                HTTP Method
              </label>
              <select
                value={formData.config.method || 'POST'}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, method: e.target.value }
                }))}
                className="input-field"
              >
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Authorization Header (Optional)
              </label>
              <input
                type="text"
                value={formData.config.authHeader || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  config: { ...prev.config, authHeader: e.target.value }
                }))}
                className="input-field"
                placeholder="Bearer your-token or Basic base64-credentials"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Please select an integration type to configure
          </div>
        );
    }
  };

  return (
    <DashboardLayout>
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Add New Integration</h1>
          <p className="text-gray-600 mt-2">Connect SmartForm Defender to your favorite tools</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Integration Type Selection */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Choose Integration Type</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {integrationTypes.map((type) => (
                <label
                  key={type.value}
                  className={`relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                    formData.type === type.value
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="type"
                    value={type.value}
                    checked={formData.type === type.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value, config: {} }))}
                    className="sr-only"
                  />
                  <span className="text-2xl mr-3">{type.icon}</span>
                  <span className="text-sm font-medium text-gray-900">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Integration Name */}
          {formData.type && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Integration Details</h2>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Integration Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="input-field"
                  placeholder="e.g., Production HubSpot, Marketing Webhook"
                  required
                />
              </div>
              {renderConfigForm()}
            </div>
          )}

          {/* Submit Button */}
          {formData.type && (
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Integration'}
              </button>
            </div>
          )}
        </form>
      </div>

      {/* HubSpot Information Modal */}
      {showHubSpotInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  HubSpot Integration Setup
                </h3>
                <button
                  onClick={() => setShowHubSpotInfo(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                {/* Access Token Section */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    🔑 Getting Your Access Token
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                    <div className="space-y-2">
                      <p className="text-sm text-gray-700">
                        <strong>Step 1:</strong> Log in to your HubSpot account
                      </p>
                      <p className="text-sm text-gray-700">
                        <strong>Step 2:</strong> Go to Settings (gear icon) → Integrations → Private Apps
                      </p>
                      <p className="text-sm text-gray-700">
                        <strong>Step 3:</strong> Click "Create a private app"
                      </p>
                      <p className="text-sm text-gray-700">
                        <strong>Step 4:</strong> Give your app a name (e.g., "SmartForm Defender")
                      </p>
                      <p className="text-sm text-gray-700">
                        <strong>Step 5:</strong> In the "Scopes" tab, select these permissions:
                      </p>
                      <ul className="text-sm text-gray-600 ml-4 space-y-1">
                        <li>• <code className="bg-gray-200 px-1 rounded">forms</code> (Read forms)</li>
                        <li>• <code className="bg-gray-200 px-1 rounded">crm.objects.contacts.write</code> (Create contacts)</li>
                        <li>• <code className="bg-gray-200 px-1 rounded">crm.objects.contacts.read</code> (Read contacts)</li>
                      </ul>
                      <p className="text-sm text-gray-700">
                        <strong>Step 6:</strong> Click "Create app" and copy the generated access token
                      </p>
                    </div>
                  </div>
                </div>

                {/* Portal ID Section */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">
                    🏢 Finding Your Portal ID
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                    <div className="space-y-2">
                      <p className="text-sm text-gray-700">
                        <strong>Method 1:</strong> From your HubSpot URL
                      </p>
                      <p className="text-sm text-gray-600 ml-4">
                        Look at your HubSpot URL: <code className="bg-gray-200 px-1 rounded">app.hubspot.com/contacts/[PORTAL-ID]/contacts/list/view/all/</code>
                      </p>
                      <p className="text-sm text-gray-700 mt-3">
                        <strong>Method 2:</strong> From Account Settings
                      </p>
                      <div className="text-sm text-gray-600 ml-4 space-y-1">
                        <p>• Go to Settings → Account Setup → Account Defaults</p>
                        <p>• Your Portal ID is displayed at the top of the page</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Security Note */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h5 className="text-sm font-medium text-yellow-800">Security Note</h5>
                      <p className="text-sm text-yellow-700 mt-1">
                        Keep your access token secure and never share it publicly. SmartForm Defender encrypts and stores your credentials safely.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowHubSpotInfo(false)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Got it!
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
