import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { IntegrationManager } from '@/lib/integrations';
import { prisma } from '@/lib/prisma';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Validate session authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get integration and verify ownership
    const integration = await prisma.integration.findFirst({
      where: {
        id: id,
        userId: user.id
      }
    });

    if (!integration) {
      return NextResponse.json(
        { error: 'Integration not found' },
        { status: 404 }
      );
    }

    // Test the integration
    const result = await IntegrationManager.testIntegration({
      type: integration.type,
      config: JSON.parse(integration.config)
    });

    // Update integration status based on test result
    await prisma.integration.update({
      where: { id: integration.id },
      data: {
        lastTested: new Date(),
        isActive: result.success
      }
    });

    return NextResponse.json({
      success: result.success,
      message: result.message,
      data: result.data,
      provider: result.provider,
      testedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Integration test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
