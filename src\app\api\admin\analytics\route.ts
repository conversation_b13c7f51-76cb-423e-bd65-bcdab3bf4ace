import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdmin();

    // Calculate date ranges
    const now = new Date();
    const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get user growth statistics
    const [
      totalUsers,
      usersThisMonth,
      usersLastMonth
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          createdAt: {
            gte: startOfThisMonth
          }
        }
      }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      })
    ]);

    // Calculate growth percentage
    const userGrowth = usersLastMonth > 0 
      ? ((usersThisMonth - usersLastMonth) / usersLastMonth) * 100 
      : 0;

    // Get project statistics
    const [
      totalProjects,
      activeProjects
    ] = await Promise.all([
      prisma.project.count(),
      prisma.project.count({
        where: { isActive: true }
      })
    ]);

    // Get submission statistics
    const [
      totalSubmissions,
      submissionsThisMonth,
      spamSubmissions
    ] = await Promise.all([
      prisma.submission.count(),
      prisma.submission.count({
        where: {
          createdAt: {
            gte: startOfThisMonth
          }
        }
      }),
      prisma.submission.count({
        where: { isSpam: true }
      })
    ]);

    const spamRate = totalSubmissions > 0 
      ? (spamSubmissions / totalSubmissions) * 100 
      : 0;

    // Get plan distribution
    const planDistribution = await prisma.user.groupBy({
      by: ['plan'],
      _count: {
        plan: true
      }
    });

    const planStats = {
      FREE: 0,
      PRO: 0,
      ENTERPRISE: 0
    };

    planDistribution.forEach(item => {
      if (item.plan in planStats) {
        planStats[item.plan as keyof typeof planStats] = item._count.plan;
      }
    });

    // Get recent activity (simplified - you can expand this based on your needs)
    const recentUsers = await prisma.user.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    });

    const recentActivity = recentUsers.map(user => ({
      id: user.id,
      type: 'user_registration',
      description: `New user registered: ${user.name || user.email}`,
      timestamp: user.createdAt.toISOString()
    }));

    const analytics = {
      userGrowth: {
        total: totalUsers,
        thisMonth: usersThisMonth,
        lastMonth: usersLastMonth,
        growth: userGrowth
      },
      projectStats: {
        total: totalProjects,
        active: activeProjects,
        inactive: totalProjects - activeProjects
      },
      submissionStats: {
        total: totalSubmissions,
        thisMonth: submissionsThisMonth,
        spamBlocked: spamSubmissions,
        spamRate: spamRate
      },
      planDistribution: planStats,
      recentActivity: recentActivity
    };

    return NextResponse.json(analytics);

  } catch (error) {
    console.error('Admin analytics error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Authentication required' || error.message === 'Admin access required') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}
