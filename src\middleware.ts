import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Check if user is authenticated for protected routes
        const { pathname } = req.nextUrl;

        // Public routes that don't require authentication
        const publicRoutes = [
          '/',
          '/about',
          '/contact',
          '/docs',
          '/demo',
          '/api-docs',
          '/auth/signin',
          '/auth/signup',
          '/auth/error',
          '/api/auth',
          '/api/validate',
          '/api/webhook-test',
        ];

        // Admin routes that require admin role
        const adminRoutes = [
          '/admin'
        ];

        // Check if the current path is public
        const isPublicRoute = publicRoutes.some(route =>
          pathname.startsWith(route)
        );

        // Check if the current path is admin
        const isAdminRoute = adminRoutes.some(route =>
          pathname.startsWith(route)
        );

        // Allow access to public routes
        if (isPublicRoute) {
          return true;
        }

        // For admin routes, check if user has admin role
        if (isAdminRoute) {
          return !!token && token.role === 'ADMIN';
        }

        // Require authentication for all other routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
