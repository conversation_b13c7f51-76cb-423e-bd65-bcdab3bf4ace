'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import AdminNav from '@/components/admin/AdminNav';
import { 
  ArrowLeft, 
  User, 
  Mail, 
  Building, 
  Calendar,
  Shield,
  CreditCard,
  Database,
  Activity
} from 'lucide-react';

interface UserDetail {
  id: string;
  email: string;
  name: string | null;
  company: string | null;
  phone: string | null;
  role: string;
  plan: string;
  createdAt: string;
  emailVerified: string | null;
  projects: Array<{
    id: string;
    name: string;
    domain: string;
    isActive: boolean;
    createdAt: string;
    _count: {
      submissions: number;
    };
  }>;
  integrations: Array<{
    id: string;
    type: string;
    isActive: boolean;
    createdAt: string;
  }>;
  _count: {
    projects: number;
    integrations: number;
  };
}

export default function UserDetailPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const [user, setUser] = useState<UserDetail | null>(null);
  const [loading, setLoading] = useState(true);

  // Redirect if not admin
  useEffect(() => {
    if (status === 'loading') return;
    if (!session || session.user.role !== 'ADMIN') {
      router.push('/dashboard');
      return;
    }
  }, [session, status, router]);

  useEffect(() => {
    if (session?.user?.role === 'ADMIN' && userId) {
      loadUserDetail();
    }
  }, [session, userId]);

  const loadUserDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/users/${userId}`);
      if (response.ok) {
        const data = await response.json();
        setUser(data);
      } else {
        console.error('Failed to load user details');
      }
    } catch (error) {
      console.error('Failed to load user details:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null;
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <AdminNav />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">User not found</h1>
            <button
              onClick={() => router.push('/admin/users')}
              className="mt-4 text-blue-600 hover:text-blue-800"
            >
              ← Back to Users
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminNav />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.push('/admin/users')}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </button>
          <h1 className="text-3xl font-bold text-gray-900">User Details</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* User Information */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">User Information</h2>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {user.name || 'No name provided'}
                    </div>
                    <div className="text-sm text-gray-500">Name</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{user.email}</div>
                    <div className="text-sm text-gray-500">
                      Email {user.emailVerified ? '(Verified)' : '(Not verified)'}
                    </div>
                  </div>
                </div>

                {user.company && (
                  <div className="flex items-center">
                    <Building className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{user.company}</div>
                      <div className="text-sm text-gray-500">Company</div>
                    </div>
                  </div>
                )}

                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'ADMIN' 
                        ? 'bg-red-100 text-red-800' 
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {user.role}
                    </span>
                    <div className="text-sm text-gray-500">Role</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <CreditCard className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.plan === 'ENTERPRISE' 
                        ? 'bg-purple-100 text-purple-800'
                        : user.plan === 'PRO'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.plan}
                    </span>
                    <div className="text-sm text-gray-500">Plan</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {formatDate(user.createdAt)}
                    </div>
                    <div className="text-sm text-gray-500">Member since</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Projects and Services */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              {/* Projects */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900 flex items-center">
                    <Database className="h-5 w-5 mr-2" />
                    Projects ({user.projects.length})
                  </h2>
                </div>
                <div className="p-6">
                  {user.projects.length > 0 ? (
                    <div className="space-y-4">
                      {user.projects.map((project) => (
                        <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium text-gray-900">{project.name}</h3>
                              <p className="text-sm text-gray-500">{project.domain}</p>
                              <p className="text-xs text-gray-400">
                                Created {formatDate(project.createdAt)}
                              </p>
                            </div>
                            <div className="text-right">
                              <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                project.isActive 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {project.isActive ? 'Active' : 'Inactive'}
                              </div>
                              <p className="text-sm text-gray-500 mt-1">
                                {project._count.submissions} submissions
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No projects found</p>
                  )}
                </div>
              </div>

              {/* Integrations */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-medium text-gray-900 flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    Integrations ({user.integrations.length})
                  </h2>
                </div>
                <div className="p-6">
                  {user.integrations.length > 0 ? (
                    <div className="space-y-4">
                      {user.integrations.map((integration) => (
                        <div key={integration.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-medium text-gray-900">{integration.type}</h3>
                              <p className="text-xs text-gray-400">
                                Created {formatDate(integration.createdAt)}
                              </p>
                            </div>
                            <div className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              integration.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {integration.isActive ? 'Active' : 'Inactive'}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No integrations found</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
