import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { generateApiKey } from '@/lib/auth';
import { requireAuth } from '@/lib/auth-utils';

// GET /api/projects - List user's projects
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth();
    const userId = user.id;

    const projects = await prisma.project.findMany({
      where: {
        userId
      },
      include: {
        _count: {
          select: {
            submissions: true,
            integrations: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({ projects });

  } catch (error) {
    console.error('Projects fetch error:', error);

    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

// POST /api/projects - Create new project
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    const body = await request.json();
    const { name, domain, description } = body;

    if (!name || !domain) {
      return NextResponse.json(
        { error: 'Name and domain are required' },
        { status: 400 }
      );
    }

    const userId = user.id;

    // Generate API key
    const apiKey = generateApiKey();

    const project = await prisma.project.create({
      data: {
        name,
        domain,
        description: description || '',
        userId,
        apiKey,
        isActive: true,
        spamThreshold: 0.7,
        enableEmailValidation: true,
        enablePhoneValidation: true,
        enableGeoValidation: false,
        allowedCountries: '',
        blockedCountries: '',
        plan: 'FREE'
      }
    });

    return NextResponse.json({ project });

  } catch (error) {
    console.error('Project creation error:', error);

    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}
