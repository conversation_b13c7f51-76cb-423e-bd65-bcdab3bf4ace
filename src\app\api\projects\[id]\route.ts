import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth-utils';

// GET /api/projects/[id] - Get project details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const project = await prisma.project.findUnique({
      where: {
        id
      },
      include: {
        integrations: true,
        user: {
          include: {
            apiKeys: {
              where: {
                isActive: true
              },
              select: {
                key: true,
                name: true,
                createdAt: true,
                lastUsed: true
              }
            }
          }
        },
        _count: {
          select: {
            submissions: true
          }
        }
      }
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ project });

  } catch (error) {
    console.error('Project fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project' },
      { status: 500 }
    );
  }
}

// PUT /api/projects/[id] - Update project
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const {
      name,
      domain,
      description,
      spamThreshold,
      enableEmailValidation,
      enablePhoneValidation,
      enableGeoValidation,
      allowedCountries,
      blockedCountries,
      isActive
    } = body;

    const project = await prisma.project.update({
      where: {
        id
      },
      data: {
        ...(name && { name }),
        ...(domain && { domain }),
        ...(description !== undefined && { description }),
        ...(spamThreshold !== undefined && { spamThreshold }),
        ...(enableEmailValidation !== undefined && { enableEmailValidation }),
        ...(enablePhoneValidation !== undefined && { enablePhoneValidation }),
        ...(enableGeoValidation !== undefined && { enableGeoValidation }),
        ...(allowedCountries !== undefined && { allowedCountries }),
        ...(blockedCountries !== undefined && { blockedCountries }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date()
      }
    });

    return NextResponse.json({ project });

  } catch (error) {
    console.error('Project update error:', error);
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    );
  }
}

// DELETE /api/projects/[id] - Delete project
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAuth();
    const { id } = await params;

    // Verify the project belongs to the authenticated user before deleting
    const project = await prisma.project.findFirst({
      where: {
        id,
        userId: user.id
      }
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      );
    }

    // Completely delete the project and all related data
    await prisma.project.delete({
      where: {
        id
      }
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Project deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    );
  }
}
