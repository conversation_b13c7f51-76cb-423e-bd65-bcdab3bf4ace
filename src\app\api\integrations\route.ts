import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/integrations - List integrations for a project
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    const integrations = await prisma.integration.findMany({
      where: {
        projectId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Parse config JSON for response
    const integrationsWithConfig = integrations.map(integration => ({
      ...integration,
      config: integration.config ? JSON.parse(integration.config) : {}
    }));

    return NextResponse.json({
      success: true,
      data: integrationsWithConfig
    });

  } catch (error) {
    console.error('Integrations fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch integrations' },
      { status: 500 }
    );
  }
}

// POST /api/integrations - Create new integration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      projectId,
      type,
      name,
      config,
      isActive = true
    } = body;

    if (!projectId || !type || !name) {
      return NextResponse.json(
        { error: 'Project ID, type, and name are required' },
        { status: 400 }
      );
    }

    // Validate integration type
    const validTypes = ['HUBSPOT', 'SALESFORCE', 'MAILCHIMP', 'ZAPIER', 'WEBHOOK'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid integration type' },
        { status: 400 }
      );
    }

    // Validate config based on type
    const validationResult = validateIntegrationConfig(type, config);
    if (!validationResult.valid) {
      return NextResponse.json(
        { error: validationResult.message },
        { status: 400 }
      );
    }

    const integration = await prisma.integration.create({
      data: {
        projectId,
        type,
        name,
        config: JSON.stringify(config || {}),
        isActive,
        lastSync: null
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        ...integration,
        config: integration.config ? JSON.parse(integration.config) : {}
      }
    });

  } catch (error) {
    console.error('Integration creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create integration' },
      { status: 500 }
    );
  }
}

function validateIntegrationConfig(type: string, config: any) {
  switch (type) {
    case 'HUBSPOT':
      if (!config?.accessToken && !config?.apiKey) {
        return {
          valid: false,
          message: 'HubSpot integration requires accessToken or apiKey'
        };
      }
      break;

    case 'SALESFORCE':
      if (!config?.clientId || !config?.clientSecret || !config?.refreshToken) {
        return {
          valid: false,
          message: 'Salesforce integration requires clientId, clientSecret, and refreshToken'
        };
      }
      break;

    case 'MAILCHIMP':
      if (!config?.apiKey || !config?.listId) {
        return {
          valid: false,
          message: 'Mailchimp integration requires apiKey and listId'
        };
      }
      break;

    case 'ZAPIER':
      if (!config?.webhookUrl) {
        return {
          valid: false,
          message: 'Zapier integration requires webhookUrl'
        };
      }
      break;

    case 'WEBHOOK':
      if (!config?.url) {
        return {
          valid: false,
          message: 'Webhook integration requires url'
        };
      }
      break;

    default:
      return {
        valid: false,
        message: 'Unknown integration type'
      };
  }

  return { valid: true, message: 'Valid configuration' };
}
