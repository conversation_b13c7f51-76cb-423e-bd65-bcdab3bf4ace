"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/integrations/new/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/integrations/new/page.tsx":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/integrations/new/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewIntegrationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction NewIntegrationPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: '',\n        name: '',\n        config: {}\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHubSpotInfo, setShowHubSpotInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const integrationTypes = [\n        {\n            value: 'HUBSPOT',\n            label: 'HubSpot CRM',\n            icon: '🧡'\n        },\n        {\n            value: 'SALESFORCE',\n            label: 'Salesforce',\n            icon: '☁️'\n        },\n        {\n            value: 'MAILCHIMP',\n            label: 'Mailchimp',\n            icon: '🐵'\n        },\n        {\n            value: 'ZAPIER',\n            label: 'Zapier',\n            icon: '⚡'\n        },\n        {\n            value: 'WEBHOOK',\n            label: 'Custom Webhook',\n            icon: '🔗'\n        }\n    ];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const response = await fetch('/api/integrations', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'X-API-Key': 'sfd_demo_key_12345'\n                },\n                body: JSON.stringify({\n                    projectId: 'demo-project-id',\n                    ...formData\n                })\n            });\n            if (response.ok) {\n                router.push('/dashboard/integrations');\n            } else {\n                const error = await response.json();\n                alert(\"Failed to create integration: \".concat(error.error));\n            }\n        } catch (error) {\n            alert('Failed to create integration');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const renderConfigForm = ()=>{\n        switch(formData.type){\n            case 'HUBSPOT':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 border border-blue-200 rounded-md p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5 text-blue-400\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    fill: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Need help getting your HubSpot credentials?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowHubSpotInfo(true),\n                                        className: \"text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                                        children: \"View Instructions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Access Token\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.accessToken || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    accessToken: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Your HubSpot access token\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Portal ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.portalId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    portalId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Your HubSpot portal ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this);\n            case 'SALESFORCE':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Client ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.clientId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    clientId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce client ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Client Secret\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.clientSecret || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    clientSecret: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce client secret\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Refresh Token\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.refreshToken || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    refreshToken: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Salesforce refresh token\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this);\n            case 'MAILCHIMP':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"API Key\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    value: formData.config.apiKey || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    apiKey: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Mailchimp API key\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"List ID\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.listId || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    listId: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Enter your Mailchimp list ID\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this);\n            case 'ZAPIER':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                children: \"Webhook URL\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"url\",\n                                value: formData.config.webhookUrl || '',\n                                onChange: (e)=>setFormData((prev)=>({\n                                            ...prev,\n                                            config: {\n                                                ...prev.config,\n                                                webhookUrl: e.target.value\n                                            }\n                                        })),\n                                className: \"input-field\",\n                                placeholder: \"https://hooks.zapier.com/hooks/catch/...\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this);\n            case 'WEBHOOK':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Webhook URL\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"url\",\n                                    value: formData.config.url || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    url: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"https://your-webhook-endpoint.com/webhook\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"HTTP Method\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: formData.config.method || 'POST',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    method: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"POST\",\n                                            children: \"POST\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PUT\",\n                                            children: \"PUT\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"PATCH\",\n                                            children: \"PATCH\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Authorization Header (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: formData.config.authHeader || '',\n                                    onChange: (e)=>setFormData((prev)=>({\n                                                ...prev,\n                                                config: {\n                                                    ...prev.config,\n                                                    authHeader: e.target.value\n                                                }\n                                            })),\n                                    className: \"input-field\",\n                                    placeholder: \"Bearer your-token or Basic base64-credentials\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-500\",\n                    children: \"Please select an integration type to configure\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Add New Integration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Connect SmartForm Defender to your favorite tools\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 299,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"Choose Integration Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: integrationTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 \".concat(formData.type === type.value ? 'border-blue-500 bg-blue-50' : 'border-gray-300'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    name: \"type\",\n                                                    value: type.value,\n                                                    checked: formData.type === type.value,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                type: e.target.value,\n                                                                config: {}\n                                                            })),\n                                                    className: \"sr-only\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: type.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: type.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, type.value, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        formData.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                    children: \"Integration Details\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Integration Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: formData.name,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    })),\n                                            className: \"input-field\",\n                                            placeholder: \"e.g., Production HubSpot, Marketing Webhook\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this),\n                                renderConfigForm()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this),\n                        formData.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>router.back(),\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                    children: loading ? 'Creating...' : 'Create Integration'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n            lineNumber: 298,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\new\\\\page.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(NewIntegrationPage, \"G/I1Bp5x6ZM+ggsZwhhlgbv8C1s=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewIntegrationPage;\nvar _c;\n$RefreshReg$(_c, \"NewIntegrationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ludGVncmF0aW9ucy9uZXcvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFaUM7QUFDVztBQUNlO0FBRTVDLFNBQVNHOztJQUN0QixNQUFNQyxTQUFTSCwwREFBU0E7SUFDeEIsTUFBTSxDQUFDSSxVQUFVQyxZQUFZLEdBQUdOLCtDQUFRQSxDQUFDO1FBQ3ZDTyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsUUFBUSxDQUFDO0lBQ1g7SUFDQSxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxpQkFBaUJDLG1CQUFtQixHQUFHYiwrQ0FBUUEsQ0FBQztJQUV2RCxNQUFNYyxtQkFBbUI7UUFDdkI7WUFBRUMsT0FBTztZQUFXQyxPQUFPO1lBQWVDLE1BQU07UUFBSztRQUNyRDtZQUFFRixPQUFPO1lBQWNDLE9BQU87WUFBY0MsTUFBTTtRQUFLO1FBQ3ZEO1lBQUVGLE9BQU87WUFBYUMsT0FBTztZQUFhQyxNQUFNO1FBQUs7UUFDckQ7WUFBRUYsT0FBTztZQUFVQyxPQUFPO1lBQVVDLE1BQU07UUFBSTtRQUM5QztZQUFFRixPQUFPO1lBQVdDLE9BQU87WUFBa0JDLE1BQU07UUFBSztLQUN6RDtJQUVELE1BQU1DLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFDaEJULFdBQVc7UUFFWCxJQUFJO1lBQ0YsTUFBTVUsV0FBVyxNQUFNQyxNQUFNLHFCQUFxQjtnQkFDaERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixhQUFhO2dCQUNmO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CQyxXQUFXO29CQUNYLEdBQUd2QixRQUFRO2dCQUNiO1lBQ0Y7WUFFQSxJQUFJZ0IsU0FBU1EsRUFBRSxFQUFFO2dCQUNmekIsT0FBTzBCLElBQUksQ0FBQztZQUNkLE9BQU87Z0JBQ0wsTUFBTUMsUUFBUSxNQUFNVixTQUFTVyxJQUFJO2dCQUNqQ0MsTUFBTSxpQ0FBNkMsT0FBWkYsTUFBTUEsS0FBSztZQUNwRDtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkRSxNQUFNO1FBQ1IsU0FBVTtZQUNSdEIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNdUIsbUJBQW1CO1FBQ3ZCLE9BQVE3QixTQUFTRSxJQUFJO1lBQ25CLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUM0QjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNDO29EQUFJRCxXQUFVO29EQUF3QkUsU0FBUTtvREFBWUMsTUFBSzs4REFDOUQsNEVBQUNDO3dEQUFLQyxVQUFTO3dEQUFVQyxHQUFFO3dEQUFtSUMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OzswREFHM0ssOERBQUNSO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDUTtvREFBRVIsV0FBVTs4REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUt6Qyw4REFBQ1M7d0NBQ0N0QyxNQUFLO3dDQUNMdUMsU0FBUyxJQUFNakMsbUJBQW1CO3dDQUNsQ3VCLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1MLDhEQUFDRDs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDVztvQ0FDQ3hDLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ3VDLFdBQVcsSUFBSTtvQ0FDdENDLFVBQVUsQ0FBQzlCLElBQU1iLFlBQVk0QyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQekMsUUFBUTtvREFBRSxHQUFHeUMsS0FBS3pDLE1BQU07b0RBQUV1QyxhQUFhN0IsRUFBRWdDLE1BQU0sQ0FBQ3BDLEtBQUs7Z0RBQUM7NENBQ3hEO29DQUNBcUIsV0FBVTtvQ0FDVmdCLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OztzQ0FHWiw4REFBQ2xCOzs4Q0FDQyw4REFBQ25CO29DQUFNb0IsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNXO29DQUNDeEMsTUFBSztvQ0FDTFEsT0FBT1YsU0FBU0ksTUFBTSxDQUFDNkMsUUFBUSxJQUFJO29DQUNuQ0wsVUFBVSxDQUFDOUIsSUFBTWIsWUFBWTRDLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1B6QyxRQUFRO29EQUFFLEdBQUd5QyxLQUFLekMsTUFBTTtvREFBRTZDLFVBQVVuQyxFQUFFZ0MsTUFBTSxDQUFDcEMsS0FBSztnREFBQzs0Q0FDckQ7b0NBQ0FxQixXQUFVO29DQUNWZ0IsYUFBWTtvQ0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTWxCLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNsQjtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ25CO29DQUFNb0IsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNXO29DQUNDeEMsTUFBSztvQ0FDTFEsT0FBT1YsU0FBU0ksTUFBTSxDQUFDOEMsUUFBUSxJQUFJO29DQUNuQ04sVUFBVSxDQUFDOUIsSUFBTWIsWUFBWTRDLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1B6QyxRQUFRO29EQUFFLEdBQUd5QyxLQUFLekMsTUFBTTtvREFBRThDLFVBQVVwQyxFQUFFZ0MsTUFBTSxDQUFDcEMsS0FBSztnREFBQzs0Q0FDckQ7b0NBQ0FxQixXQUFVO29DQUNWZ0IsYUFBWTtvQ0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7O3NDQUdaLDhEQUFDbEI7OzhDQUNDLDhEQUFDbkI7b0NBQU1vQixXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ1c7b0NBQ0N4QyxNQUFLO29DQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUMrQyxZQUFZLElBQUk7b0NBQ3ZDUCxVQUFVLENBQUM5QixJQUFNYixZQUFZNEMsQ0FBQUEsT0FBUztnREFDcEMsR0FBR0EsSUFBSTtnREFDUHpDLFFBQVE7b0RBQUUsR0FBR3lDLEtBQUt6QyxNQUFNO29EQUFFK0MsY0FBY3JDLEVBQUVnQyxNQUFNLENBQUNwQyxLQUFLO2dEQUFDOzRDQUN6RDtvQ0FDQXFCLFdBQVU7b0NBQ1ZnQixhQUFZO29DQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7c0NBR1osOERBQUNsQjs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDVztvQ0FDQ3hDLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ2dELFlBQVksSUFBSTtvQ0FDdkNSLFVBQVUsQ0FBQzlCLElBQU1iLFlBQVk0QyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQekMsUUFBUTtvREFBRSxHQUFHeUMsS0FBS3pDLE1BQU07b0RBQUVnRCxjQUFjdEMsRUFBRWdDLE1BQU0sQ0FBQ3BDLEtBQUs7Z0RBQUM7NENBQ3pEO29DQUNBcUIsV0FBVTtvQ0FDVmdCLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1sQixLQUFLO2dCQUNILHFCQUNFLDhEQUFDbEI7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDVztvQ0FDQ3hDLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ2lELE1BQU0sSUFBSTtvQ0FDakNULFVBQVUsQ0FBQzlCLElBQU1iLFlBQVk0QyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQekMsUUFBUTtvREFBRSxHQUFHeUMsS0FBS3pDLE1BQU07b0RBQUVpRCxRQUFRdkMsRUFBRWdDLE1BQU0sQ0FBQ3BDLEtBQUs7Z0RBQUM7NENBQ25EO29DQUNBcUIsV0FBVTtvQ0FDVmdCLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OztzQ0FHWiw4REFBQ2xCOzs4Q0FDQyw4REFBQ25CO29DQUFNb0IsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNXO29DQUNDeEMsTUFBSztvQ0FDTFEsT0FBT1YsU0FBU0ksTUFBTSxDQUFDa0QsTUFBTSxJQUFJO29DQUNqQ1YsVUFBVSxDQUFDOUIsSUFBTWIsWUFBWTRDLENBQUFBLE9BQVM7Z0RBQ3BDLEdBQUdBLElBQUk7Z0RBQ1B6QyxRQUFRO29EQUFFLEdBQUd5QyxLQUFLekMsTUFBTTtvREFBRWtELFFBQVF4QyxFQUFFZ0MsTUFBTSxDQUFDcEMsS0FBSztnREFBQzs0Q0FDbkQ7b0NBQ0FxQixXQUFVO29DQUNWZ0IsYUFBWTtvQ0FDWkMsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTWxCLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNsQjtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7OzBDQUNDLDhEQUFDbkI7Z0NBQU1vQixXQUFVOzBDQUErQzs7Ozs7OzBDQUdoRSw4REFBQ1c7Z0NBQ0N4QyxNQUFLO2dDQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUNtRCxVQUFVLElBQUk7Z0NBQ3JDWCxVQUFVLENBQUM5QixJQUFNYixZQUFZNEMsQ0FBQUEsT0FBUzs0Q0FDcEMsR0FBR0EsSUFBSTs0Q0FDUHpDLFFBQVE7Z0RBQUUsR0FBR3lDLEtBQUt6QyxNQUFNO2dEQUFFbUQsWUFBWXpDLEVBQUVnQyxNQUFNLENBQUNwQyxLQUFLOzRDQUFDO3dDQUN2RDtnQ0FDQXFCLFdBQVU7Z0NBQ1ZnQixhQUFZO2dDQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OztZQU1sQixLQUFLO2dCQUNILHFCQUNFLDhEQUFDbEI7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNuQjtvQ0FBTW9CLFdBQVU7OENBQStDOzs7Ozs7OENBR2hFLDhEQUFDVztvQ0FDQ3hDLE1BQUs7b0NBQ0xRLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ29ELEdBQUcsSUFBSTtvQ0FDOUJaLFVBQVUsQ0FBQzlCLElBQU1iLFlBQVk0QyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQekMsUUFBUTtvREFBRSxHQUFHeUMsS0FBS3pDLE1BQU07b0RBQUVvRCxLQUFLMUMsRUFBRWdDLE1BQU0sQ0FBQ3BDLEtBQUs7Z0RBQUM7NENBQ2hEO29DQUNBcUIsV0FBVTtvQ0FDVmdCLGFBQVk7b0NBQ1pDLFFBQVE7Ozs7Ozs7Ozs7OztzQ0FHWiw4REFBQ2xCOzs4Q0FDQyw4REFBQ25CO29DQUFNb0IsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUMwQjtvQ0FDQy9DLE9BQU9WLFNBQVNJLE1BQU0sQ0FBQ2MsTUFBTSxJQUFJO29DQUNqQzBCLFVBQVUsQ0FBQzlCLElBQU1iLFlBQVk0QyxDQUFBQSxPQUFTO2dEQUNwQyxHQUFHQSxJQUFJO2dEQUNQekMsUUFBUTtvREFBRSxHQUFHeUMsS0FBS3pDLE1BQU07b0RBQUVjLFFBQVFKLEVBQUVnQyxNQUFNLENBQUNwQyxLQUFLO2dEQUFDOzRDQUNuRDtvQ0FDQXFCLFdBQVU7O3NEQUVWLDhEQUFDMkI7NENBQU9oRCxPQUFNO3NEQUFPOzs7Ozs7c0RBQ3JCLDhEQUFDZ0Q7NENBQU9oRCxPQUFNO3NEQUFNOzs7Ozs7c0RBQ3BCLDhEQUFDZ0Q7NENBQU9oRCxPQUFNO3NEQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBRzFCLDhEQUFDb0I7OzhDQUNDLDhEQUFDbkI7b0NBQU1vQixXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ1c7b0NBQ0N4QyxNQUFLO29DQUNMUSxPQUFPVixTQUFTSSxNQUFNLENBQUN1RCxVQUFVLElBQUk7b0NBQ3JDZixVQUFVLENBQUM5QixJQUFNYixZQUFZNEMsQ0FBQUEsT0FBUztnREFDcEMsR0FBR0EsSUFBSTtnREFDUHpDLFFBQVE7b0RBQUUsR0FBR3lDLEtBQUt6QyxNQUFNO29EQUFFdUQsWUFBWTdDLEVBQUVnQyxNQUFNLENBQUNwQyxLQUFLO2dEQUFDOzRDQUN2RDtvQ0FDQXFCLFdBQVU7b0NBQ1ZnQixhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNdEI7Z0JBQ0UscUJBQ0UsOERBQUNqQjtvQkFBSUMsV0FBVTs4QkFBaUM7Ozs7OztRQUl0RDtJQUNGO0lBRUEscUJBQ0UsOERBQUNsQyxtRUFBZUE7a0JBQ2QsNEVBQUNpQztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDNkI7NEJBQUc3QixXQUFVO3NDQUFtQzs7Ozs7O3NDQUNqRCw4REFBQ1E7NEJBQUVSLFdBQVU7c0NBQXFCOzs7Ozs7Ozs7Ozs7OEJBR3BDLDhEQUFDOEI7b0JBQUtDLFVBQVVqRDtvQkFBY2tCLFdBQVU7O3NDQUV0Qyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDZ0M7b0NBQUdoQyxXQUFVOzhDQUF5Qzs7Ozs7OzhDQUN2RCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1p0QixpQkFBaUJ1RCxHQUFHLENBQUMsQ0FBQzlELHFCQUNyQiw4REFBQ1M7NENBRUNvQixXQUFXLG9GQUlWLE9BSEMvQixTQUFTRSxJQUFJLEtBQUtBLEtBQUtRLEtBQUssR0FDeEIsK0JBQ0E7OzhEQUdOLDhEQUFDZ0M7b0RBQ0N4QyxNQUFLO29EQUNMQyxNQUFLO29EQUNMTyxPQUFPUixLQUFLUSxLQUFLO29EQUNqQnVELFNBQVNqRSxTQUFTRSxJQUFJLEtBQUtBLEtBQUtRLEtBQUs7b0RBQ3JDa0MsVUFBVSxDQUFDOUIsSUFBTWIsWUFBWTRDLENBQUFBLE9BQVM7Z0VBQUUsR0FBR0EsSUFBSTtnRUFBRTNDLE1BQU1ZLEVBQUVnQyxNQUFNLENBQUNwQyxLQUFLO2dFQUFFTixRQUFRLENBQUM7NERBQUU7b0RBQ2xGMkIsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDbUM7b0RBQUtuQyxXQUFVOzhEQUFpQjdCLEtBQUtVLElBQUk7Ozs7Ozs4REFDMUMsOERBQUNzRDtvREFBS25DLFdBQVU7OERBQXFDN0IsS0FBS1MsS0FBSzs7Ozs7OzsyQ0FoQjFEVCxLQUFLUSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7O3dCQXVCdEJWLFNBQVNFLElBQUksa0JBQ1osOERBQUM0Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNnQztvQ0FBR2hDLFdBQVU7OENBQXlDOzs7Ozs7OENBQ3ZELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNwQjs0Q0FBTW9CLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDVzs0Q0FDQ3hDLE1BQUs7NENBQ0xRLE9BQU9WLFNBQVNHLElBQUk7NENBQ3BCeUMsVUFBVSxDQUFDOUIsSUFBTWIsWUFBWTRDLENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRTFDLE1BQU1XLEVBQUVnQyxNQUFNLENBQUNwQyxLQUFLO29EQUFDOzRDQUN0RXFCLFdBQVU7NENBQ1ZnQixhQUFZOzRDQUNaQyxRQUFROzs7Ozs7Ozs7Ozs7Z0NBR1huQjs7Ozs7Ozt3QkFLSjdCLFNBQVNFLElBQUksa0JBQ1osOERBQUM0Qjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNTO29DQUNDdEMsTUFBSztvQ0FDTHVDLFNBQVMsSUFBTTFDLE9BQU9vRSxJQUFJO29DQUMxQnBDLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ1M7b0NBQ0N0QyxNQUFLO29DQUNMa0UsVUFBVS9EO29DQUNWMEIsV0FBVTs4Q0FFVDFCLFVBQVUsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVEzQztHQWxYd0JQOztRQUNQRixzREFBU0E7OztLQURGRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqZXNzZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxTbWFydEZvcm1EZWZlbmRlclxcc3JjXFxhcHBcXGRhc2hib2FyZFxcaW50ZWdyYXRpb25zXFxuZXdcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IERhc2hib2FyZExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvRGFzaGJvYXJkTGF5b3V0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTmV3SW50ZWdyYXRpb25QYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgdHlwZTogJycsXG4gICAgbmFtZTogJycsXG4gICAgY29uZmlnOiB7fVxuICB9KTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0h1YlNwb3RJbmZvLCBzZXRTaG93SHViU3BvdEluZm9dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IGludGVncmF0aW9uVHlwZXMgPSBbXG4gICAgeyB2YWx1ZTogJ0hVQlNQT1QnLCBsYWJlbDogJ0h1YlNwb3QgQ1JNJywgaWNvbjogJ/Cfp6EnIH0sXG4gICAgeyB2YWx1ZTogJ1NBTEVTRk9SQ0UnLCBsYWJlbDogJ1NhbGVzZm9yY2UnLCBpY29uOiAn4piB77iPJyB9LFxuICAgIHsgdmFsdWU6ICdNQUlMQ0hJTVAnLCBsYWJlbDogJ01haWxjaGltcCcsIGljb246ICfwn5C1JyB9LFxuICAgIHsgdmFsdWU6ICdaQVBJRVInLCBsYWJlbDogJ1phcGllcicsIGljb246ICfimqEnIH0sXG4gICAgeyB2YWx1ZTogJ1dFQkhPT0snLCBsYWJlbDogJ0N1c3RvbSBXZWJob29rJywgaWNvbjogJ/CflJcnIH1cbiAgXTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9pbnRlZ3JhdGlvbnMnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAnWC1BUEktS2V5JzogJ3NmZF9kZW1vX2tleV8xMjM0NSdcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHByb2plY3RJZDogJ2RlbW8tcHJvamVjdC1pZCcsXG4gICAgICAgICAgLi4uZm9ybURhdGFcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQvaW50ZWdyYXRpb25zJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgYWxlcnQoYEZhaWxlZCB0byBjcmVhdGUgaW50ZWdyYXRpb246ICR7ZXJyb3IuZXJyb3J9YCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGFsZXJ0KCdGYWlsZWQgdG8gY3JlYXRlIGludGVncmF0aW9uJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZW5kZXJDb25maWdGb3JtID0gKCkgPT4ge1xuICAgIHN3aXRjaCAoZm9ybURhdGEudHlwZSkge1xuICAgICAgY2FzZSAnSFVCU1BPVCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbWQgcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNDAwXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTE4IDEwYTggOCAwIDExLTE2IDAgOCA4IDAgMDExNiAwem0tNy00YTEgMSAwIDExLTIgMCAxIDEgMCAwMTIgMHpNOSA5YTEgMSAwIDAwMCAydjNhMSAxIDAgMDAxIDFoMWExIDEgMCAxMDAtMnYtM2ExIDEgMCAwMC0xLTFIOXpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIE5lZWQgaGVscCBnZXR0aW5nIHlvdXIgSHViU3BvdCBjcmVkZW50aWFscz9cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93SHViU3BvdEluZm8odHJ1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDAgdGV4dC1zbSBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgVmlldyBJbnN0cnVjdGlvbnNcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgQWNjZXNzIFRva2VuXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJwYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy5hY2Nlc3NUb2tlbiB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGFjY2Vzc1Rva2VuOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgSHViU3BvdCBhY2Nlc3MgdG9rZW5cIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIFBvcnRhbCBJRFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy5wb3J0YWxJZCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIHBvcnRhbElkOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIllvdXIgSHViU3BvdCBwb3J0YWwgSURcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcblxuICAgICAgY2FzZSAnU0FMRVNGT1JDRSc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIENsaWVudCBJRFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy5jbGllbnRJZCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGNsaWVudElkOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgU2FsZXNmb3JjZSBjbGllbnQgSURcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIENsaWVudCBTZWNyZXRcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmNsaWVudFNlY3JldCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGNsaWVudFNlY3JldDogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIFNhbGVzZm9yY2UgY2xpZW50IHNlY3JldFwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgUmVmcmVzaCBUb2tlblxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb25maWcucmVmcmVzaFRva2VuIHx8ICcnfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgIGNvbmZpZzogeyAuLi5wcmV2LmNvbmZpZywgcmVmcmVzaFRva2VuOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgU2FsZXNmb3JjZSByZWZyZXNoIHRva2VuXCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG5cbiAgICAgIGNhc2UgJ01BSUxDSElNUCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIEFQSSBLZXlcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmFwaUtleSB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGFwaUtleTogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIE1haWxjaGltcCBBUEkga2V5XCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICBMaXN0IElEXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmxpc3RJZCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIGxpc3RJZDogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciB5b3VyIE1haWxjaGltcCBsaXN0IElEXCJcbiAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICk7XG5cbiAgICAgIGNhc2UgJ1pBUElFUic6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIFdlYmhvb2sgVVJMXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb25maWcud2ViaG9va1VybCB8fCAnJ31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICBjb25maWc6IHsgLi4ucHJldi5jb25maWcsIHdlYmhvb2tVcmw6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGRcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly9ob29rcy56YXBpZXIuY29tL2hvb2tzL2NhdGNoLy4uLlwiXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuXG4gICAgICBjYXNlICdXRUJIT09LJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgV2ViaG9vayBVUkxcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInVybFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbmZpZy51cmwgfHwgJyd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgY29uZmlnOiB7IC4uLnByZXYuY29uZmlnLCB1cmw6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGRcIlxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiaHR0cHM6Ly95b3VyLXdlYmhvb2stZW5kcG9pbnQuY29tL3dlYmhvb2tcIlxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgIEhUVFAgTWV0aG9kXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLm1ldGhvZCB8fCAnUE9TVCd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgY29uZmlnOiB7IC4uLnByZXYuY29uZmlnLCBtZXRob2Q6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5wdXQtZmllbGRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBPU1RcIj5QT1NUPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBVVFwiPlBVVDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQQVRDSFwiPlBBVENIPC9vcHRpb24+XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICBBdXRob3JpemF0aW9uIEhlYWRlciAoT3B0aW9uYWwpXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29uZmlnLmF1dGhIZWFkZXIgfHwgJyd9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgY29uZmlnOiB7IC4uLnByZXYuY29uZmlnLCBhdXRoSGVhZGVyOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlucHV0LWZpZWxkXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJlYXJlciB5b3VyLXRva2VuIG9yIEJhc2ljIGJhc2U2NC1jcmVkZW50aWFsc1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKTtcblxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTggdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgUGxlYXNlIHNlbGVjdCBhbiBpbnRlZ3JhdGlvbiB0eXBlIHRvIGNvbmZpZ3VyZVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxEYXNoYm9hcmRMYXlvdXQ+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTJ4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi04XCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+QWRkIE5ldyBJbnRlZ3JhdGlvbjwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0yXCI+Q29ubmVjdCBTbWFydEZvcm0gRGVmZW5kZXIgdG8geW91ciBmYXZvcml0ZSB0b29sczwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgey8qIEludGVncmF0aW9uIFR5cGUgU2VsZWN0aW9uICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5DaG9vc2UgSW50ZWdyYXRpb24gVHlwZTwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge2ludGVncmF0aW9uVHlwZXMubWFwKCh0eXBlKSA9PiAoXG4gICAgICAgICAgICAgICAgPGxhYmVsXG4gICAgICAgICAgICAgICAgICBrZXk9e3R5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBmbGV4IGl0ZW1zLWNlbnRlciBwLTQgYm9yZGVyIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgaG92ZXI6YmctZ3JheS01MCAke1xuICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS50eXBlID09PSB0eXBlLnZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIGJnLWJsdWUtNTAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYWRpb1wiXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJ0eXBlXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3R5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLnR5cGUgPT09IHR5cGUudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCB0eXBlOiBlLnRhcmdldC52YWx1ZSwgY29uZmlnOiB7fSB9KSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHlcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIG1yLTNcIj57dHlwZS5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPnt0eXBlLmxhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEludGVncmF0aW9uIE5hbWUgKi99XG4gICAgICAgICAge2Zvcm1EYXRhLnR5cGUgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi00XCI+SW50ZWdyYXRpb24gRGV0YWlsczwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgSW50ZWdyYXRpb24gTmFtZVxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC1maWVsZFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIFByb2R1Y3Rpb24gSHViU3BvdCwgTWFya2V0aW5nIFdlYmhvb2tcIlxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAge3JlbmRlckNvbmZpZ0Zvcm0oKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogU3VibWl0IEJ1dHRvbiAqL31cbiAgICAgICAgICB7Zm9ybURhdGEudHlwZSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdDcmVhdGluZy4uLicgOiAnQ3JlYXRlIEludGVncmF0aW9uJ31cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsIkRhc2hib2FyZExheW91dCIsIk5ld0ludGVncmF0aW9uUGFnZSIsInJvdXRlciIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJ0eXBlIiwibmFtZSIsImNvbmZpZyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2hvd0h1YlNwb3RJbmZvIiwic2V0U2hvd0h1YlNwb3RJbmZvIiwiaW50ZWdyYXRpb25UeXBlcyIsInZhbHVlIiwibGFiZWwiLCJpY29uIiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInByb2plY3RJZCIsIm9rIiwicHVzaCIsImVycm9yIiwianNvbiIsImFsZXJ0IiwicmVuZGVyQ29uZmlnRm9ybSIsImRpdiIsImNsYXNzTmFtZSIsInN2ZyIsInZpZXdCb3giLCJmaWxsIiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwicCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJpbnB1dCIsImFjY2Vzc1Rva2VuIiwib25DaGFuZ2UiLCJwcmV2IiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsInBvcnRhbElkIiwiY2xpZW50SWQiLCJjbGllbnRTZWNyZXQiLCJyZWZyZXNoVG9rZW4iLCJhcGlLZXkiLCJsaXN0SWQiLCJ3ZWJob29rVXJsIiwidXJsIiwic2VsZWN0Iiwib3B0aW9uIiwiYXV0aEhlYWRlciIsImgxIiwiZm9ybSIsIm9uU3VibWl0IiwiaDIiLCJtYXAiLCJjaGVja2VkIiwic3BhbiIsImJhY2siLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/integrations/new/page.tsx\n"));

/***/ })

});