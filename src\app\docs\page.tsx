import { <PERSON>, <PERSON>, Zap, <PERSON>ting<PERSON>, BookOpen, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export default function DocsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <Shield className="h-8 w-8 text-primary-600" />
                <span className="ml-2 text-xl font-bold text-gray-900">SmartForm Defender</span>
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/#features" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Features
                </Link>
                <Link href="/#pricing" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Pricing
                </Link>
                <Link href="/docs" className="text-primary-600 px-3 py-2 rounded-md text-sm font-medium">
                  Docs
                </Link>
                <Link href="/dashboard" className="btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-24">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Documentation</h3>
              <nav className="space-y-2">
                <a href="#getting-started" className="block text-primary-600 hover:text-primary-700 py-1">
                  Getting Started
                </a>
                <a href="#installation" className="block text-gray-600 hover:text-gray-900 py-1">
                  Installation
                </a>
                <a href="#configuration" className="block text-gray-600 hover:text-gray-900 py-1">
                  Configuration
                </a>
                <a href="#integrations" className="block text-gray-600 hover:text-gray-900 py-1">
                  Integrations
                </a>
                <a href="#api-reference" className="block text-gray-600 hover:text-gray-900 py-1">
                  API Reference
                </a>
                <a href="#examples" className="block text-gray-600 hover:text-gray-900 py-1">
                  Examples
                </a>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              {/* Header */}
              <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                  SmartForm Defender Documentation
                </h1>
                <p className="text-xl text-gray-600">
                  Complete guide to implementing spam protection for your lead forms
                </p>
              </div>

              {/* Getting Started */}
              <section id="getting-started" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <BookOpen className="h-6 w-6 text-primary-600 mr-2" />
                  Getting Started
                </h2>
                <p className="text-gray-600 mb-6">
                  SmartForm Defender protects your lead forms from spam and fake submissions using AI-powered validation. 
                  Get started in under 5 minutes with our simple JavaScript snippet.
                </p>
                
                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Quick Setup</h3>
                  <ol className="list-decimal list-inside space-y-2 text-gray-600">
                    <li>Sign up for a SmartForm Defender account</li>
                    <li>Create a new project and get your API key</li>
                    <li>Add our JavaScript snippet to your website</li>
                    <li>Configure your form protection settings</li>
                  </ol>
                </div>
              </section>

              {/* Installation */}
              <section id="installation" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Code className="h-6 w-6 text-primary-600 mr-2" />
                  Installation
                </h2>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-3">JavaScript Snippet</h3>
                <p className="text-gray-600 mb-4">
                  Add this code to your website's &lt;head&gt; section or before the closing &lt;/body&gt; tag:
                </p>
                
                <div className="bg-gray-900 rounded-lg p-4 mb-6 overflow-x-auto">
                  <code className="text-green-400 text-sm">
{`<!-- SmartForm Defender -->
<script src="https://cdn.smartformdefender.com/sdk/v1/smartform.js"></script>
<script>
  SmartFormDefender.init({
    apiKey: 'your_api_key_here',
    projectId: 'your_project_id',
    endpoint: 'https://api.smartformdefender.com/validate/submission'
  });
</script>`}
                  </code>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-3">NPM Package</h3>
                <p className="text-gray-600 mb-4">
                  For modern JavaScript applications, install via npm:
                </p>
                
                <div className="bg-gray-900 rounded-lg p-4 mb-6">
                  <code className="text-green-400 text-sm">
                    npm install @smartform-defender/sdk
                  </code>
                </div>
              </section>

              {/* Configuration */}
              <section id="configuration" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Settings className="h-6 w-6 text-primary-600 mr-2" />
                  Configuration
                </h2>
                
                <p className="text-gray-600 mb-6">
                  Customize SmartForm Defender's behavior with these configuration options:
                </p>

                <div className="bg-gray-50 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Configuration Options</h3>
                  <div className="space-y-4">
                    <div>
                      <code className="bg-gray-200 px-2 py-1 rounded text-sm">apiKey</code>
                      <span className="text-gray-600 ml-2">Your unique API key (required)</span>
                    </div>
                    <div>
                      <code className="bg-gray-200 px-2 py-1 rounded text-sm">projectId</code>
                      <span className="text-gray-600 ml-2">Your project identifier (required)</span>
                    </div>
                    <div>
                      <code className="bg-gray-200 px-2 py-1 rounded text-sm">enableRealTimeValidation</code>
                      <span className="text-gray-600 ml-2">Enable real-time field validation (default: true)</span>
                    </div>
                    <div>
                      <code className="bg-gray-200 px-2 py-1 rounded text-sm">enableBotDetection</code>
                      <span className="text-gray-600 ml-2">Enable bot behavior detection (default: true)</span>
                    </div>
                    <div>
                      <code className="bg-gray-200 px-2 py-1 rounded text-sm">spamThreshold</code>
                      <span className="text-gray-600 ml-2">Spam confidence threshold 0-1 (default: 0.7)</span>
                    </div>
                  </div>
                </div>
              </section>

              {/* Integrations */}
              <section id="integrations" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Zap className="h-6 w-6 text-primary-600 mr-2" />
                  Integrations
                </h2>
                
                <p className="text-gray-600 mb-6">
                  SmartForm Defender integrates seamlessly with popular CRM and marketing platforms:
                </p>

                <div className="grid md:grid-cols-2 gap-6 mb-6">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🧡 HubSpot CRM</h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Direct integration with HubSpot forms and contact management
                    </p>
                    <Link href="/dashboard/integrations" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      Setup Guide →
                    </Link>
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">☁️ Salesforce</h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Connect with Salesforce leads and opportunities
                    </p>
                    <Link href="/dashboard/integrations" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      Setup Guide →
                    </Link>
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🐵 Mailchimp</h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Sync validated leads to your email marketing lists
                    </p>
                    <Link href="/dashboard/integrations" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      Setup Guide →
                    </Link>
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🔗 Custom Webhooks</h3>
                    <p className="text-gray-600 text-sm mb-3">
                      Send validated submissions to any endpoint
                    </p>
                    <Link href="/dashboard/integrations" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                      Setup Guide →
                    </Link>
                  </div>
                </div>
              </section>

              {/* API Reference */}
              <section id="api-reference" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">API Reference</h2>
                
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">
                    <ExternalLink className="h-5 w-5 inline mr-2" />
                    Complete API Documentation
                  </h3>
                  <p className="text-blue-800 mb-3">
                    For detailed API endpoints, request/response formats, and advanced integration options.
                  </p>
                  <Link 
                    href="/api-docs" 
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                  >
                    View Full API Docs
                    <ExternalLink className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </section>

              {/* Examples */}
              <section id="examples" className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Examples</h2>
                
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Basic Contact Form</h3>
                    <Link 
                      href="/demo" 
                      className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium"
                    >
                      View Live Demo
                      <ExternalLink className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">WordPress Integration</h3>
                    <p className="text-gray-600 mb-2">
                      Step-by-step guide for WordPress sites using Contact Form 7 or Gravity Forms.
                    </p>
                    <span className="text-gray-500 text-sm">Coming soon</span>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">React/Next.js Integration</h3>
                    <p className="text-gray-600 mb-2">
                      Modern JavaScript framework integration examples.
                    </p>
                    <span className="text-gray-500 text-sm">Coming soon</span>
                  </div>
                </div>
              </section>

              {/* Support */}
              <section className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-4">Need Help?</h2>
                <p className="text-gray-600 mb-4">
                  Can't find what you're looking for? Our support team is here to help.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/contact" className="btn-primary">
                    Contact Support
                  </Link>
                  <Link href="/demo" className="btn-secondary">
                    Try Live Demo
                  </Link>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-primary-400" />
              <span className="ml-2 text-xl font-bold">SmartForm Defender</span>
            </div>
            <div className="text-gray-400">
              © 2025 SmartForm Defender. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
