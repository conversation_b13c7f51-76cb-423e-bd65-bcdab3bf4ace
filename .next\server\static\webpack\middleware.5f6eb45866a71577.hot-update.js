"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth_middleware__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/middleware */ \"(middleware)/./node_modules/next-auth/middleware.js\");\n/* harmony import */ var next_auth_middleware__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_middleware__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_auth_middleware__WEBPACK_IMPORTED_MODULE_0__.withAuth)(function middleware(req) {\n    // Add any additional middleware logic here\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n}, {\n    callbacks: {\n        authorized: ({ token, req })=>{\n            // Check if user is authenticated for protected routes\n            const { pathname } = req.nextUrl;\n            // Public routes that don't require authentication\n            const publicRoutes = [\n                '/',\n                '/about',\n                '/contact',\n                '/docs',\n                '/demo',\n                '/api-docs',\n                '/auth/signin',\n                '/auth/signup',\n                '/auth/error',\n                '/api/auth',\n                '/api/validate',\n                '/api/webhook-test'\n            ];\n            // Admin routes that require admin role\n            const adminRoutes = [\n                '/admin'\n            ];\n            // Check if the current path is public\n            const isPublicRoute = publicRoutes.some((route)=>pathname.startsWith(route));\n            // Check if the current path is admin\n            const isAdminRoute = adminRoutes.some((route)=>pathname.startsWith(route));\n            // Allow access to public routes\n            if (isPublicRoute) {\n                return true;\n            }\n            // For admin routes, check if user has admin role\n            if (isAdminRoute) {\n                return !!token && token.role === 'ADMIN';\n            }\n            // Require authentication for all other routes\n            return !!token;\n        }\n    }\n}));\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */ '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});