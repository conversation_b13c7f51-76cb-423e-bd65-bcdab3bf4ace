"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/projects/[id]/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProjectConfigPage() {\n    var _project__count, _project__count1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const projectId = params.id;\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        domain: '',\n        description: '',\n        spamThreshold: 0.7,\n        enableEmailValidation: true,\n        enablePhoneValidation: true,\n        enableGeoValidation: false,\n        allowedCountries: '',\n        blockedCountries: '',\n        isActive: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectConfigPage.useEffect\": ()=>{\n            if (projectId) {\n                loadProject();\n            }\n        }\n    }[\"ProjectConfigPage.useEffect\"], [\n        projectId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectConfigPage.useEffect\": ()=>{\n            console.log('Config state updated:', config);\n        }\n    }[\"ProjectConfigPage.useEffect\"], [\n        config\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await fetch(\"/api/projects/\".concat(projectId));\n            if (response.ok) {\n                const data = await response.json();\n                const projectData = data.project;\n                setProject(projectData);\n                setConfig({\n                    name: projectData.name,\n                    domain: projectData.domain,\n                    description: projectData.description || '',\n                    spamThreshold: projectData.spamThreshold,\n                    enableEmailValidation: projectData.enableEmailValidation,\n                    enablePhoneValidation: projectData.enablePhoneValidation,\n                    enableGeoValidation: projectData.enableGeoValidation,\n                    allowedCountries: projectData.allowedCountries || '',\n                    blockedCountries: projectData.blockedCountries || '',\n                    isActive: projectData.isActive\n                });\n            } else {\n                console.error('Failed to load project:', response.status);\n            }\n        } catch (error) {\n            console.error('Failed to load project:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveProject = async ()=>{\n        setSaving(true);\n        try {\n            const response = await fetch(\"/api/projects/\".concat(projectId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(config)\n            });\n            if (response.ok) {\n                alert('Project updated successfully!');\n                loadProject(); // Refresh data\n            } else {\n                alert('Failed to update project');\n            }\n        } catch (error) {\n            console.error('Failed to save project:', error);\n            alert('Failed to update project');\n        } finally{\n            setSaving(false);\n        }\n    };\n    const generateJavaScriptSnippet = ()=>{\n        var _project_user_apiKeys_, _project_user_apiKeys, _project_user;\n        const apiKey = (project === null || project === void 0 ? void 0 : (_project_user = project.user) === null || _project_user === void 0 ? void 0 : (_project_user_apiKeys = _project_user.apiKeys) === null || _project_user_apiKeys === void 0 ? void 0 : (_project_user_apiKeys_ = _project_user_apiKeys[0]) === null || _project_user_apiKeys_ === void 0 ? void 0 : _project_user_apiKeys_.key) || 'your-api-key-here';\n        return '<!-- SmartForm Defender -->\\n<script src=\"https://cdn.smartformdefender.com/sdk/v1/smartform.js\"></script>\\n<script>\\n  SmartFormDefender.init({\\n    apiKey: \\''.concat(apiKey, \"',\\n    projectId: '\").concat(projectId, \"',\\n    endpoint: 'https://api.smartformdefender.com/validate/submission'\\n  });\\n</script>\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Project not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/dashboard/projects\",\n                        className: \"text-blue-600 hover:text-blue-500 mt-2 inline-block\",\n                        children: \"← Back to Projects\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/projects\",\n                                        className: \"text-blue-600 hover:text-blue-500 text-sm\",\n                                        children: \"← Back to Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mt-2\",\n                                        children: project.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Configure project settings and integration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/integrations\",\n                                        className: \"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\",\n                                        children: \"Manage Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/projects/\".concat(projectId, \"/analytics\"),\n                                        className: \"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700\",\n                                        children: \"View Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Basic Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Project Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.name,\n                                                            onChange: (e)=>{\n                                                                console.log('Name input changed to:', e.target.value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        name: e.target.value\n                                                                    }));\n                                                            },\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '10px',\n                                                                color: 'red'\n                                                            },\n                                                            children: [\n                                                                'Debug: name = \"',\n                                                                config.name,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Domain\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.domain,\n                                                            onChange: (e)=>{\n                                                                console.log('Domain input changed to:', e.target.value);\n                                                                setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        domain: e.target.value\n                                                                    }));\n                                                            },\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                                                            placeholder: \"example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '10px',\n                                                                color: 'red'\n                                                            },\n                                                            children: [\n                                                                'Debug: domain = \"',\n                                                                config.domain,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: config.description,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    rows: 3,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                                                    placeholder: \"Describe this project...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Spam Detection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            \"Spam Threshold (\",\n                                                            (config.spamThreshold * 100).toFixed(0),\n                                                            \"%)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        min: \"0\",\n                                                        max: \"1\",\n                                                        step: \"0.1\",\n                                                        value: config.spamThreshold,\n                                                        onChange: (e)=>setConfig((prev)=>({\n                                                                    ...prev,\n                                                                    spamThreshold: parseFloat(e.target.value)\n                                                                })),\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Lenient (0%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Strict (100%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Validation Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"emailValidation\",\n                                                            checked: config.enableEmailValidation,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        enableEmailValidation: e.target.checked\n                                                                    })),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"emailValidation\",\n                                                            className: \"ml-2 block text-sm text-gray-900\",\n                                                            children: \"Enable Email Validation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"phoneValidation\",\n                                                            checked: config.enablePhoneValidation,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        enablePhoneValidation: e.target.checked\n                                                                    })),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"phoneValidation\",\n                                                            className: \"ml-2 block text-sm text-gray-900\",\n                                                            children: \"Enable Phone Validation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"geoValidation\",\n                                                            checked: config.enableGeoValidation,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        enableGeoValidation: e.target.checked\n                                                                    })),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"geoValidation\",\n                                                            className: \"ml-2 block text-sm text-gray-900\",\n                                                            children: \"Enable Geographic Validation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.enableGeoValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Allowed Countries (comma-separated)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.allowedCountries,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        allowedCountries: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                                                            placeholder: \"US, CA, GB\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 285,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Blocked Countries (comma-separated)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.blockedCountries,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        blockedCountries: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\",\n                                                            placeholder: \"CN, RU\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveProject,\n                                        disabled: saving,\n                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                        children: saving ? 'Saving...' : 'Save Changes'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Installation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Add this code to your website's HTML, just before the closing </body> tag:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900 p-3 rounded text-xs font-mono overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-green-400\",\n                                                children: generateJavaScriptSnippet()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigator.clipboard.writeText(generateJavaScriptSnippet()),\n                                            className: \"mt-3 w-full px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"Copy Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Project Stats\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Total Submissions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_project__count = project._count) === null || _project__count === void 0 ? void 0 : _project__count.submissions) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Active Integrations:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_project__count1 = project._count) === null || _project__count1 === void 0 ? void 0 : _project__count1.integrations) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Status:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium \".concat(project.isActive ? 'text-green-600' : 'text-red-600'),\n                                                            children: project.isActive ? 'Active' : 'Inactive'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Created:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: new Date(project.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectConfigPage, \"67D5dI57/TCf75XNWT0n/G+MgZQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = ProjectConfigPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3Byb2plY3RzL1tpZF0vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2Y7QUFDZTtBQUU3QixTQUFTSTtRQW1WeUJDLGlCQUlBQTs7SUF0Vi9DLE1BQU1DLFNBQVNILDBEQUFTQTtJQUN4QixNQUFNSSxZQUFZRCxPQUFPRSxFQUFFO0lBRTNCLE1BQU0sQ0FBQ0gsU0FBU0ksV0FBVyxHQUFHVCwrQ0FBUUEsQ0FBTTtJQUM1QyxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ2MsUUFBUUMsVUFBVSxHQUFHZiwrQ0FBUUEsQ0FBQztRQUNuQ2dCLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsdUJBQXVCO1FBQ3ZCQyx1QkFBdUI7UUFDdkJDLHFCQUFxQjtRQUNyQkMsa0JBQWtCO1FBQ2xCQyxrQkFBa0I7UUFDbEJDLFVBQVU7SUFDWjtJQUVBeEIsZ0RBQVNBO3VDQUFDO1lBQ1IsSUFBSU0sV0FBVztnQkFDYm1CO1lBQ0Y7UUFDRjtzQ0FBRztRQUFDbkI7S0FBVTtJQUVkTixnREFBU0E7dUNBQUM7WUFDUjBCLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJkO1FBQ3ZDO3NDQUFHO1FBQUNBO0tBQU87SUFFWCxNQUFNWSxjQUFjO1FBQ2xCLElBQUk7WUFDRixNQUFNRyxXQUFXLE1BQU1DLE1BQU0saUJBQTJCLE9BQVZ2QjtZQUU5QyxJQUFJc0IsU0FBU0UsRUFBRSxFQUFFO2dCQUNmLE1BQU1DLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtnQkFDaEMsTUFBTUMsY0FBY0YsS0FBSzNCLE9BQU87Z0JBRWhDSSxXQUFXeUI7Z0JBQ1huQixVQUFVO29CQUNSQyxNQUFNa0IsWUFBWWxCLElBQUk7b0JBQ3RCQyxRQUFRaUIsWUFBWWpCLE1BQU07b0JBQzFCQyxhQUFhZ0IsWUFBWWhCLFdBQVcsSUFBSTtvQkFDeENDLGVBQWVlLFlBQVlmLGFBQWE7b0JBQ3hDQyx1QkFBdUJjLFlBQVlkLHFCQUFxQjtvQkFDeERDLHVCQUF1QmEsWUFBWWIscUJBQXFCO29CQUN4REMscUJBQXFCWSxZQUFZWixtQkFBbUI7b0JBQ3BEQyxrQkFBa0JXLFlBQVlYLGdCQUFnQixJQUFJO29CQUNsREMsa0JBQWtCVSxZQUFZVixnQkFBZ0IsSUFBSTtvQkFDbERDLFVBQVVTLFlBQVlULFFBQVE7Z0JBQ2hDO1lBQ0YsT0FBTztnQkFDTEUsUUFBUVEsS0FBSyxDQUFDLDJCQUEyQk4sU0FBU08sTUFBTTtZQUMxRDtRQUNGLEVBQUUsT0FBT0QsT0FBTztZQUNkUixRQUFRUSxLQUFLLENBQUMsMkJBQTJCQTtRQUMzQyxTQUFVO1lBQ1J4QixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU0wQixjQUFjO1FBQ2xCeEIsVUFBVTtRQUNWLElBQUk7WUFDRixNQUFNZ0IsV0FBVyxNQUFNQyxNQUFNLGlCQUEyQixPQUFWdkIsWUFBYTtnQkFDekQrQixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzVCO1lBQ3ZCO1lBRUEsSUFBSWUsU0FBU0UsRUFBRSxFQUFFO2dCQUNmWSxNQUFNO2dCQUNOakIsZUFBZSxlQUFlO1lBQ2hDLE9BQU87Z0JBQ0xpQixNQUFNO1lBQ1I7UUFDRixFQUFFLE9BQU9SLE9BQU87WUFDZFIsUUFBUVEsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekNRLE1BQU07UUFDUixTQUFVO1lBQ1I5QixVQUFVO1FBQ1o7SUFDRjtJQUVBLE1BQU0rQiw0QkFBNEI7WUFDakJ2Qyx3QkFBQUEsdUJBQUFBO1FBQWYsTUFBTXdDLFNBQVN4QyxDQUFBQSxvQkFBQUEsK0JBQUFBLGdCQUFBQSxRQUFTeUMsSUFBSSxjQUFiekMscUNBQUFBLHdCQUFBQSxjQUFlMEMsT0FBTyxjQUF0QjFDLDZDQUFBQSx5QkFBQUEscUJBQXdCLENBQUMsRUFBRSxjQUEzQkEsNkNBQUFBLHVCQUE2QjJDLEdBQUcsS0FBSTtRQUNuRCxPQUFPLG1LQUtPekMsT0FESHNDLFFBQU8sd0JBQ00sT0FBVnRDLFdBQVU7SUFJMUI7SUFFQSxJQUFJRyxTQUFTO1FBQ1gscUJBQ0UsOERBQUN1QztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxJQUFJLENBQUM3QyxTQUFTO1FBQ1oscUJBQ0UsOERBQUM0QztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUFHRCxXQUFVO2tDQUFtQzs7Ozs7O2tDQUNqRCw4REFBQ2hELGtEQUFJQTt3QkFBQ2tELE1BQUs7d0JBQXNCRixXQUFVO2tDQUFzRDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNekc7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDs7a0RBQ0MsOERBQUMvQyxrREFBSUE7d0NBQUNrRCxNQUFLO3dDQUFzQkYsV0FBVTtrREFBNEM7Ozs7OztrREFHdkYsOERBQUNHO3dDQUFHSCxXQUFVO2tEQUF5QzdDLFFBQVFXLElBQUk7Ozs7OztrREFDbkUsOERBQUNzQzt3Q0FBRUosV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FFL0IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2hELGtEQUFJQTt3Q0FDSGtELE1BQU87d0NBQ1BGLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ2hELGtEQUFJQTt3Q0FDSGtELE1BQU0sdUJBQWlDLE9BQVY3QyxXQUFVO3dDQUN2QzJDLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVQsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBR0QsV0FBVTtzREFBeUM7Ozs7OztzREFDdkQsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDTTs0REFBTUwsV0FBVTtzRUFBK0M7Ozs7OztzRUFHaEUsOERBQUNNOzREQUNDQyxNQUFLOzREQUNMQyxPQUFPNUMsT0FBT0UsSUFBSTs0REFDbEIyQyxVQUFVLENBQUNDO2dFQUNUakMsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQmdDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnRUFDcEQzQyxVQUFVK0MsQ0FBQUEsT0FBUzt3RUFBRSxHQUFHQSxJQUFJO3dFQUFFOUMsTUFBTTRDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvRUFBQzs0REFDckQ7NERBQ0FSLFdBQVU7Ozs7OztzRUFFWiw4REFBQ0Q7NERBQUljLE9BQU87Z0VBQUNDLFVBQVU7Z0VBQVFDLE9BQU87NERBQUs7O2dFQUFHO2dFQUFnQm5ELE9BQU9FLElBQUk7Z0VBQUM7Ozs7Ozs7Ozs7Ozs7OERBRTVFLDhEQUFDaUM7O3NFQUNDLDhEQUFDTTs0REFBTUwsV0FBVTtzRUFBK0M7Ozs7OztzRUFHaEUsOERBQUNNOzREQUNDQyxNQUFLOzREQUNMQyxPQUFPNUMsT0FBT0csTUFBTTs0REFDcEIwQyxVQUFVLENBQUNDO2dFQUNUakMsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QmdDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnRUFDdEQzQyxVQUFVK0MsQ0FBQUEsT0FBUzt3RUFBRSxHQUFHQSxJQUFJO3dFQUFFN0MsUUFBUTJDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvRUFBQzs0REFDdkQ7NERBQ0FSLFdBQVU7NERBQ1ZnQixhQUFZOzs7Ozs7c0VBRWQsOERBQUNqQjs0REFBSWMsT0FBTztnRUFBQ0MsVUFBVTtnRUFBUUMsT0FBTzs0REFBSzs7Z0VBQUc7Z0VBQWtCbkQsT0FBT0csTUFBTTtnRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFHbEYsOERBQUNnQzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNLO29EQUFNTCxXQUFVOzhEQUErQzs7Ozs7OzhEQUdoRSw4REFBQ2lCO29EQUNDVCxPQUFPNUMsT0FBT0ksV0FBVztvREFDekJ5QyxVQUFVLENBQUNDLElBQU03QyxVQUFVK0MsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFNUMsYUFBYTBDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFBQztvREFDM0VVLE1BQU07b0RBQ05sQixXQUFVO29EQUNWZ0IsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU1sQiw4REFBQ2pCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0M7NENBQUdELFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7O2tFQUNDLDhEQUFDTTt3REFBTUwsV0FBVTs7NERBQStDOzREQUM1Q3BDLENBQUFBLE9BQU9LLGFBQWEsR0FBRyxHQUFFLEVBQUdrRCxPQUFPLENBQUM7NERBQUc7Ozs7Ozs7a0VBRTNELDhEQUFDYjt3REFDQ0MsTUFBSzt3REFDTGEsS0FBSTt3REFDSkMsS0FBSTt3REFDSkMsTUFBSzt3REFDTGQsT0FBTzVDLE9BQU9LLGFBQWE7d0RBQzNCd0MsVUFBVSxDQUFDQyxJQUFNN0MsVUFBVStDLENBQUFBLE9BQVM7b0VBQUUsR0FBR0EsSUFBSTtvRUFBRTNDLGVBQWVzRCxXQUFXYixFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0VBQUU7d0RBQ3pGUixXQUFVOzs7Ozs7a0VBRVosOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ3dCOzBFQUFLOzs7Ozs7MEVBQ04sOERBQUNBOzBFQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPZCw4REFBQ3pCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0M7NENBQUdELFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ007NERBQ0NDLE1BQUs7NERBQ0xqRCxJQUFHOzREQUNIbUUsU0FBUzdELE9BQU9NLHFCQUFxQjs0REFDckN1QyxVQUFVLENBQUNDLElBQU03QyxVQUFVK0MsQ0FBQUEsT0FBUzt3RUFBRSxHQUFHQSxJQUFJO3dFQUFFMUMsdUJBQXVCd0MsRUFBRUMsTUFBTSxDQUFDYyxPQUFPO29FQUFDOzREQUN2RnpCLFdBQVU7Ozs7OztzRUFFWiw4REFBQ0s7NERBQU1xQixTQUFROzREQUFrQjFCLFdBQVU7c0VBQW1DOzs7Ozs7Ozs7Ozs7OERBSWhGLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNNOzREQUNDQyxNQUFLOzREQUNMakQsSUFBRzs0REFDSG1FLFNBQVM3RCxPQUFPTyxxQkFBcUI7NERBQ3JDc0MsVUFBVSxDQUFDQyxJQUFNN0MsVUFBVStDLENBQUFBLE9BQVM7d0VBQUUsR0FBR0EsSUFBSTt3RUFBRXpDLHVCQUF1QnVDLEVBQUVDLE1BQU0sQ0FBQ2MsT0FBTztvRUFBQzs0REFDdkZ6QixXQUFVOzs7Ozs7c0VBRVosOERBQUNLOzREQUFNcUIsU0FBUTs0REFBa0IxQixXQUFVO3NFQUFtQzs7Ozs7Ozs7Ozs7OzhEQUloRiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDTTs0REFDQ0MsTUFBSzs0REFDTGpELElBQUc7NERBQ0htRSxTQUFTN0QsT0FBT1EsbUJBQW1COzREQUNuQ3FDLFVBQVUsQ0FBQ0MsSUFBTTdDLFVBQVUrQyxDQUFBQSxPQUFTO3dFQUFFLEdBQUdBLElBQUk7d0VBQUV4QyxxQkFBcUJzQyxFQUFFQyxNQUFNLENBQUNjLE9BQU87b0VBQUM7NERBQ3JGekIsV0FBVTs7Ozs7O3NFQUVaLDhEQUFDSzs0REFBTXFCLFNBQVE7NERBQWdCMUIsV0FBVTtzRUFBbUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FNL0VwQyxPQUFPUSxtQkFBbUIsa0JBQ3pCLDhEQUFDMkI7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNNOzREQUFNTCxXQUFVO3NFQUErQzs7Ozs7O3NFQUdoRSw4REFBQ007NERBQ0NDLE1BQUs7NERBQ0xDLE9BQU81QyxPQUFPUyxnQkFBZ0I7NERBQzlCb0MsVUFBVSxDQUFDQyxJQUFNN0MsVUFBVStDLENBQUFBLE9BQVM7d0VBQUUsR0FBR0EsSUFBSTt3RUFBRXZDLGtCQUFrQnFDLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztvRUFBQzs0REFDaEZSLFdBQVU7NERBQ1ZnQixhQUFZOzs7Ozs7Ozs7Ozs7OERBR2hCLDhEQUFDakI7O3NFQUNDLDhEQUFDTTs0REFBTUwsV0FBVTtzRUFBK0M7Ozs7OztzRUFHaEUsOERBQUNNOzREQUNDQyxNQUFLOzREQUNMQyxPQUFPNUMsT0FBT1UsZ0JBQWdCOzREQUM5Qm1DLFVBQVUsQ0FBQ0MsSUFBTTdDLFVBQVUrQyxDQUFBQSxPQUFTO3dFQUFFLEdBQUdBLElBQUk7d0VBQUV0QyxrQkFBa0JvQyxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0VBQUM7NERBQ2hGUixXQUFVOzREQUNWZ0IsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVF0Qiw4REFBQ2pCO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDMkI7d0NBQ0NDLFNBQVN6Qzt3Q0FDVDBDLFVBQVVuRTt3Q0FDVnNDLFdBQVU7a0RBRVR0QyxTQUFTLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU05Qiw4REFBQ3FDOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDOEI7NENBQUc5QixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQ0k7NENBQUVKLFdBQVU7c0RBQTZCOzs7Ozs7c0RBRzFDLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQytCO2dEQUFJL0IsV0FBVTswREFBa0JOOzs7Ozs7Ozs7OztzREFFbkMsOERBQUNpQzs0Q0FDQ0MsU0FBUyxJQUFNSSxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ3hDOzRDQUM3Q00sV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQU1ILDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM4Qjs0Q0FBRzlCLFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3ZELDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3dCOzREQUFLeEIsV0FBVTtzRUFBZ0I7Ozs7OztzRUFDaEMsOERBQUN3Qjs0REFBS3hCLFdBQVU7c0VBQWU3QyxFQUFBQSxrQkFBQUEsUUFBUWdGLE1BQU0sY0FBZGhGLHNDQUFBQSxnQkFBZ0JpRixXQUFXLEtBQUk7Ozs7Ozs7Ozs7Ozs4REFFaEUsOERBQUNyQztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN3Qjs0REFBS3hCLFdBQVU7c0VBQWdCOzs7Ozs7c0VBQ2hDLDhEQUFDd0I7NERBQUt4QixXQUFVO3NFQUFlN0MsRUFBQUEsbUJBQUFBLFFBQVFnRixNQUFNLGNBQWRoRix1Q0FBQUEsaUJBQWdCa0YsWUFBWSxLQUFJOzs7Ozs7Ozs7Ozs7OERBRWpFLDhEQUFDdEM7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDd0I7NERBQUt4QixXQUFVO3NFQUFnQjs7Ozs7O3NFQUNoQyw4REFBQ3dCOzREQUFLeEIsV0FBVyxlQUFvRSxPQUFyRDdDLFFBQVFvQixRQUFRLEdBQUcsbUJBQW1CO3NFQUNuRXBCLFFBQVFvQixRQUFRLEdBQUcsV0FBVzs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ3dCO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3dCOzREQUFLeEIsV0FBVTtzRUFBZ0I7Ozs7OztzRUFDaEMsOERBQUN3Qjs0REFBS3hCLFdBQVU7c0VBQWUsSUFBSXNDLEtBQUtuRixRQUFRb0YsU0FBUyxFQUFFQyxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUy9GO0dBMVd3QnRGOztRQUNQRCxzREFBU0E7OztLQURGQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqZXNzZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxTbWFydEZvcm1EZWZlbmRlclxcc3JjXFxhcHBcXGRhc2hib2FyZFxccHJvamVjdHNcXFtpZF1cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9qZWN0Q29uZmlnUGFnZSgpIHtcbiAgY29uc3QgcGFyYW1zID0gdXNlUGFyYW1zKCk7XG4gIGNvbnN0IHByb2plY3RJZCA9IHBhcmFtcy5pZCBhcyBzdHJpbmc7XG4gIFxuICBjb25zdCBbcHJvamVjdCwgc2V0UHJvamVjdF0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3NhdmluZywgc2V0U2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NvbmZpZywgc2V0Q29uZmlnXSA9IHVzZVN0YXRlKHtcbiAgICBuYW1lOiAnJyxcbiAgICBkb21haW46ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBzcGFtVGhyZXNob2xkOiAwLjcsXG4gICAgZW5hYmxlRW1haWxWYWxpZGF0aW9uOiB0cnVlLFxuICAgIGVuYWJsZVBob25lVmFsaWRhdGlvbjogdHJ1ZSxcbiAgICBlbmFibGVHZW9WYWxpZGF0aW9uOiBmYWxzZSxcbiAgICBhbGxvd2VkQ291bnRyaWVzOiAnJyxcbiAgICBibG9ja2VkQ291bnRyaWVzOiAnJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZVxuICB9KTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChwcm9qZWN0SWQpIHtcbiAgICAgIGxvYWRQcm9qZWN0KCk7XG4gICAgfVxuICB9LCBbcHJvamVjdElkXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnQ29uZmlnIHN0YXRlIHVwZGF0ZWQ6JywgY29uZmlnKTtcbiAgfSwgW2NvbmZpZ10pO1xuXG4gIGNvbnN0IGxvYWRQcm9qZWN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3Byb2plY3RzLyR7cHJvamVjdElkfWApO1xuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgY29uc3QgcHJvamVjdERhdGEgPSBkYXRhLnByb2plY3Q7XG5cbiAgICAgICAgc2V0UHJvamVjdChwcm9qZWN0RGF0YSk7XG4gICAgICAgIHNldENvbmZpZyh7XG4gICAgICAgICAgbmFtZTogcHJvamVjdERhdGEubmFtZSxcbiAgICAgICAgICBkb21haW46IHByb2plY3REYXRhLmRvbWFpbixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogcHJvamVjdERhdGEuZGVzY3JpcHRpb24gfHwgJycsXG4gICAgICAgICAgc3BhbVRocmVzaG9sZDogcHJvamVjdERhdGEuc3BhbVRocmVzaG9sZCxcbiAgICAgICAgICBlbmFibGVFbWFpbFZhbGlkYXRpb246IHByb2plY3REYXRhLmVuYWJsZUVtYWlsVmFsaWRhdGlvbixcbiAgICAgICAgICBlbmFibGVQaG9uZVZhbGlkYXRpb246IHByb2plY3REYXRhLmVuYWJsZVBob25lVmFsaWRhdGlvbixcbiAgICAgICAgICBlbmFibGVHZW9WYWxpZGF0aW9uOiBwcm9qZWN0RGF0YS5lbmFibGVHZW9WYWxpZGF0aW9uLFxuICAgICAgICAgIGFsbG93ZWRDb3VudHJpZXM6IHByb2plY3REYXRhLmFsbG93ZWRDb3VudHJpZXMgfHwgJycsXG4gICAgICAgICAgYmxvY2tlZENvdW50cmllczogcHJvamVjdERhdGEuYmxvY2tlZENvdW50cmllcyB8fCAnJyxcbiAgICAgICAgICBpc0FjdGl2ZTogcHJvamVjdERhdGEuaXNBY3RpdmVcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBwcm9qZWN0OicsIHJlc3BvbnNlLnN0YXR1cyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHByb2plY3Q6JywgZXJyb3IpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3Qgc2F2ZVByb2plY3QgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0U2F2aW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL3Byb2plY3RzLyR7cHJvamVjdElkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoY29uZmlnKVxuICAgICAgfSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBhbGVydCgnUHJvamVjdCB1cGRhdGVkIHN1Y2Nlc3NmdWxseSEnKTtcbiAgICAgICAgbG9hZFByb2plY3QoKTsgLy8gUmVmcmVzaCBkYXRhXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbGVydCgnRmFpbGVkIHRvIHVwZGF0ZSBwcm9qZWN0Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIHByb2plY3Q6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0ZhaWxlZCB0byB1cGRhdGUgcHJvamVjdCcpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTYXZpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZW5lcmF0ZUphdmFTY3JpcHRTbmlwcGV0ID0gKCkgPT4ge1xuICAgIGNvbnN0IGFwaUtleSA9IHByb2plY3Q/LnVzZXI/LmFwaUtleXM/LlswXT8ua2V5IHx8ICd5b3VyLWFwaS1rZXktaGVyZSc7XG4gICAgcmV0dXJuIGA8IS0tIFNtYXJ0Rm9ybSBEZWZlbmRlciAtLT5cbjxzY3JpcHQgc3JjPVwiaHR0cHM6Ly9jZG4uc21hcnRmb3JtZGVmZW5kZXIuY29tL3Nkay92MS9zbWFydGZvcm0uanNcIj48L3NjcmlwdD5cbjxzY3JpcHQ+XG4gIFNtYXJ0Rm9ybURlZmVuZGVyLmluaXQoe1xuICAgIGFwaUtleTogJyR7YXBpS2V5fScsXG4gICAgcHJvamVjdElkOiAnJHtwcm9qZWN0SWR9JyxcbiAgICBlbmRwb2ludDogJ2h0dHBzOi8vYXBpLnNtYXJ0Zm9ybWRlZmVuZGVyLmNvbS92YWxpZGF0ZS9zdWJtaXNzaW9uJ1xuICB9KTtcbjwvc2NyaXB0PmA7XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgaWYgKCFwcm9qZWN0KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlByb2plY3Qgbm90IGZvdW5kPC9oMj5cbiAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZC9wcm9qZWN0c1wiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTUwMCBtdC0yIGlubGluZS1ibG9ja1wiPlxuICAgICAgICAgICAg4oaQIEJhY2sgdG8gUHJvamVjdHNcbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBweS02XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Rhc2hib2FyZC9wcm9qZWN0c1wiIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTUwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAg4oaQIEJhY2sgdG8gUHJvamVjdHNcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbXQtMlwiPntwcm9qZWN0Lm5hbWV9PC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNvbmZpZ3VyZSBwcm9qZWN0IHNldHRpbmdzIGFuZCBpbnRlZ3JhdGlvbjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9e2AvZGFzaGJvYXJkL2ludGVncmF0aW9uc2B9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JlZW4tNzAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIE1hbmFnZSBJbnRlZ3JhdGlvbnNcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9e2AvZGFzaGJvYXJkL3Byb2plY3RzLyR7cHJvamVjdElkfS9hbmFseXRpY3NgfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1wdXJwbGUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBob3ZlcjpiZy1wdXJwbGUtNzAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFZpZXcgQW5hbHl0aWNzXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgey8qIENvbmZpZ3VyYXRpb24gRm9ybSAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTIgc3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogQmFzaWMgU2V0dGluZ3MgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5CYXNpYyBTZXR0aW5nczwvaDI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgUHJvamVjdCBOYW1lXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5uYW1lfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnTmFtZSBpbnB1dCBjaGFuZ2VkIHRvOicsIGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICBzZXRDb25maWcocHJldiA9PiAoeyAuLi5wcmV2LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCB0ZXh0LWdyYXktOTAwIGJnLXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7Zm9udFNpemU6ICcxMHB4JywgY29sb3I6ICdyZWQnfX0+RGVidWc6IG5hbWUgPSBcIntjb25maWcubmFtZX1cIjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgRG9tYWluXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5kb21haW59XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdEb21haW4gaW5wdXQgY2hhbmdlZCB0bzonLCBlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgc2V0Q29uZmlnKHByZXYgPT4gKHsgLi4ucHJldiwgZG9tYWluOiBlLnRhcmdldC52YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCB0ZXh0LWdyYXktOTAwIGJnLXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJleGFtcGxlLmNvbVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e2ZvbnRTaXplOiAnMTBweCcsIGNvbG9yOiAncmVkJ319PkRlYnVnOiBkb21haW4gPSBcIntjb25maWcuZG9tYWlufVwiPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIERlc2NyaXB0aW9uXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIHRleHQtZ3JheS05MDAgYmctd2hpdGVcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmliZSB0aGlzIHByb2plY3QuLi5cIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTcGFtIERldGVjdGlvbiBTZXR0aW5ncyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cgcC02XCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlNwYW0gRGV0ZWN0aW9uPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIFNwYW0gVGhyZXNob2xkICh7KGNvbmZpZy5zcGFtVGhyZXNob2xkICogMTAwKS50b0ZpeGVkKDApfSUpXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICBtYXg9XCIxXCJcbiAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcuc3BhbVRocmVzaG9sZH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb25maWcocHJldiA9PiAoeyAuLi5wcmV2LCBzcGFtVGhyZXNob2xkOiBwYXJzZUZsb2F0KGUudGFyZ2V0LnZhbHVlKSB9KSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5MZW5pZW50ICgwJSk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPlN0cmljdCAoMTAwJSk8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFZhbGlkYXRpb24gU2V0dGluZ3MgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5WYWxpZGF0aW9uIFNldHRpbmdzPC9oMj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgaWQ9XCJlbWFpbFZhbGlkYXRpb25cIlxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb25maWcuZW5hYmxlRW1haWxWYWxpZGF0aW9ufVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7IC4uLnByZXYsIGVuYWJsZUVtYWlsVmFsaWRhdGlvbjogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ibHVlLTYwMCBmb2N1czpyaW5nLWJsdWUtNTAwIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImVtYWlsVmFsaWRhdGlvblwiIGNsYXNzTmFtZT1cIm1sLTIgYmxvY2sgdGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIEVuYWJsZSBFbWFpbCBWYWxpZGF0aW9uXG4gICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBpZD1cInBob25lVmFsaWRhdGlvblwiXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbmZpZy5lbmFibGVQaG9uZVZhbGlkYXRpb259XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHByZXYgPT4gKHsgLi4ucHJldiwgZW5hYmxlUGhvbmVWYWxpZGF0aW9uOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWJsdWUtNjAwIGZvY3VzOnJpbmctYmx1ZS01MDAgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwicGhvbmVWYWxpZGF0aW9uXCIgY2xhc3NOYW1lPVwibWwtMiBibG9jayB0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIFBob25lIFZhbGlkYXRpb25cbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgIGlkPVwiZ2VvVmFsaWRhdGlvblwiXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvbmZpZy5lbmFibGVHZW9WYWxpZGF0aW9ufVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7IC4uLnByZXYsIGVuYWJsZUdlb1ZhbGlkYXRpb246IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtYmx1ZS02MDAgZm9jdXM6cmluZy1ibHVlLTUwMCBib3JkZXItZ3JheS0zMDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJnZW9WYWxpZGF0aW9uXCIgY2xhc3NOYW1lPVwibWwtMiBibG9jayB0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgRW5hYmxlIEdlb2dyYXBoaWMgVmFsaWRhdGlvblxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAge2NvbmZpZy5lbmFibGVHZW9WYWxpZGF0aW9uICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQWxsb3dlZCBDb3VudHJpZXMgKGNvbW1hLXNlcGFyYXRlZClcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcuYWxsb3dlZENvdW50cmllc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7IC4uLnByZXYsIGFsbG93ZWRDb3VudHJpZXM6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgdGV4dC1ncmF5LTkwMCBiZy13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJVUywgQ0EsIEdCXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgQmxvY2tlZCBDb3VudHJpZXMgKGNvbW1hLXNlcGFyYXRlZClcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcuYmxvY2tlZENvdW50cmllc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyhwcmV2ID0+ICh7IC4uLnByZXYsIGJsb2NrZWRDb3VudHJpZXM6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgdGV4dC1ncmF5LTkwMCBiZy13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJDTiwgUlVcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFNhdmUgQnV0dG9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtzYXZlUHJvamVjdH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17c2F2aW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMiBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7c2F2aW5nID8gJ1NhdmluZy4uLicgOiAnU2F2ZSBDaGFuZ2VzJ31cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBTaWRlYmFyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICB7LyogSW5zdGFsbGF0aW9uIENvZGUgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5JbnN0YWxsYXRpb248L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIEFkZCB0aGlzIGNvZGUgdG8geW91ciB3ZWJzaXRlJ3MgSFRNTCwganVzdCBiZWZvcmUgdGhlIGNsb3NpbmcgJmx0Oy9ib2R5Jmd0OyB0YWc6XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBwLTMgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbW9ubyBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwXCI+e2dlbmVyYXRlSmF2YVNjcmlwdFNuaXBwZXQoKX08L3ByZT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dChnZW5lcmF0ZUphdmFTY3JpcHRTbmlwcGV0KCkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTMgdy1mdWxsIHB4LTMgcHktMiB0ZXh0LXNtIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcm91bmRlZCBob3ZlcjpiZy1ncmF5LTIwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDb3B5IENvZGVcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFByb2plY3QgU3RhdHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTRcIj5Qcm9qZWN0IFN0YXRzPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Ub3RhbCBTdWJtaXNzaW9uczo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9qZWN0Ll9jb3VudD8uc3VibWlzc2lvbnMgfHwgMH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+QWN0aXZlIEludGVncmF0aW9uczo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9qZWN0Ll9jb3VudD8uaW50ZWdyYXRpb25zIHx8IDB9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlN0YXR1czo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSAke3Byb2plY3QuaXNBY3RpdmUgPyAndGV4dC1ncmVlbi02MDAnIDogJ3RleHQtcmVkLTYwMCd9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtwcm9qZWN0LmlzQWN0aXZlID8gJ0FjdGl2ZScgOiAnSW5hY3RpdmUnfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNyZWF0ZWQ6PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57bmV3IERhdGUocHJvamVjdC5jcmVhdGVkQXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkxpbmsiLCJ1c2VQYXJhbXMiLCJQcm9qZWN0Q29uZmlnUGFnZSIsInByb2plY3QiLCJwYXJhbXMiLCJwcm9qZWN0SWQiLCJpZCIsInNldFByb2plY3QiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhdmluZyIsInNldFNhdmluZyIsImNvbmZpZyIsInNldENvbmZpZyIsIm5hbWUiLCJkb21haW4iLCJkZXNjcmlwdGlvbiIsInNwYW1UaHJlc2hvbGQiLCJlbmFibGVFbWFpbFZhbGlkYXRpb24iLCJlbmFibGVQaG9uZVZhbGlkYXRpb24iLCJlbmFibGVHZW9WYWxpZGF0aW9uIiwiYWxsb3dlZENvdW50cmllcyIsImJsb2NrZWRDb3VudHJpZXMiLCJpc0FjdGl2ZSIsImxvYWRQcm9qZWN0IiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImRhdGEiLCJqc29uIiwicHJvamVjdERhdGEiLCJlcnJvciIsInN0YXR1cyIsInNhdmVQcm9qZWN0IiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiYWxlcnQiLCJnZW5lcmF0ZUphdmFTY3JpcHRTbmlwcGV0IiwiYXBpS2V5IiwidXNlciIsImFwaUtleXMiLCJrZXkiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsImhyZWYiLCJoMSIsInAiLCJsYWJlbCIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicHJldiIsInN0eWxlIiwiZm9udFNpemUiLCJjb2xvciIsInBsYWNlaG9sZGVyIiwidGV4dGFyZWEiLCJyb3dzIiwidG9GaXhlZCIsIm1pbiIsIm1heCIsInN0ZXAiLCJwYXJzZUZsb2F0Iiwic3BhbiIsImNoZWNrZWQiLCJodG1sRm9yIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiaDMiLCJwcmUiLCJuYXZpZ2F0b3IiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJfY291bnQiLCJzdWJtaXNzaW9ucyIsImludGVncmF0aW9ucyIsIkRhdGUiLCJjcmVhdGVkQXQiLCJ0b0xvY2FsZURhdGVTdHJpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx\n"));

/***/ })

});