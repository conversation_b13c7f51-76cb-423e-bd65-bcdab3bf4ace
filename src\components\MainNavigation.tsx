'use client';

import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { LogoWithText } from '@/components/Logo';

interface MainNavigationProps {
  currentPage?: string;
}

export default function MainNavigation({ currentPage }: MainNavigationProps) {
  const { data: session, status } = useSession();

  return (
    <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/">
              <LogoWithText size="md" variant="default" />
            </Link>
          </div>
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              <Link 
                href="/#features" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'home' 
                    ? 'text-primary-600 hover:text-primary-700' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Features
              </Link>
              <Link 
                href="/#pricing" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'home' 
                    ? 'text-primary-600 hover:text-primary-700' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Pricing
              </Link>
              <Link 
                href="/docs" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'docs' 
                    ? 'text-primary-600 hover:text-primary-700 border-b-2 border-primary-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Docs
              </Link>
              <Link 
                href="/about" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'about' 
                    ? 'text-primary-600 hover:text-primary-700 border-b-2 border-primary-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                About
              </Link>
              <Link 
                href="/contact" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'contact' 
                    ? 'text-primary-600 hover:text-primary-700 border-b-2 border-primary-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Contact
              </Link>
              <Link 
                href="/demo" 
                className={`px-3 py-2 rounded-md text-sm font-medium ${
                  currentPage === 'demo' 
                    ? 'text-primary-600 hover:text-primary-700 border-b-2 border-primary-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Demo
              </Link>
              
              {/* Authentication Links */}
              {status === 'loading' ? (
                <div className="w-20 h-8 bg-gray-200 animate-pulse rounded"></div>
              ) : session ? (
                <Link href="/dashboard" className="btn-primary">
                  Dashboard
                </Link>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link 
                    href="/auth/signin" 
                    className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                  >
                    Sign In
                  </Link>
                  <Link href="/auth/signup" className="btn-primary">
                    Get Started
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
