"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/submissions/[id]/route";
exports.ids = ["app/api/submissions/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_submissions_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/submissions/[id]/route.ts */ \"(rsc)/./src/app/api/submissions/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/submissions/[id]/route\",\n        pathname: \"/api/submissions/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/submissions/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\submissions\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_submissions_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/submissions/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/submissions/[id]/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/submissions/[id]/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// GET /api/submissions/[id] - Get submission details\nasync function GET(request, { params }) {\n    try {\n        const submission = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.findUnique({\n            where: {\n                id: params.id\n            },\n            include: {\n                project: {\n                    select: {\n                        name: true,\n                        domain: true\n                    }\n                }\n            }\n        });\n        if (!submission) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Submission not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Parse JSON fields for response\n        const response = {\n            ...submission,\n            formData: submission.formData ? JSON.parse(submission.formData) : {},\n            validationResults: submission.validationResults ? JSON.parse(submission.validationResults) : {},\n            fingerprint: submission.fingerprint ? JSON.parse(submission.fingerprint) : {},\n            crmResponse: submission.crmResponse ? JSON.parse(submission.crmResponse) : null\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            submission: response\n        });\n    } catch (error) {\n        console.error(\"Submission fetch error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch submission\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/submissions/[id] - Update submission status\nasync function PUT(request, { params }) {\n    try {\n        const body = await request.json();\n        const { status, isSpam, isValid, notes } = body;\n        const updateData = {\n            updatedAt: new Date()\n        };\n        if (status) updateData.status = status;\n        if (isSpam !== undefined) updateData.isSpam = isSpam;\n        if (isValid !== undefined) updateData.isValid = isValid;\n        if (notes !== undefined) updateData.notes = notes;\n        const submission = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.update({\n            where: {\n                id: params.id\n            },\n            data: updateData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            submission\n        });\n    } catch (error) {\n        console.error(\"Submission update error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update submission\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/submissions/[id] - Delete submission\nasync function DELETE(request, { params }) {\n    try {\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Submission deletion error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete submission\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/submissions/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydGZvcm0tZGVmZW5kZXIvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&page=%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubmissions%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();