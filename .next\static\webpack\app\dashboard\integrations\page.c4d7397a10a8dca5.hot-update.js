"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/integrations/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/integrations/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/integrations/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ IntegrationsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction IntegrationsPage() {\n    _s();\n    const [integrations, setIntegrations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadIntegrations();\n    }, []);\n    const loadIntegrations = async ()=>{\n        try {\n            const response = await fetch(\"/api/integrations?projectId=demo-project-id\", {\n                headers: {\n                    \"X-API-Key\": \"sfd_demo_key_12345\"\n                }\n            });\n            const data = await response.json();\n            setIntegrations(data.data || []);\n        } catch (error) {\n            console.error(\"Failed to load integrations:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testIntegration = async (integrationId)=>{\n        try {\n            const response = await fetch(\"/api/integrations/\".concat(integrationId, \"/test\"), {\n                method: \"POST\",\n                headers: {\n                    \"X-API-Key\": \"sfd_demo_key_12345\"\n                }\n            });\n            const result = await response.json();\n            alert(\"Test Result: \".concat(result.message));\n            loadIntegrations(); // Refresh data\n        } catch (error) {\n            alert(\"Test failed: \" + error.message);\n        }\n    };\n    const toggleIntegration = async (integrationId, currentStatus)=>{\n        try {\n            const response = await fetch(\"/api/integrations/\".concat(integrationId), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"X-API-Key\": \"sfd_demo_key_12345\"\n                },\n                body: JSON.stringify({\n                    isActive: !currentStatus\n                })\n            });\n            if (response.ok) {\n                loadIntegrations(); // Refresh the list\n            }\n        } catch (error) {\n            console.error(\"Failed to update integration status:\", error);\n        }\n    };\n    const getIntegrationIcon = (type)=>{\n        switch(type){\n            case \"HUBSPOT\":\n                return \"\\uD83E\\uDDE1\";\n            case \"SALESFORCE\":\n                return \"☁️\";\n            case \"MAILCHIMP\":\n                return \"\\uD83D\\uDC35\";\n            case \"ZAPIER\":\n                return \"⚡\";\n            case \"WEBHOOK\":\n                return \"\\uD83D\\uDD17\";\n            default:\n                return \"\\uD83D\\uDD0C\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/dashboard\",\n                                        className: \"text-blue-600 hover:text-blue-500 text-sm\",\n                                        children: \"← Back to Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mt-2\",\n                                        children: \"Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Manage your CRM and automation integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/dashboard/integrations/new\",\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                children: \"Add Integration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: integrations.map((integration)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl mr-3\",\n                                                            children: getIntegrationIcon(integration.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                    children: integration.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                                    lineNumber: 120,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: integration.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                                    lineNumber: 121,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 rounded-full mr-2 \".concat(integration.isActive ? \"bg-green-500\" : \"bg-gray-400\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: integration.isActive ? \"Active\" : \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Last Tested:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: integration.lastTested ? new Date(integration.lastTested).toLocaleDateString() : \"Never\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Status:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat(integration.status === \"CONNECTED\" ? \"text-green-600\" : integration.status === \"ERROR\" ? \"text-red-600\" : \"text-yellow-600\"),\n                                                            children: integration.status || \"Unknown\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Created:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-900\",\n                                                            children: new Date(integration.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 rounded p-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-600 space-y-1\",\n                                                    children: [\n                                                        integration.type === \"HUBSPOT\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Portal ID: \",\n                                                                        integration.config.portalId || \"Not set\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Auth Type: \",\n                                                                        integration.config.accessToken ? \"OAuth\" : \"API Key\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true),\n                                                        integration.type === \"WEBHOOK\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"URL: \",\n                                                                        integration.config.url || \"Not set\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Method: \",\n                                                                        integration.config.method || \"POST\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true),\n                                                        integration.type === \"ZAPIER\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"Webhook URL: \",\n                                                                JSON.parse(integration.config).webhookUrl ? \"Configured\" : \"Not set\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>testIntegration(integration.id),\n                                                    className: \"flex-1 px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200\",\n                                                    children: \"Test\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/dashboard/integrations/\".concat(integration.id, \"/edit\"),\n                                                    className: \"flex-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-center\",\n                                                    children: \"Edit\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>toggleIntegration(integration.id, integration.isActive),\n                                                    className: \"flex-1 px-3 py-2 text-sm rounded \".concat(integration.isActive ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-green-100 text-green-700 hover:bg-green-200\"),\n                                                    children: integration.isActive ? \"Disable\" : \"Enable\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            }, integration.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    integrations.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"mx-auto h-12 w-12 text-gray-400\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mt-2 text-sm font-medium text-gray-900\",\n                                children: \"No integrations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: \"Get started by adding your first integration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/dashboard/integrations/new\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"-ml-1 mr-2 h-5 w-5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Integration\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-6\",\n                                children: \"Available Integrations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    {\n                                        type: \"HUBSPOT\",\n                                        name: \"HubSpot\",\n                                        icon: \"\\uD83E\\uDDE1\",\n                                        description: \"CRM and marketing automation\"\n                                    },\n                                    {\n                                        type: \"SALESFORCE\",\n                                        name: \"Salesforce\",\n                                        icon: \"☁️\",\n                                        description: \"World's #1 CRM platform\"\n                                    },\n                                    {\n                                        type: \"MAILCHIMP\",\n                                        name: \"Mailchimp\",\n                                        icon: \"\\uD83D\\uDC35\",\n                                        description: \"Email marketing platform\"\n                                    },\n                                    {\n                                        type: \"ZAPIER\",\n                                        name: \"Zapier\",\n                                        icon: \"⚡\",\n                                        description: \"Connect 5000+ apps\"\n                                    },\n                                    {\n                                        type: \"WEBHOOK\",\n                                        name: \"Custom Webhook\",\n                                        icon: \"\\uD83D\\uDD17\",\n                                        description: \"Send data to any endpoint\"\n                                    }\n                                ].map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-3xl mb-2 block\",\n                                                    children: provider.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: provider.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                    children: provider.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/dashboard/integrations/new?type=\".concat(provider.type),\n                                                    className: \"mt-3 inline-block px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\",\n                                                    children: [\n                                                        \"Add \",\n                                                        provider.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, provider.type, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-blue-50 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-blue-900 mb-2\",\n                                children: \"Integration Help\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-1\",\n                                                children: \"Setup Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Each integration requires specific credentials and configuration. Follow our setup guides for each platform.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-1\",\n                                                children: \"Testing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Always test your integrations after setup to ensure leads are being sent correctly to your CRM.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-1\",\n                                                children: \"Troubleshooting\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Check the integration status and test results if leads aren't appearing in your CRM system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\integrations\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(IntegrationsPage, \"Po/39k1JHmbljy4+Zbl9FBO00EA=\");\n_c = IntegrationsPage;\nvar _c;\n$RefreshReg$(_c, \"IntegrationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/integrations/page.tsx\n"));

/***/ })

});