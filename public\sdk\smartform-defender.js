/**
 * SmartForm Defender SDK
 * Client-side form protection with real-time validation and spam detection
 */

class SmartFormDefenderSDK {
  constructor(config = {}) {
    this.config = {
      apiKey: config.apiKey || '',
      apiUrl: config.apiUrl || '/api/validate',
      enableRealTimeValidation: config.enableRealTimeValidation !== false,
      enableBotDetection: config.enableBotDetection !== false,
      spamThreshold: config.spamThreshold || 0.7,
      debug: config.debug || false,
      ...config
    };

    this.formData = new Map();
    this.behaviorData = {
      mouseMovements: [],
      keystrokes: [],
      focusEvents: [],
      startTime: Date.now(),
      interactions: 0
    };

    this.init();
  }

  init() {
    if (this.config.debug) {
      console.log('SmartForm Defender initialized', this.config);
    }

    // Start behavior tracking
    if (this.config.enableBotDetection) {
      this.startBehaviorTracking();
    }

    // Auto-protect forms
    this.protectForms();
  }

  protectForms(selector = 'form[data-smartform]') {
    const forms = document.querySelectorAll(selector);
    
    forms.forEach(form => {
      this.protectForm(form);
    });
  }

  protectForm(form) {
    if (form.dataset.smartformProtected) return;
    
    form.dataset.smartformProtected = 'true';
    
    // Add real-time validation
    if (this.config.enableRealTimeValidation) {
      this.addRealTimeValidation(form);
    }

    // Override form submission
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleFormSubmission(form, e);
    });

    if (this.config.debug) {
      console.log('Form protected:', form);
    }
  }

  addRealTimeValidation(form) {
    const emailFields = form.querySelectorAll('input[type="email"], input[name*="email"]');
    const phoneFields = form.querySelectorAll('input[type="tel"], input[name*="phone"]');

    emailFields.forEach(field => {
      field.addEventListener('blur', () => this.validateEmail(field));
      field.addEventListener('input', this.debounce(() => this.validateEmail(field), 500));
    });

    phoneFields.forEach(field => {
      field.addEventListener('blur', () => this.validatePhone(field));
      field.addEventListener('input', this.debounce(() => this.validatePhone(field), 500));
    });
  }

  async validateEmail(field) {
    const email = field.value.trim();
    if (!email) return;

    const isValid = this.isValidEmailFormat(email);
    this.updateFieldValidation(field, isValid, isValid ? '' : 'Invalid email format');

    if (isValid) {
      // Additional email validation via API
      try {
        const response = await this.apiCall('/api/validate/email', { email });
        this.updateFieldValidation(field, response.valid, response.message);
      } catch (error) {
        if (this.config.debug) console.error('Email validation error:', error);
      }
    }
  }

  async validatePhone(field) {
    const phone = field.value.trim();
    if (!phone) return;

    const isValid = this.isValidPhoneFormat(phone);
    this.updateFieldValidation(field, isValid, isValid ? '' : 'Invalid phone format');

    if (isValid) {
      try {
        const response = await this.apiCall('/api/validate/phone', { phone });
        this.updateFieldValidation(field, response.valid, response.message);
      } catch (error) {
        if (this.config.debug) console.error('Phone validation error:', error);
      }
    }
  }

  updateFieldValidation(field, isValid, message) {
    // Remove existing validation classes
    field.classList.remove('sfd-valid', 'sfd-invalid');
    
    // Add new validation class
    field.classList.add(isValid ? 'sfd-valid' : 'sfd-invalid');

    // Update or create validation message
    let messageEl = field.parentNode.querySelector('.sfd-validation-message');
    if (!messageEl) {
      messageEl = document.createElement('div');
      messageEl.className = 'sfd-validation-message';
      field.parentNode.appendChild(messageEl);
    }

    messageEl.textContent = message;
    messageEl.style.display = message ? 'block' : 'none';
  }

  async handleFormSubmission(form, event) {
    try {
      // Show loading state
      this.setFormLoading(form, true);

      // Collect form data
      const formData = this.collectFormData(form);
      
      // Generate fingerprint and behavior data
      const fingerprint = this.generateFingerprint();
      const behaviorScore = this.calculateBehaviorScore();

      // Prepare submission data
      const submissionData = {
        formData,
        fingerprint,
        behaviorData: this.behaviorData,
        behaviorScore,
        metadata: {
          url: window.location.href,
          referrer: document.referrer,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        }
      };

      // Validate with API
      const validation = await this.apiCall('/api/validate/submission', submissionData);

      if (validation.isValid && validation.spamScore < this.config.spamThreshold) {
        // Valid submission - proceed
        this.handleValidSubmission(form, submissionData, validation);
      } else {
        // Spam detected
        this.handleSpamSubmission(form, validation);
      }

    } catch (error) {
      console.error('Form submission error:', error);
      this.handleSubmissionError(form, error);
    } finally {
      this.setFormLoading(form, false);
    }
  }

  collectFormData(form) {
    const data = {};
    const formData = new FormData(form);
    
    for (let [key, value] of formData.entries()) {
      data[key] = value;
    }
    
    return data;
  }

  generateFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('SmartForm fingerprint', 2, 2);
    
    return {
      canvas: canvas.toDataURL(),
      screen: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack
    };
  }

  calculateBehaviorScore() {
    const { mouseMovements, keystrokes, focusEvents, startTime, interactions } = this.behaviorData;
    const timeSpent = Date.now() - startTime;
    
    let score = 0;
    
    // Time-based scoring
    if (timeSpent < 2000) score += 0.3; // Too fast
    if (timeSpent > 300000) score += 0.1; // Very slow
    
    // Mouse movement scoring
    if (mouseMovements.length === 0) score += 0.4; // No mouse movement
    if (mouseMovements.length < 5) score += 0.2; // Very few movements
    
    // Keystroke scoring
    if (keystrokes.length === 0) score += 0.3; // No typing
    
    // Interaction scoring
    if (interactions < 3) score += 0.2; // Few interactions
    
    return Math.min(score, 1); // Cap at 1
  }

  startBehaviorTracking() {
    // Track mouse movements
    document.addEventListener('mousemove', (e) => {
      this.behaviorData.mouseMovements.push({
        x: e.clientX,
        y: e.clientY,
        timestamp: Date.now()
      });
      
      // Keep only last 100 movements
      if (this.behaviorData.mouseMovements.length > 100) {
        this.behaviorData.mouseMovements.shift();
      }
    });

    // Track keystrokes
    document.addEventListener('keydown', (e) => {
      this.behaviorData.keystrokes.push({
        key: e.key,
        timestamp: Date.now(),
        interval: this.behaviorData.keystrokes.length > 0 
          ? Date.now() - this.behaviorData.keystrokes[this.behaviorData.keystrokes.length - 1].timestamp 
          : 0
      });
      
      if (this.behaviorData.keystrokes.length > 50) {
        this.behaviorData.keystrokes.shift();
      }
    });

    // Track focus events
    document.addEventListener('focusin', (e) => {
      this.behaviorData.focusEvents.push({
        target: e.target.tagName,
        timestamp: Date.now()
      });
      this.behaviorData.interactions++;
    });
  }

  // Utility methods
  isValidEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidPhoneFormat(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  async apiCall(endpoint, data) {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.config.apiKey
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`);
    }

    return response.json();
  }

  setFormLoading(form, loading) {
    const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
    if (submitBtn) {
      submitBtn.disabled = loading;
      submitBtn.textContent = loading ? 'Validating...' : submitBtn.dataset.originalText || 'Submit';
      if (!submitBtn.dataset.originalText) {
        submitBtn.dataset.originalText = submitBtn.textContent;
      }
    }
  }

  handleValidSubmission(form, data, validation) {
    if (this.config.debug) {
      console.log('Valid submission:', { data, validation });
    }
    
    // Trigger custom event
    form.dispatchEvent(new CustomEvent('smartform:valid', { 
      detail: { data, validation } 
    }));
  }

  handleSpamSubmission(form, validation) {
    if (this.config.debug) {
      console.log('Spam detected:', validation);
    }
    
    // Trigger custom event
    form.dispatchEvent(new CustomEvent('smartform:spam', { 
      detail: { validation } 
    }));
  }

  handleSubmissionError(form, error) {
    if (this.config.debug) {
      console.error('Submission error:', error);
    }
    
    form.dispatchEvent(new CustomEvent('smartform:error', { 
      detail: { error } 
    }));
  }
}

// Auto-initialize if config is provided
if (typeof window !== 'undefined' && window.SmartFormDefenderConfig) {
  window.SmartFormDefenderSDK = new SmartFormDefenderSDK(window.SmartFormDefenderConfig);
}

// Export for module systems and backwards compatibility
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SmartFormDefenderSDK;
}

// Make available globally with both names for compatibility
if (typeof window !== 'undefined') {
  window.SmartFormDefenderSDK = SmartFormDefenderSDK;
  // Keep old name for backwards compatibility but avoid conflicts
  if (!window.SmartFormDefender) {
    window.SmartFormDefender = SmartFormDefenderSDK;
  }
}
