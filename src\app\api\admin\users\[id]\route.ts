import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateUserSchema = z.object({
  name: z.string().optional(),
  email: z.string().email(),
  company: z.string().optional(),
  phone: z.string().optional(),
  role: z.enum(['USER', 'ADMIN']),
  plan: z.enum(['FREE', 'PRO', 'ENTERPRISE'])
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin access
    await requireAdmin();

    const userId = params.id;

    // Get user with detailed information
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        projects: {
          select: {
            id: true,
            name: true,
            domain: true,
            isActive: true,
            createdAt: true,
            _count: {
              select: {
                submissions: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        integrations: {
          select: {
            id: true,
            type: true,
            isActive: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            projects: true,
            integrations: true
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);

  } catch (error) {
    console.error('Admin user detail fetch error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Authentication required' || error.message === 'Admin access required') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to fetch user details' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin access
    await requireAdmin();

    const body = await request.json();
    const validatedData = updateUserSchema.parse(body);

    // Check if email is already taken by another user
    const existingUser = await prisma.user.findFirst({
      where: {
        email: validatedData.email,
        NOT: { id: params.id }
      }
    });

    if (existingUser) {
      return NextResponse.json({ error: 'Email already in use' }, { status: 400 });
    }

    const updatedUser = await prisma.user.update({
      where: { id: params.id },
      data: {
        name: validatedData.name || null,
        email: validatedData.email,
        company: validatedData.company || null,
        phone: validatedData.phone || null,
        role: validatedData.role,
        plan: validatedData.plan
      }
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Admin user update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid data', details: error.errors }, { status: 400 });
    }

    if (error instanceof Error) {
      if (error.message === 'Authentication required' || error.message === 'Admin access required') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        );
      }
    }

    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify admin access
    const adminUser = await requireAdmin();

    const userId = params.id;

    // Prevent admin from deleting themselves
    if (adminUser.id === userId) {
      return NextResponse.json({ error: 'Cannot delete your own account' }, { status: 400 });
    }

    // Check if user exists
    const userToDelete = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, name: true, email: true }
    });

    if (!userToDelete) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Delete user and related data
    await prisma.$transaction(async (tx) => {
      // Delete user's projects first (due to foreign key constraints)
      await tx.project.deleteMany({
        where: { userId: userId }
      });

      // Delete user's accounts (OAuth connections)
      await tx.account.deleteMany({
        where: { userId: userId }
      });

      // Delete user's sessions
      await tx.session.deleteMany({
        where: { userId: userId }
      });

      // Finally delete the user
      await tx.user.delete({
        where: { id: userId }
      });
    });

    return NextResponse.json({
      message: 'User deleted successfully',
      deletedUser: userToDelete
    });
  } catch (error) {
    console.error('Admin user delete error:', error);

    if (error instanceof Error) {
      if (error.message === 'Authentication required' || error.message === 'Admin access required') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        );
      }
    }

    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
