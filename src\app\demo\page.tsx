'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { LogoWithText } from '@/components/Logo';

export default function DemoPage() {
  useEffect(() => {
    // Use a unique identifier to prevent multiple loads
    const SDK_LOADED_FLAG = 'smartform-defender-sdk-loaded';

    // Check if SDK is already loaded or loading
    if ((window as any)[SDK_LOADED_FLAG] || (window as any).SmartFormDefenderSDK) {
      console.log('✅ SmartForm Defender SDK already loaded');
      return;
    }

    // Mark as loading to prevent duplicate loads
    (window as any)[SDK_LOADED_FLAG] = true;

    let script: HTMLScriptElement | null = null;
    let link: HTMLLinkElement | null = null;

    // Create a unique script with timestamp to avoid caching issues
    script = document.createElement('script');
    script.src = `/sdk/smartform-defender.js?v=${Date.now()}`;
    script.async = true;

    script.onload = () => {
      console.log('📦 SDK script loaded');
      // Small delay to ensure script execution
      setTimeout(() => {
        if (typeof window !== 'undefined' && (window as any).SmartFormDefenderSDK) {
          console.log('✅ SmartForm Defender SDK class available');
          try {
            // Initialize SDK instance
            (window as any).SmartFormDefenderInstance = new (window as any).SmartFormDefenderSDK({
              apiKey: 'demo_key',
              debug: true,
              enableRealTimeValidation: true,
              enableBotDetection: true,
              spamThreshold: 0.7
            });
            console.log('✅ SmartForm Defender SDK initialized successfully');
          } catch (error) {
            console.error('❌ Failed to initialize SDK:', error);
          }
        } else {
          console.warn('⚠️ SmartForm Defender SDK class not found after script load');
        }
      }, 100);
    };

    script.onerror = (error) => {
      console.error('❌ Failed to load SmartForm Defender SDK script:', error);
      (window as any)[SDK_LOADED_FLAG] = false;
    };

    document.head.appendChild(script);

    // Load CSS
    link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/sdk/smartform-defender.css';
    document.head.appendChild(link);

    return () => {
      // Cleanup function
      if (script && document.head.contains(script)) {
        document.head.removeChild(script);
      }
      if (link && document.head.contains(link)) {
        document.head.removeChild(link);
      }
      // Reset flag on cleanup
      (window as any)[SDK_LOADED_FLAG] = false;
    };
  }, []);

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/">
                <LogoWithText size="md" variant="default" />
              </Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/#features" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Features
                </Link>
                <Link href="/#pricing" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Pricing
                </Link>
                <Link href="/docs" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Docs
                </Link>
                <Link href="/about" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  About
                </Link>
                <Link href="/contact" className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
                  Contact
                </Link>
                <Link href="/demo" className="text-primary-600 hover:text-primary-700 px-3 py-2 rounded-md text-sm font-medium border-b-2 border-primary-600">
                  Demo
                </Link>
                <Link href="/dashboard" className="btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            SmartForm Defender Demo
          </h1>
          <p className="text-xl text-gray-600">
            Test our form protection system with real-time validation and spam detection
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Demo Form */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Protected Contact Form
            </h2>
            
            <form data-smartform className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  className="input-field"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  className="input-field"
                  placeholder="Enter your email address"
                />
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  className="input-field"
                  placeholder="Enter your phone number"
                />
              </div>

              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  id="company"
                  name="company"
                  className="input-field"
                  placeholder="Enter your company name"
                />
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Message *
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows={4}
                  required
                  className="input-field"
                  placeholder="Enter your message"
                ></textarea>
              </div>

              {/* Honeypot field - hidden from users */}
              <input
                type="text"
                name="website"
                className="sfd-honeypot"
                tabIndex={-1}
                autoComplete="off"
              />

              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
              >
                Send Message
              </button>

              <div className="sfd-protected-badge">
                Protected by SmartForm Defender
              </div>
            </form>
          </div>

          {/* Real-time Analytics */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Real-time Protection Analytics
            </h2>
            
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-green-800 mb-2">
                  🛡️ Active Protection
                </h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>✓ Real-time email validation</li>
                  <li>✓ Phone number verification</li>
                  <li>✓ Behavioral analysis</li>
                  <li>✓ Bot detection</li>
                  <li>✓ Spam scoring</li>
                </ul>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">
                  📊 Form Metrics
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-blue-600 font-medium">Submissions</div>
                    <div className="text-2xl font-bold text-blue-800">0</div>
                  </div>
                  <div>
                    <div className="text-blue-600 font-medium">Spam Blocked</div>
                    <div className="text-2xl font-bold text-blue-800">0</div>
                  </div>
                  <div>
                    <div className="text-blue-600 font-medium">Valid Leads</div>
                    <div className="text-2xl font-bold text-blue-800">0</div>
                  </div>
                  <div>
                    <div className="text-blue-600 font-medium">Success Rate</div>
                    <div className="text-2xl font-bold text-blue-800">100%</div>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                  ⚡ Features Demo
                </h3>
                <div className="text-sm text-yellow-700 space-y-2">
                  <p><strong>Try these to see protection in action:</strong></p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Enter an invalid email format</li>
                    <li>Submit the form very quickly</li>
                    <li>Fill the hidden honeypot field</li>
                    <li>Use suspicious content patterns</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
}
