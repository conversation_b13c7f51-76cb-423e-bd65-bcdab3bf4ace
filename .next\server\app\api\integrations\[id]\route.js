"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/integrations/[id]/route";
exports.ids = ["app/api/integrations/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/integrations/[id]/route.ts */ \"(rsc)/./src/app/api/integrations/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/integrations/[id]/route\",\n        pathname: \"/api/integrations/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/integrations/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\integrations\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/integrations/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/integrations/[id]/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/integrations/[id]/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// GET /api/integrations/[id] - Get integration details\nasync function GET(request, { params }) {\n    try {\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findUnique({\n            where: {\n                id: params.id\n            },\n            include: {\n                project: {\n                    select: {\n                        name: true,\n                        domain: true\n                    }\n                }\n            }\n        });\n        if (!integration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Integration not found\"\n            }, {\n                status: 404\n            });\n        }\n        const response = {\n            ...integration,\n            config: integration.config ? JSON.parse(integration.config) : {}\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            integration: response\n        });\n    } catch (error) {\n        console.error(\"Integration fetch error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch integration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/integrations/[id] - Update integration\nasync function PUT(request, { params }) {\n    try {\n        const body = await request.json();\n        const { name, config, isActive } = body;\n        const updateData = {\n            updatedAt: new Date()\n        };\n        if (name) updateData.name = name;\n        if (config) updateData.config = JSON.stringify(config);\n        if (isActive !== undefined) updateData.isActive = isActive;\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.update({\n            where: {\n                id: params.id\n            },\n            data: updateData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            integration: {\n                ...integration,\n                config: integration.config ? JSON.parse(integration.config) : {}\n            }\n        });\n    } catch (error) {\n        console.error(\"Integration update error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update integration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/integrations/[id] - Delete integration\nasync function DELETE(request, { params }) {\n    try {\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Integration deletion error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete integration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/integrations/[id]/test - Test integration connection\nasync function POST(request, { params }) {\n    try {\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findUnique({\n            where: {\n                id: params.id\n            }\n        });\n        if (!integration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Integration not found\"\n            }, {\n                status: 404\n            });\n        }\n        const config = integration.config ? JSON.parse(integration.config) : {};\n        // Test the integration based on type\n        const testResult = await testIntegrationConnection(integration.type, config);\n        // Update last sync time if successful\n        if (testResult.success) {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.update({\n                where: {\n                    id: params.id\n                },\n                data: {\n                    lastSync: new Date()\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(testResult);\n    } catch (error) {\n        console.error(\"Integration test error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to test integration\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function testIntegrationConnection(type, config) {\n    try {\n        switch(type){\n            case \"HUBSPOT\":\n                return await testHubSpotConnection(config);\n            case \"SALESFORCE\":\n                return await testSalesforceConnection(config);\n            case \"MAILCHIMP\":\n                return await testMailchimpConnection(config);\n            case \"ZAPIER\":\n                return await testZapierConnection(config);\n            case \"WEBHOOK\":\n                return await testWebhookConnection(config);\n            default:\n                return {\n                    success: false,\n                    message: \"Unknown integration type\"\n                };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            message: `Connection test failed: ${error.message}`\n        };\n    }\n}\nasync function testHubSpotConnection(config) {\n    // In production, you would make an actual API call to HubSpot\n    // For now, just validate the config\n    if (!config.accessToken && !config.apiKey) {\n        return {\n            success: false,\n            message: \"Missing HubSpot credentials\"\n        };\n    }\n    return {\n        success: true,\n        message: \"HubSpot connection successful\"\n    };\n}\nasync function testSalesforceConnection(config) {\n    return {\n        success: true,\n        message: \"Salesforce connection test not implemented yet\"\n    };\n}\nasync function testMailchimpConnection(config) {\n    return {\n        success: true,\n        message: \"Mailchimp connection test not implemented yet\"\n    };\n}\nasync function testZapierConnection(config) {\n    return {\n        success: true,\n        message: \"Zapier webhook test not implemented yet\"\n    };\n}\nasync function testWebhookConnection(config) {\n    return {\n        success: true,\n        message: \"Webhook test not implemented yet\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/integrations/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydGZvcm0tZGVmZW5kZXIvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();