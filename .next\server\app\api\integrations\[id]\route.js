"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/integrations/[id]/route";
exports.ids = ["app/api/integrations/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/integrations/[id]/route.ts */ \"(rsc)/./src/app/api/integrations/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/integrations/[id]/route\",\n        pathname: \"/api/integrations/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/integrations/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\integrations\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_integrations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/integrations/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/integrations/[id]/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/integrations/[id]/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\n\n// GET /api/integrations/[id] - Get integration details\nasync function GET(request, { params }) {\n    try {\n        // Validate API key\n        const apiKey = request.headers.get(\"x-api-key\");\n        const project = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.validateApiKey)(apiKey);\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid API key\"\n            }, {\n                status: 401\n            });\n        }\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findFirst({\n            where: {\n                id: params.id,\n                projectId: project.id\n            },\n            include: {\n                project: {\n                    select: {\n                        name: true,\n                        domain: true\n                    }\n                }\n            }\n        });\n        if (!integration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Integration not found\"\n            }, {\n                status: 404\n            });\n        }\n        const response = {\n            ...integration,\n            config: integration.config ? JSON.parse(integration.config) : {}\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: response\n        });\n    } catch (error) {\n        console.error(\"Integration fetch error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch integration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/integrations/[id] - Update integration\nasync function PUT(request, { params }) {\n    try {\n        const body = await request.json();\n        const { name, config, isActive } = body;\n        const updateData = {\n            updatedAt: new Date()\n        };\n        if (name) updateData.name = name;\n        if (config) updateData.config = JSON.stringify(config);\n        if (isActive !== undefined) updateData.isActive = isActive;\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.update({\n            where: {\n                id: params.id\n            },\n            data: updateData\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                ...integration,\n                config: integration.config ? JSON.parse(integration.config) : {}\n            }\n        });\n    } catch (error) {\n        console.error(\"Integration update error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update integration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/integrations/[id] - Delete integration\nasync function DELETE(request, { params }) {\n    try {\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.delete({\n            where: {\n                id: params.id\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Integration deletion error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete integration\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/integrations/[id]/test - Test integration connection\nasync function POST(request, { params }) {\n    try {\n        const integration = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findUnique({\n            where: {\n                id: params.id\n            }\n        });\n        if (!integration) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Integration not found\"\n            }, {\n                status: 404\n            });\n        }\n        const config = integration.config ? JSON.parse(integration.config) : {};\n        // Test the integration based on type\n        const testResult = await testIntegrationConnection(integration.type, config);\n        // Update last sync time if successful\n        if (testResult.success) {\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.update({\n                where: {\n                    id: params.id\n                },\n                data: {\n                    lastSync: new Date()\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(testResult);\n    } catch (error) {\n        console.error(\"Integration test error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to test integration\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function testIntegrationConnection(type, config) {\n    try {\n        switch(type){\n            case \"HUBSPOT\":\n                return await testHubSpotConnection(config);\n            case \"SALESFORCE\":\n                return await testSalesforceConnection(config);\n            case \"MAILCHIMP\":\n                return await testMailchimpConnection(config);\n            case \"ZAPIER\":\n                return await testZapierConnection(config);\n            case \"WEBHOOK\":\n                return await testWebhookConnection(config);\n            default:\n                return {\n                    success: false,\n                    message: \"Unknown integration type\"\n                };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            message: `Connection test failed: ${error.message}`\n        };\n    }\n}\nasync function testHubSpotConnection(config) {\n    // In production, you would make an actual API call to HubSpot\n    // For now, just validate the config\n    if (!config.accessToken && !config.apiKey) {\n        return {\n            success: false,\n            message: \"Missing HubSpot credentials\"\n        };\n    }\n    return {\n        success: true,\n        message: \"HubSpot connection successful\"\n    };\n}\nasync function testSalesforceConnection(config) {\n    return {\n        success: true,\n        message: \"Salesforce connection test not implemented yet\"\n    };\n}\nasync function testMailchimpConnection(config) {\n    return {\n        success: true,\n        message: \"Mailchimp connection test not implemented yet\"\n    };\n}\nasync function testZapierConnection(config) {\n    return {\n        success: true,\n        message: \"Zapier webhook test not implemented yet\"\n    };\n}\nasync function testWebhookConnection(config) {\n    return {\n        success: true,\n        message: \"Webhook test not implemented yet\"\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/integrations/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\nasync function validateApiKey(apiKey) {\n    try {\n        if (!apiKey || !apiKey.startsWith(\"sfd_\")) {\n            return null;\n        }\n        // Find the API key and get the associated user's projects\n        const apiKeyRecord = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.findFirst({\n            where: {\n                key: apiKey,\n                isActive: true\n            },\n            include: {\n                user: {\n                    include: {\n                        projects: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!apiKeyRecord || !apiKeyRecord.user.projects.length) {\n            return null;\n        }\n        // Update last used timestamp\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.update({\n            where: {\n                id: apiKeyRecord.id\n            },\n            data: {\n                lastUsed: new Date()\n            }\n        });\n        // Return the first active project (in a real app, you might want to specify which project)\n        return apiKeyRecord.user.projects[0];\n    } catch (error) {\n        console.error(\"API key validation error:\", error);\n        return null;\n    }\n}\nfunction generateApiKey() {\n    return \"sfd_\" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydGZvcm0tZGVmZW5kZXIvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fintegrations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();