"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/validate/submission/route";
exports.ids = ["app/api/validate/submission/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_validate_submission_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/validate/submission/route.ts */ \"(rsc)/./src/app/api/validate/submission/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/validate/submission/route\",\n        pathname: \"/api/validate/submission\",\n        filename: \"route\",\n        bundlePath: \"app/api/validate/submission/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\validate\\\\submission\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_validate_submission_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/validate/submission/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/validate/submission/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/validate/submission/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_spam_detection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/spam-detection */ \"(rsc)/./src/lib/spam-detection.ts\");\n/* harmony import */ var _lib_integrations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/integrations */ \"(rsc)/./src/lib/integrations/index.ts\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./src/lib/validation.ts\");\n\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Get API key from headers\n        const apiKey = request.headers.get(\"X-API-Key\");\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API key required\"\n            }, {\n                status: 401\n            });\n        }\n        // Validate API key and get project\n        const project = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.validateApiKey)(apiKey);\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid API key\"\n            }, {\n                status: 401\n            });\n        }\n        // Parse request body\n        const body = await request.json();\n        const { formData, fingerprint, behaviorData, behaviorScore, metadata } = body;\n        // Validate required fields\n        if (!formData || !fingerprint || !metadata) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Get client IP\n        const clientIP = request.headers.get(\"x-forwarded-for\") || request.headers.get(\"x-real-ip\") || \"unknown\";\n        // Perform spam detection\n        const spamAnalysis = await (0,_lib_spam_detection__WEBPACK_IMPORTED_MODULE_3__.calculateSpamScore)({\n            formData,\n            fingerprint,\n            behaviorData,\n            behaviorScore,\n            metadata,\n            clientIP,\n            project\n        });\n        // Validate form data\n        const validationResults = await (0,_lib_validation__WEBPACK_IMPORTED_MODULE_5__.validateFormData)(formData, project);\n        // Determine if submission is valid\n        const isValid = spamAnalysis.spamScore < project.spamThreshold && validationResults.isValid;\n        const isSpam = spamAnalysis.spamScore >= project.spamThreshold;\n        // Store submission in database\n        const submission = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.create({\n            data: {\n                projectId: project.id,\n                formData: JSON.stringify(formData),\n                email: formData.email || null,\n                phone: formData.phone || null,\n                name: formData.name || null,\n                company: formData.company || null,\n                spamScore: spamAnalysis.spamScore,\n                isSpam,\n                isValid,\n                validationResults: JSON.stringify(validationResults),\n                ipAddress: clientIP,\n                userAgent: metadata.userAgent || null,\n                fingerprint: JSON.stringify(fingerprint),\n                country: spamAnalysis.geoData?.country || null,\n                city: spamAnalysis.geoData?.city || null,\n                status: isSpam ? \"SPAM\" : isValid ? \"VALID\" : \"PENDING\"\n            }\n        });\n        // Update analytics\n        await updateAnalytics(project.id, isSpam, isValid);\n        // Prepare response\n        const response = {\n            submissionId: submission.id,\n            isValid,\n            isSpam,\n            spamScore: spamAnalysis.spamScore,\n            validationResults,\n            reasons: spamAnalysis.reasons,\n            message: isSpam ? \"Submission blocked due to spam detection\" : isValid ? \"Submission validated successfully\" : \"Submission requires manual review\"\n        };\n        // If valid and not spam, trigger CRM integration\n        if (isValid && !isSpam) {\n            // Queue CRM integration (async)\n            processCRMIntegration(submission.id, project.id, formData);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Validation API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function updateAnalytics(projectId, isSpam, isValid) {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    try {\n        const analytics = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.analytics.upsert({\n            where: {\n                date_projectId: {\n                    date: today,\n                    projectId\n                }\n            },\n            update: {\n                totalSubmissions: {\n                    increment: 1\n                },\n                spamBlocked: isSpam ? {\n                    increment: 1\n                } : undefined,\n                validLeads: isValid && !isSpam ? {\n                    increment: 1\n                } : undefined\n            },\n            create: {\n                date: today,\n                projectId,\n                totalSubmissions: 1,\n                spamBlocked: isSpam ? 1 : 0,\n                validLeads: isValid && !isSpam ? 1 : 0,\n                conversionRate: 0,\n                hourlyData: JSON.stringify({})\n            }\n        });\n        // Update conversion rate\n        if (analytics.totalSubmissions > 0) {\n            const conversionRate = analytics.validLeads / analytics.totalSubmissions;\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.analytics.update({\n                where: {\n                    id: analytics.id\n                },\n                data: {\n                    conversionRate\n                }\n            });\n        }\n    } catch (error) {\n        console.error(\"Analytics update error:\", error);\n    }\n}\nasync function processCRMIntegration(submissionId, projectId, formData) {\n    try {\n        // Get project integrations\n        const integrations = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findMany({\n            where: {\n                projectId,\n                isActive: true\n            }\n        });\n        for (const integration of integrations){\n            try {\n                const leadData = {\n                    email: formData.email,\n                    name: formData.name,\n                    firstName: formData.firstName,\n                    lastName: formData.lastName,\n                    company: formData.company,\n                    phone: formData.phone,\n                    message: formData.message,\n                    website: formData.website,\n                    source: \"SmartForm Defender\",\n                    formData\n                };\n                const result = await _lib_integrations__WEBPACK_IMPORTED_MODULE_4__.IntegrationManager.sendToIntegration({\n                    type: integration.type,\n                    config: JSON.parse(integration.config)\n                }, leadData);\n                // Update submission with CRM response\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.update({\n                    where: {\n                        id: submissionId\n                    },\n                    data: {\n                        sentToCrm: result.success,\n                        crmResponse: JSON.stringify({\n                            provider: result.provider,\n                            success: result.success,\n                            message: result.message,\n                            contactId: result.contactId,\n                            dealId: result.dealId,\n                            timestamp: new Date().toISOString()\n                        })\n                    }\n                });\n                console.log(`${integration.type} integration result:`, result);\n            } catch (integrationError) {\n                console.error(`${integration.type} integration error:`, integrationError);\n                // Update submission with error\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.update({\n                    where: {\n                        id: submissionId\n                    },\n                    data: {\n                        sentToCrm: false,\n                        crmResponse: JSON.stringify({\n                            provider: integration.type,\n                            success: false,\n                            error: integrationError.message,\n                            timestamp: new Date().toISOString()\n                        })\n                    }\n                });\n            }\n        }\n    } catch (error) {\n        console.error(\"CRM integration error:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/validate/submission/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\nasync function validateApiKey(apiKey) {\n    try {\n        if (!apiKey || !apiKey.startsWith(\"sfd_\")) {\n            return null;\n        }\n        // Find the API key and get the associated user's projects\n        const apiKeyRecord = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.findFirst({\n            where: {\n                key: apiKey,\n                isActive: true\n            },\n            include: {\n                user: {\n                    include: {\n                        projects: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!apiKeyRecord || !apiKeyRecord.user.projects.length) {\n            return null;\n        }\n        // Update last used timestamp\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.update({\n            where: {\n                id: apiKeyRecord.id\n            },\n            data: {\n                lastUsed: new Date()\n            }\n        });\n        // Return the first active project (in a real app, you might want to specify which project)\n        return apiKeyRecord.user.projects[0];\n    } catch (error) {\n        console.error(\"API key validation error:\", error);\n        return null;\n    }\n}\nfunction generateApiKey() {\n    return \"sfd_\" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/hubspot.ts":
/*!*****************************************!*\
  !*** ./src/lib/integrations/hubspot.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HubSpotIntegration: () => (/* binding */ HubSpotIntegration)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nclass HubSpotIntegration {\n    constructor(config){\n        this.baseUrl = \"https://api.hubapi.com\";\n        this.config = config;\n    }\n    getAuthHeaders() {\n        if (this.config.accessToken) {\n            return {\n                \"Authorization\": `Bearer ${this.config.accessToken}`,\n                \"Content-Type\": \"application/json\"\n            };\n        } else if (this.config.apiKey) {\n            return {\n                \"Content-Type\": \"application/json\"\n            };\n        }\n        throw new Error(\"No valid authentication method provided\");\n    }\n    getApiUrl(endpoint) {\n        if (this.config.apiKey) {\n            const separator = endpoint.includes(\"?\") ? \"&\" : \"?\";\n            return `${this.baseUrl}${endpoint}${separator}hapikey=${this.config.apiKey}`;\n        }\n        return `${this.baseUrl}${endpoint}`;\n    }\n    async testConnection() {\n        try {\n            const url = this.getApiUrl(\"/contacts/v1/lists/all/contacts/all\");\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers,\n                params: {\n                    count: 1\n                }\n            });\n            return {\n                success: true,\n                message: \"HubSpot connection successful\",\n                data: {\n                    portalId: response.data?.[\"portal-id\"] || \"unknown\",\n                    hasContacts: response.data?.contacts?.length > 0\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `HubSpot connection failed: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createContact(contactData) {\n        try {\n            // Prepare contact properties for HubSpot API\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl(\"/contacts/v1/contact\");\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: \"Contact created successfully in HubSpot\",\n                data: {\n                    vid: response.data.vid,\n                    portalId: response.data[\"portal-id\"],\n                    isNew: response.data[\"is-new\"]\n                }\n            };\n        } catch (error) {\n            // Handle duplicate contact error\n            if (error.response?.status === 409) {\n                return await this.updateExistingContact(contactData, error.response.data);\n            }\n            return {\n                success: false,\n                message: `Failed to create HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async updateExistingContact(contactData, errorData) {\n        try {\n            // Extract existing contact ID from error response\n            const existingContactId = errorData.identityProfile?.vid;\n            if (!existingContactId) {\n                return {\n                    success: false,\n                    message: \"Contact already exists but could not retrieve contact ID\"\n                };\n            }\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl(`/contacts/v1/contact/vid/${existingContactId}/profile`);\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: existingContactId.toString(),\n                message: \"Existing contact updated successfully in HubSpot\",\n                data: {\n                    vid: existingContactId,\n                    isNew: false\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to update existing HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    formatContactProperties(contactData) {\n        const properties = [];\n        // Required fields\n        if (contactData.email) {\n            properties.push({\n                property: \"email\",\n                value: contactData.email\n            });\n        }\n        // Optional fields\n        if (contactData.firstname) {\n            properties.push({\n                property: \"firstname\",\n                value: contactData.firstname\n            });\n        }\n        if (contactData.lastname) {\n            properties.push({\n                property: \"lastname\",\n                value: contactData.lastname\n            });\n        }\n        if (contactData.company) {\n            properties.push({\n                property: \"company\",\n                value: contactData.company\n            });\n        }\n        if (contactData.phone) {\n            properties.push({\n                property: \"phone\",\n                value: contactData.phone\n            });\n        }\n        if (contactData.website) {\n            properties.push({\n                property: \"website\",\n                value: contactData.website\n            });\n        }\n        if (contactData.message) {\n            properties.push({\n                property: \"message\",\n                value: contactData.message\n            });\n        }\n        // Lead tracking\n        properties.push({\n            property: \"lead_source\",\n            value: contactData.lead_source || \"SmartForm Defender\"\n        });\n        properties.push({\n            property: \"hs_lead_status\",\n            value: contactData.hs_lead_status || \"NEW\"\n        });\n        // Add timestamp\n        properties.push({\n            property: \"createdate\",\n            value: new Date().getTime().toString()\n        });\n        return properties;\n    }\n    async getContact(email) {\n        try {\n            const url = this.getApiUrl(`/contacts/v1/contact/email/${encodeURIComponent(email)}/profile`);\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: \"Contact retrieved successfully\",\n                data: response.data\n            };\n        } catch (error) {\n            if (error.response?.status === 404) {\n                return {\n                    success: false,\n                    message: \"Contact not found in HubSpot\"\n                };\n            }\n            return {\n                success: false,\n                message: `Failed to retrieve HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createDeal(contactId, dealData) {\n        try {\n            const url = this.getApiUrl(\"/deals/v1/deal\");\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: [\n                    {\n                        name: \"dealname\",\n                        value: dealData.dealName || \"SmartForm Lead\"\n                    },\n                    {\n                        name: \"dealstage\",\n                        value: dealData.dealStage || \"appointmentscheduled\"\n                    },\n                    {\n                        name: \"pipeline\",\n                        value: dealData.pipeline || \"default\"\n                    },\n                    {\n                        name: \"amount\",\n                        value: dealData.amount || \"0\"\n                    },\n                    {\n                        name: \"closedate\",\n                        value: dealData.closeDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime()\n                    },\n                    {\n                        name: \"source\",\n                        value: \"SmartForm Defender\"\n                    }\n                ],\n                associations: {\n                    associatedVids: [\n                        parseInt(contactId)\n                    ]\n                }\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                message: \"Deal created successfully in HubSpot\",\n                data: {\n                    dealId: response.data.dealId,\n                    portalId: response.data.portalId\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to create HubSpot deal: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/hubspot.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/index.ts":
/*!***************************************!*\
  !*** ./src/lib/integrations/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationManager: () => (/* binding */ IntegrationManager)\n/* harmony export */ });\n/* harmony import */ var _hubspot__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hubspot */ \"(rsc)/./src/lib/integrations/hubspot.ts\");\n\nclass IntegrationManager {\n    static async sendToIntegration(integration, leadData) {\n        try {\n            switch(integration.type){\n                case \"HUBSPOT\":\n                    return await this.sendToHubSpot(integration.config, leadData);\n                case \"SALESFORCE\":\n                    return await this.sendToSalesforce(integration.config, leadData);\n                case \"MAILCHIMP\":\n                    return await this.sendToMailchimp(integration.config, leadData);\n                case \"ZAPIER\":\n                    return await this.sendToZapier(integration.config, leadData);\n                case \"WEBHOOK\":\n                    return await this.sendToWebhook(integration.config, leadData);\n                default:\n                    return {\n                        success: false,\n                        message: `Unsupported integration type: ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Integration error: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n    static async sendToHubSpot(config, leadData) {\n        const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(config);\n        // Parse name into first and last name if needed\n        const { firstName, lastName } = this.parseName(leadData.name, leadData.firstName, leadData.lastName);\n        const contactData = {\n            email: leadData.email,\n            firstname: firstName,\n            lastname: lastName,\n            company: leadData.company,\n            phone: leadData.phone,\n            website: leadData.website,\n            message: leadData.message,\n            lead_source: leadData.source || \"SmartForm Defender\",\n            hs_lead_status: \"NEW\"\n        };\n        const result = await hubspot.createContact(contactData);\n        return {\n            success: result.success,\n            message: result.message,\n            contactId: result.contactId,\n            data: result.data,\n            provider: \"HUBSPOT\"\n        };\n    }\n    static async sendToSalesforce(config, leadData) {\n        // Placeholder for Salesforce integration\n        return {\n            success: false,\n            message: \"Salesforce integration not implemented yet\",\n            provider: \"SALESFORCE\"\n        };\n    }\n    static async sendToMailchimp(config, leadData) {\n        // Placeholder for Mailchimp integration\n        return {\n            success: false,\n            message: \"Mailchimp integration not implemented yet\",\n            provider: \"MAILCHIMP\"\n        };\n    }\n    static async sendToZapier(config, leadData) {\n        try {\n            const response = await fetch(config.webhookUrl, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: \"SmartForm Defender\"\n                })\n            });\n            if (response.ok) {\n                return {\n                    success: true,\n                    message: \"Lead sent to Zapier successfully\",\n                    provider: \"ZAPIER\"\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Zapier webhook failed: ${response.statusText}`,\n                    provider: \"ZAPIER\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Zapier webhook error: ${error.message}`,\n                provider: \"ZAPIER\"\n            };\n        }\n    }\n    static async sendToWebhook(config, leadData) {\n        try {\n            const headers = {\n                \"Content-Type\": \"application/json\",\n                ...config.headers\n            };\n            const response = await fetch(config.url, {\n                method: config.method || \"POST\",\n                headers,\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: \"SmartForm Defender\"\n                })\n            });\n            if (response.ok) {\n                const responseData = await response.text();\n                return {\n                    success: true,\n                    message: \"Lead sent to webhook successfully\",\n                    data: responseData,\n                    provider: \"WEBHOOK\"\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Webhook failed: ${response.statusText}`,\n                    provider: \"WEBHOOK\"\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Webhook error: ${error.message}`,\n                provider: \"WEBHOOK\"\n            };\n        }\n    }\n    static parseName(fullName, firstName, lastName) {\n        if (firstName && lastName) {\n            return {\n                firstName,\n                lastName\n            };\n        }\n        if (fullName) {\n            const parts = fullName.trim().split(\" \");\n            return {\n                firstName: parts[0] || \"\",\n                lastName: parts.slice(1).join(\" \") || \"\"\n            };\n        }\n        return {\n            firstName: firstName || \"\",\n            lastName: lastName || \"\"\n        };\n    }\n    static async testIntegration(integration) {\n        try {\n            switch(integration.type){\n                case \"HUBSPOT\":\n                    const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(integration.config);\n                    const result = await hubspot.testConnection();\n                    return {\n                        success: result.success,\n                        message: result.message,\n                        data: result.data,\n                        provider: \"HUBSPOT\"\n                    };\n                default:\n                    return {\n                        success: false,\n                        message: `Test not implemented for ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Test failed: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUF5QixFQUFjSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zbWFydGZvcm0tZGVmZW5kZXIvLi9zcmMvbGliL3ByaXNtYS50cz8wMWQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcblxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHtcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59O1xuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KCk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/spam-detection.ts":
/*!***********************************!*\
  !*** ./src/lib/spam-detection.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSpamScore: () => (/* binding */ calculateSpamScore)\n/* harmony export */ });\n/**\n * Spam Detection Engine\n * Advanced algorithms for detecting spam and bot submissions\n */ async function calculateSpamScore(input) {\n    const reasons = [];\n    let totalScore = 0;\n    const riskFactors = {\n        behavioral: 0,\n        content: 0,\n        technical: 0,\n        temporal: 0\n    };\n    // 1. Behavioral Analysis (30% weight)\n    const behavioralScore = analyzeBehavior(input.behaviorData, input.behaviorScore);\n    riskFactors.behavioral = behavioralScore;\n    totalScore += behavioralScore * 0.3;\n    if (behavioralScore > 0.5) {\n        reasons.push(\"Suspicious user behavior detected\");\n    }\n    // 2. Content Analysis (25% weight)\n    const contentScore = analyzeContent(input.formData);\n    riskFactors.content = contentScore;\n    totalScore += contentScore * 0.25;\n    if (contentScore > 0.5) {\n        reasons.push(\"Suspicious content patterns detected\");\n    }\n    // 3. Technical Fingerprinting (25% weight)\n    const technicalScore = analyzeTechnicalFingerprint(input.fingerprint, input.metadata);\n    riskFactors.technical = technicalScore;\n    totalScore += technicalScore * 0.25;\n    if (technicalScore > 0.5) {\n        reasons.push(\"Suspicious technical fingerprint\");\n    }\n    // 4. Temporal Analysis (20% weight)\n    const temporalScore = analyzeTemporalPatterns(input.behaviorData, input.metadata);\n    riskFactors.temporal = temporalScore;\n    totalScore += temporalScore * 0.2;\n    if (temporalScore > 0.5) {\n        reasons.push(\"Suspicious timing patterns\");\n    }\n    // 5. IP and Geo Analysis\n    const geoData = await analyzeGeoLocation(input.clientIP);\n    if (geoData && input.project.enableGeoValidation) {\n        const geoScore = analyzeGeoRisk(geoData, input.project);\n        totalScore += geoScore * 0.1;\n        if (geoScore > 0.5) {\n            reasons.push(`Submission from restricted location: ${geoData.country}`);\n        }\n    }\n    // 6. Historical Analysis\n    const historicalScore = await analyzeHistoricalData(input.clientIP, input.fingerprint);\n    totalScore += historicalScore * 0.1;\n    if (historicalScore > 0.5) {\n        reasons.push(\"Previous suspicious activity detected\");\n    }\n    // Cap the score at 1.0\n    const finalScore = Math.min(totalScore, 1.0);\n    return {\n        spamScore: finalScore,\n        reasons,\n        geoData,\n        riskFactors\n    };\n}\nfunction analyzeBehavior(behaviorData, behaviorScore) {\n    let score = behaviorScore; // Base score from client-side analysis\n    const { mouseMovements, keystrokes, focusEvents, startTime, interactions } = behaviorData;\n    const timeSpent = Date.now() - startTime;\n    // Mouse movement analysis\n    if (mouseMovements.length === 0) {\n        score += 0.4; // No mouse movement is highly suspicious\n    } else if (mouseMovements.length < 5) {\n        score += 0.2; // Very few movements\n    } else {\n        // Analyze movement patterns\n        const movements = mouseMovements.slice(-20); // Last 20 movements\n        let straightLines = 0;\n        let perfectCurves = 0;\n        for(let i = 2; i < movements.length; i++){\n            const dx1 = movements[i - 1].x - movements[i - 2].x;\n            const dy1 = movements[i - 1].y - movements[i - 2].y;\n            const dx2 = movements[i].x - movements[i - 1].x;\n            const dy2 = movements[i].y - movements[i - 1].y;\n            // Check for perfectly straight lines (bot-like)\n            if (dx1 !== 0 && dy1 !== 0 && dx2 !== 0 && dy2 !== 0) {\n                const slope1 = dy1 / dx1;\n                const slope2 = dy2 / dx2;\n                if (Math.abs(slope1 - slope2) < 0.01) {\n                    straightLines++;\n                }\n            }\n        }\n        if (straightLines > movements.length * 0.7) {\n            score += 0.3; // Too many straight lines\n        }\n    }\n    // Keystroke analysis\n    if (keystrokes.length > 0) {\n        const intervals = keystrokes.map((k)=>k.interval).filter((i)=>i > 0);\n        if (intervals.length > 5) {\n            const avgInterval = intervals.reduce((a, b)=>a + b, 0) / intervals.length;\n            const variance = intervals.reduce((acc, val)=>acc + Math.pow(val - avgInterval, 2), 0) / intervals.length;\n            // Very consistent typing intervals suggest automation\n            if (variance < 100 && avgInterval < 50) {\n                score += 0.3;\n            }\n        }\n    }\n    // Time-based analysis\n    if (timeSpent < 1000) {\n        score += 0.4; // Submitted too quickly\n    } else if (timeSpent < 3000) {\n        score += 0.2; // Still quite fast\n    }\n    // Interaction analysis\n    if (interactions < 2) {\n        score += 0.3; // Very few interactions with form\n    }\n    return Math.min(score, 1.0);\n}\nfunction analyzeContent(formData) {\n    let score = 0;\n    // Common spam patterns\n    const spamPatterns = [\n        /viagra|cialis|pharmacy/i,\n        /make money|earn \\$|work from home/i,\n        /click here|visit now|act now/i,\n        /free trial|limited time|urgent/i,\n        /congratulations|winner|selected/i\n    ];\n    // Check all text fields\n    Object.values(formData).forEach((value)=>{\n        if (typeof value === \"string\") {\n            const text = value.toLowerCase();\n            // Check for spam patterns\n            spamPatterns.forEach((pattern)=>{\n                if (pattern.test(text)) {\n                    score += 0.2;\n                }\n            });\n            // Check for excessive URLs\n            const urlCount = (text.match(/https?:\\/\\/[^\\s]+/g) || []).length;\n            if (urlCount > 2) {\n                score += 0.3;\n            }\n            // Check for excessive special characters\n            const specialCharRatio = (text.match(/[^a-zA-Z0-9\\s]/g) || []).length / text.length;\n            if (specialCharRatio > 0.3) {\n                score += 0.2;\n            }\n            // Check for repeated characters\n            if (/(.)\\1{4,}/.test(text)) {\n                score += 0.2;\n            }\n            // Check for all caps\n            if (text.length > 10 && text === text.toUpperCase()) {\n                score += 0.1;\n            }\n        }\n    });\n    // Check for honeypot fields\n    if (formData.website || formData.url || formData.homepage) {\n        score += 0.8; // Honeypot field filled\n    }\n    return Math.min(score, 1.0);\n}\nfunction analyzeTechnicalFingerprint(fingerprint, metadata) {\n    let score = 0;\n    // User agent analysis\n    const userAgent = metadata.userAgent || \"\";\n    // Check for bot user agents\n    const botPatterns = [\n        /bot|crawler|spider|scraper/i,\n        /curl|wget|python|java/i,\n        /headless|phantom|selenium/i\n    ];\n    if (botPatterns.some((pattern)=>pattern.test(userAgent))) {\n        score += 0.6;\n    }\n    // Check for missing or suspicious browser features\n    if (!fingerprint.cookieEnabled) {\n        score += 0.2;\n    }\n    if (fingerprint.doNotTrack === \"1\") {\n        score += 0.1; // Slight increase for privacy-conscious users\n    }\n    // Screen resolution analysis\n    if (fingerprint.screen) {\n        const [width, height] = fingerprint.screen.split(\"x\").map(Number);\n        // Common bot resolutions\n        if (width === 1024 && height === 768 || width === 1920 && height === 1080 && !userAgent.includes(\"Windows\")) {\n            score += 0.2;\n        }\n    }\n    // Canvas fingerprint analysis\n    if (fingerprint.canvas) {\n        // Very short canvas fingerprints suggest blocking or automation\n        if (fingerprint.canvas.length < 100) {\n            score += 0.3;\n        }\n    }\n    // Language/timezone mismatch\n    if (fingerprint.language && fingerprint.timezone) {\n        // This would require a lookup table of language-timezone correlations\n        // For now, just check for obvious mismatches\n        if (fingerprint.language.startsWith(\"en\") && !fingerprint.timezone.includes(\"America\") && !fingerprint.timezone.includes(\"Europe\")) {\n            score += 0.1;\n        }\n    }\n    return Math.min(score, 1.0);\n}\nfunction analyzeTemporalPatterns(behaviorData, metadata) {\n    let score = 0;\n    const submissionTime = new Date(metadata.timestamp);\n    const hour = submissionTime.getHours();\n    // Submissions during unusual hours (2-6 AM) are slightly more suspicious\n    if (hour >= 2 && hour <= 6) {\n        score += 0.1;\n    }\n    // Check for rapid-fire submissions (would need database lookup)\n    // This is a placeholder for rate limiting logic\n    return Math.min(score, 1.0);\n}\nasync function analyzeGeoLocation(ip) {\n    try {\n        // In production, you would use a real IP geolocation service\n        // For now, return mock data\n        return {\n            country: \"US\",\n            city: \"Unknown\"\n        };\n    } catch (error) {\n        console.error(\"Geo location error:\", error);\n        return null;\n    }\n}\nfunction analyzeGeoRisk(geoData, project) {\n    let score = 0;\n    // Check blocked countries\n    if (project.blockedCountries) {\n        const blockedCountries = project.blockedCountries.split(\",\").map((c)=>c.trim());\n        if (blockedCountries.includes(geoData.country)) {\n            score += 0.8;\n        }\n    }\n    // Check allowed countries\n    if (project.allowedCountries) {\n        const allowedCountries = project.allowedCountries.split(\",\").map((c)=>c.trim());\n        if (allowedCountries.length > 0 && !allowedCountries.includes(geoData.country)) {\n            score += 0.6;\n        }\n    }\n    return Math.min(score, 1.0);\n}\nasync function analyzeHistoricalData(ip, fingerprint) {\n    try {\n        // In production, check database for previous submissions from this IP/fingerprint\n        // For now, return 0\n        return 0;\n    } catch (error) {\n        console.error(\"Historical analysis error:\", error);\n        return 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/spam-detection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validation.ts":
/*!*******************************!*\
  !*** ./src/lib/validation.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSpamScore: () => (/* binding */ calculateSpamScore),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   phoneSchema: () => (/* binding */ phoneSchema),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateFormData: () => (/* binding */ validateFormData),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n// Validation schemas\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email();\nconst phoneSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/);\n// Utility functions\nfunction generateApiKey() {\n    return \"sfd_\" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\nfunction validateEmail(email) {\n    return emailSchema.safeParse(email).success;\n}\nfunction validatePhone(phone) {\n    const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, \"\");\n    return phoneSchema.safeParse(cleanPhone).success;\n}\nasync function validateFormData(formData, project) {\n    const errors = {};\n    const warnings = {};\n    // Email validation\n    if (formData.email) {\n        if (!validateEmail(formData.email)) {\n            errors.email = \"Invalid email format\";\n        } else if (project.enableEmailValidation) {\n            // Additional email validation would go here\n            const emailValidation = await validateEmailAdvanced(formData.email);\n            if (!emailValidation.valid) {\n                errors.email = emailValidation.message;\n            }\n        }\n    }\n    // Phone validation\n    if (formData.phone) {\n        if (!validatePhone(formData.phone)) {\n            errors.phone = \"Invalid phone format\";\n        } else if (project.enablePhoneValidation) {\n            const phoneValidation = await validatePhoneAdvanced(formData.phone);\n            if (!phoneValidation.valid) {\n                errors.phone = phoneValidation.message;\n            }\n        }\n    }\n    // Required field validation\n    const requiredFields = [\n        \"email\",\n        \"name\"\n    ];\n    requiredFields.forEach((field)=>{\n        if (!formData[field] || formData[field].trim() === \"\") {\n            errors[field] = `${field} is required`;\n        }\n    });\n    // Content validation\n    if (formData.message && formData.message.length < 10) {\n        warnings.message = \"Message seems too short\";\n    }\n    if (formData.name && formData.name.length < 2) {\n        warnings.name = \"Name seems too short\";\n    }\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors,\n        warnings\n    };\n}\nasync function validateEmailAdvanced(email) {\n    try {\n        // In production, you would use a real email validation service\n        // For now, just do basic checks\n        // Check for disposable email domains\n        const disposableDomains = [\n            \"10minutemail.com\",\n            \"tempmail.org\",\n            \"guerrillamail.com\",\n            \"mailinator.com\"\n        ];\n        const domain = email.split(\"@\")[1];\n        if (disposableDomains.includes(domain)) {\n            return {\n                valid: false,\n                message: \"Disposable email addresses are not allowed\"\n            };\n        }\n        // Check for common typos in popular domains\n        const commonDomains = {\n            \"gmial.com\": \"gmail.com\",\n            \"gmai.com\": \"gmail.com\",\n            \"yahooo.com\": \"yahoo.com\",\n            \"hotmial.com\": \"hotmail.com\"\n        };\n        if (commonDomains[domain]) {\n            return {\n                valid: false,\n                message: `Did you mean ${email.replace(domain, commonDomains[domain])}?`\n            };\n        }\n        return {\n            valid: true,\n            message: \"Email is valid\"\n        };\n    } catch (error) {\n        return {\n            valid: true,\n            message: \"Could not validate email\"\n        };\n    }\n}\nasync function validatePhoneAdvanced(phone) {\n    try {\n        // In production, you would use a phone validation service\n        const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, \"\");\n        // Basic length check\n        if (cleanPhone.length < 7 || cleanPhone.length > 15) {\n            return {\n                valid: false,\n                message: \"Phone number length is invalid\"\n            };\n        }\n        return {\n            valid: true,\n            message: \"Phone number is valid\"\n        };\n    } catch (error) {\n        return {\n            valid: true,\n            message: \"Could not validate phone number\"\n        };\n    }\n}\nfunction calculateSpamScore(data) {\n    let score = 0;\n    // Basic spam detection logic\n    if (data.email && data.email.includes(\"test\")) score += 0.2;\n    if (data.message && data.message.length < 10) score += 0.3;\n    if (data.name && data.name.length < 2) score += 0.2;\n    return Math.min(score, 1);\n}\nfunction formatDate(date) {\n    return date.toLocaleDateString();\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat().format(num);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();