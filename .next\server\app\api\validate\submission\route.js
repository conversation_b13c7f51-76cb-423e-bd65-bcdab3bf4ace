/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/validate/submission/route";
exports.ids = ["app/api/validate/submission/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_validate_submission_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/validate/submission/route.ts */ \"(rsc)/./src/app/api/validate/submission/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/validate/submission/route\",\n        pathname: \"/api/validate/submission\",\n        filename: \"route\",\n        bundlePath: \"app/api/validate/submission/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\validate\\\\submission\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_validate_submission_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/validate/submission/route.ts":
/*!**************************************************!*\
  !*** ./src/app/api/validate/submission/route.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_spam_detection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/spam-detection */ \"(rsc)/./src/lib/spam-detection.ts\");\n/* harmony import */ var _lib_integrations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/integrations */ \"(rsc)/./src/lib/integrations/index.ts\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/validation */ \"(rsc)/./src/lib/validation.ts\");\n\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Get API key from headers\n        const apiKey = request.headers.get('X-API-Key');\n        if (!apiKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API key required'\n            }, {\n                status: 401\n            });\n        }\n        // Validate API key and get project\n        const project = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.validateApiKey)(apiKey);\n        if (!project) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid API key'\n            }, {\n                status: 401\n            });\n        }\n        // Parse request body\n        const body = await request.json();\n        const { formData, fingerprint, behaviorData, behaviorScore, metadata } = body;\n        // Validate required fields\n        if (!formData || !fingerprint || !metadata) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Get client IP\n        const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';\n        // Perform spam detection\n        const spamAnalysis = await (0,_lib_spam_detection__WEBPACK_IMPORTED_MODULE_3__.calculateSpamScore)({\n            formData,\n            fingerprint,\n            behaviorData,\n            behaviorScore,\n            metadata,\n            clientIP,\n            project\n        });\n        // Validate form data\n        const validationResults = await (0,_lib_validation__WEBPACK_IMPORTED_MODULE_5__.validateFormData)(formData, project);\n        // Determine if submission is valid\n        const isValid = spamAnalysis.spamScore < project.spamThreshold && validationResults.isValid;\n        const isSpam = spamAnalysis.spamScore >= project.spamThreshold;\n        // Store submission in database\n        const submission = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.create({\n            data: {\n                projectId: project.id,\n                formData: JSON.stringify(formData),\n                email: formData.email || null,\n                phone: formData.phone || null,\n                name: formData.name || null,\n                company: formData.company || null,\n                spamScore: spamAnalysis.spamScore,\n                isSpam,\n                isValid,\n                validationResults: JSON.stringify(validationResults),\n                ipAddress: clientIP,\n                userAgent: metadata.userAgent || null,\n                fingerprint: JSON.stringify(fingerprint),\n                country: spamAnalysis.geoData?.country || null,\n                city: spamAnalysis.geoData?.city || null,\n                status: isSpam ? 'SPAM' : isValid ? 'VALID' : 'PENDING'\n            }\n        });\n        // Update analytics\n        await updateAnalytics(project.id, isSpam, isValid);\n        // Prepare response\n        const response = {\n            submissionId: submission.id,\n            isValid,\n            isSpam,\n            spamScore: spamAnalysis.spamScore,\n            validationResults,\n            reasons: spamAnalysis.reasons,\n            message: isSpam ? 'Submission blocked due to spam detection' : isValid ? 'Submission validated successfully' : 'Submission requires manual review'\n        };\n        // If valid and not spam, trigger CRM integration\n        if (isValid && !isSpam) {\n            // Queue CRM integration (async)\n            processCRMIntegration(submission.id, project.id, formData);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Validation API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function updateAnalytics(projectId, isSpam, isValid) {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    try {\n        const analytics = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.analytics.upsert({\n            where: {\n                date_projectId: {\n                    date: today,\n                    projectId\n                }\n            },\n            update: {\n                totalSubmissions: {\n                    increment: 1\n                },\n                spamBlocked: isSpam ? {\n                    increment: 1\n                } : undefined,\n                validLeads: isValid && !isSpam ? {\n                    increment: 1\n                } : undefined\n            },\n            create: {\n                date: today,\n                projectId,\n                totalSubmissions: 1,\n                spamBlocked: isSpam ? 1 : 0,\n                validLeads: isValid && !isSpam ? 1 : 0,\n                conversionRate: 0,\n                hourlyData: JSON.stringify({})\n            }\n        });\n        // Update conversion rate\n        if (analytics.totalSubmissions > 0) {\n            const conversionRate = analytics.validLeads / analytics.totalSubmissions;\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.analytics.update({\n                where: {\n                    id: analytics.id\n                },\n                data: {\n                    conversionRate\n                }\n            });\n        }\n    } catch (error) {\n        console.error('Analytics update error:', error);\n    }\n}\nasync function processCRMIntegration(submissionId, projectId, formData) {\n    try {\n        // Get project integrations\n        const integrations = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.integration.findMany({\n            where: {\n                projectId,\n                isActive: true\n            }\n        });\n        for (const integration of integrations){\n            try {\n                const leadData = {\n                    email: formData.email,\n                    name: formData.name,\n                    firstName: formData.firstName,\n                    lastName: formData.lastName,\n                    company: formData.company,\n                    phone: formData.phone,\n                    message: formData.message,\n                    website: formData.website,\n                    source: 'SmartForm Defender',\n                    formData\n                };\n                const result = await _lib_integrations__WEBPACK_IMPORTED_MODULE_4__.IntegrationManager.sendToIntegration({\n                    type: integration.type,\n                    config: JSON.parse(integration.config)\n                }, leadData);\n                // Update submission with CRM response\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.update({\n                    where: {\n                        id: submissionId\n                    },\n                    data: {\n                        sentToCrm: result.success,\n                        crmResponse: JSON.stringify({\n                            provider: result.provider,\n                            success: result.success,\n                            message: result.message,\n                            contactId: result.contactId,\n                            dealId: result.dealId,\n                            timestamp: new Date().toISOString()\n                        })\n                    }\n                });\n                console.log(`${integration.type} integration result:`, result);\n            } catch (integrationError) {\n                console.error(`${integration.type} integration error:`, integrationError);\n                // Update submission with error\n                await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.update({\n                    where: {\n                        id: submissionId\n                    },\n                    data: {\n                        sentToCrm: false,\n                        crmResponse: JSON.stringify({\n                            provider: integration.type,\n                            success: false,\n                            error: integrationError.message,\n                            timestamp: new Date().toISOString()\n                        })\n                    }\n                });\n            }\n        }\n    } catch (error) {\n        console.error('CRM integration error:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/validate/submission/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   validateApiKey: () => (/* binding */ validateApiKey)\n/* harmony export */ });\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\nasync function validateApiKey(apiKey) {\n    try {\n        if (!apiKey || !apiKey.startsWith('sfd_')) {\n            return null;\n        }\n        // Find the API key and get the associated user's projects\n        const apiKeyRecord = await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.findFirst({\n            where: {\n                key: apiKey,\n                isActive: true\n            },\n            include: {\n                user: {\n                    include: {\n                        projects: {\n                            where: {\n                                isActive: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        if (!apiKeyRecord || !apiKeyRecord.user.projects.length) {\n            return null;\n        }\n        // Update last used timestamp\n        await _prisma__WEBPACK_IMPORTED_MODULE_0__.prisma.apiKey.update({\n            where: {\n                id: apiKeyRecord.id\n            },\n            data: {\n                lastUsed: new Date()\n            }\n        });\n        // Return the first active project (in a real app, you might want to specify which project)\n        return apiKeyRecord.user.projects[0];\n    } catch (error) {\n        console.error('API key validation error:', error);\n        return null;\n    }\n}\nfunction generateApiKey() {\n    return 'sfd_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/hubspot.ts":
/*!*****************************************!*\
  !*** ./src/lib/integrations/hubspot.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HubSpotIntegration: () => (/* binding */ HubSpotIntegration)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n\nclass HubSpotIntegration {\n    constructor(config){\n        this.baseUrl = 'https://api.hubapi.com';\n        this.config = config;\n    }\n    getAuthHeaders() {\n        if (this.config.accessToken) {\n            return {\n                'Authorization': `Bearer ${this.config.accessToken}`,\n                'Content-Type': 'application/json'\n            };\n        } else if (this.config.apiKey) {\n            return {\n                'Content-Type': 'application/json'\n            };\n        }\n        throw new Error('No valid authentication method provided');\n    }\n    getApiUrl(endpoint) {\n        if (this.config.apiKey) {\n            const separator = endpoint.includes('?') ? '&' : '?';\n            return `${this.baseUrl}${endpoint}${separator}hapikey=${this.config.apiKey}`;\n        }\n        return `${this.baseUrl}${endpoint}`;\n    }\n    async testConnection() {\n        try {\n            const url = this.getApiUrl('/contacts/v1/lists/all/contacts/all');\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers,\n                params: {\n                    count: 1\n                }\n            });\n            return {\n                success: true,\n                message: 'HubSpot connection successful',\n                data: {\n                    portalId: response.data?.['portal-id'] || 'unknown',\n                    hasContacts: response.data?.contacts?.length > 0\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `HubSpot connection failed: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createContact(contactData) {\n        try {\n            // Prepare contact properties for HubSpot API\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl('/contacts/v1/contact');\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: 'Contact created successfully in HubSpot',\n                data: {\n                    vid: response.data.vid,\n                    portalId: response.data['portal-id'],\n                    isNew: response.data['is-new']\n                }\n            };\n        } catch (error) {\n            // Handle duplicate contact error\n            if (error.response?.status === 409) {\n                return await this.updateExistingContact(contactData, error.response.data);\n            }\n            return {\n                success: false,\n                message: `Failed to create HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async updateExistingContact(contactData, errorData) {\n        try {\n            // Extract existing contact ID from error response\n            const existingContactId = errorData.identityProfile?.vid;\n            if (!existingContactId) {\n                return {\n                    success: false,\n                    message: 'Contact already exists but could not retrieve contact ID'\n                };\n            }\n            const properties = this.formatContactProperties(contactData);\n            const url = this.getApiUrl(`/contacts/v1/contact/vid/${existingContactId}/profile`);\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: properties\n            };\n            await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: existingContactId.toString(),\n                message: 'Existing contact updated successfully in HubSpot',\n                data: {\n                    vid: existingContactId,\n                    isNew: false\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to update existing HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    formatContactProperties(contactData) {\n        const properties = [];\n        // Required fields\n        if (contactData.email) {\n            properties.push({\n                property: 'email',\n                value: contactData.email\n            });\n        }\n        // Optional fields\n        if (contactData.firstname) {\n            properties.push({\n                property: 'firstname',\n                value: contactData.firstname\n            });\n        }\n        if (contactData.lastname) {\n            properties.push({\n                property: 'lastname',\n                value: contactData.lastname\n            });\n        }\n        if (contactData.company) {\n            properties.push({\n                property: 'company',\n                value: contactData.company\n            });\n        }\n        if (contactData.phone) {\n            properties.push({\n                property: 'phone',\n                value: contactData.phone\n            });\n        }\n        if (contactData.website) {\n            properties.push({\n                property: 'website',\n                value: contactData.website\n            });\n        }\n        if (contactData.message) {\n            properties.push({\n                property: 'message',\n                value: contactData.message\n            });\n        }\n        // Lead tracking\n        properties.push({\n            property: 'lead_source',\n            value: contactData.lead_source || 'SmartForm Defender'\n        });\n        properties.push({\n            property: 'hs_lead_status',\n            value: contactData.hs_lead_status || 'NEW'\n        });\n        // Add timestamp\n        properties.push({\n            property: 'createdate',\n            value: new Date().getTime().toString()\n        });\n        return properties;\n    }\n    async getContact(email) {\n        try {\n            const url = this.getApiUrl(`/contacts/v1/contact/email/${encodeURIComponent(email)}/profile`);\n            const headers = this.getAuthHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                headers\n            });\n            return {\n                success: true,\n                contactId: response.data.vid?.toString(),\n                message: 'Contact retrieved successfully',\n                data: response.data\n            };\n        } catch (error) {\n            if (error.response?.status === 404) {\n                return {\n                    success: false,\n                    message: 'Contact not found in HubSpot'\n                };\n            }\n            return {\n                success: false,\n                message: `Failed to retrieve HubSpot contact: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n    async createDeal(contactId, dealData) {\n        try {\n            const url = this.getApiUrl('/deals/v1/deal');\n            const headers = this.getAuthHeaders();\n            const payload = {\n                properties: [\n                    {\n                        name: 'dealname',\n                        value: dealData.dealName || 'SmartForm Lead'\n                    },\n                    {\n                        name: 'dealstage',\n                        value: dealData.dealStage || 'appointmentscheduled'\n                    },\n                    {\n                        name: 'pipeline',\n                        value: dealData.pipeline || 'default'\n                    },\n                    {\n                        name: 'amount',\n                        value: dealData.amount || '0'\n                    },\n                    {\n                        name: 'closedate',\n                        value: dealData.closeDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).getTime()\n                    },\n                    {\n                        name: 'source',\n                        value: 'SmartForm Defender'\n                    }\n                ],\n                associations: {\n                    associatedVids: [\n                        parseInt(contactId)\n                    ]\n                }\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, payload, {\n                headers\n            });\n            return {\n                success: true,\n                message: 'Deal created successfully in HubSpot',\n                data: {\n                    dealId: response.data.dealId,\n                    portalId: response.data.portalId\n                }\n            };\n        } catch (error) {\n            return {\n                success: false,\n                message: `Failed to create HubSpot deal: ${error.response?.data?.message || error.message}`\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2ludGVncmF0aW9ucy9odWJzcG90LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBCO0FBMkJuQixNQUFNQztJQUlYQyxZQUFZQyxNQUFxQixDQUFFO2FBRjNCQyxVQUFVO1FBR2hCLElBQUksQ0FBQ0QsTUFBTSxHQUFHQTtJQUNoQjtJQUVRRSxpQkFBeUM7UUFDL0MsSUFBSSxJQUFJLENBQUNGLE1BQU0sQ0FBQ0csV0FBVyxFQUFFO1lBQzNCLE9BQU87Z0JBQ0wsaUJBQWlCLENBQUMsT0FBTyxFQUFFLElBQUksQ0FBQ0gsTUFBTSxDQUFDRyxXQUFXLEVBQUU7Z0JBQ3BELGdCQUFnQjtZQUNsQjtRQUNGLE9BQU8sSUFBSSxJQUFJLENBQUNILE1BQU0sQ0FBQ0ksTUFBTSxFQUFFO1lBQzdCLE9BQU87Z0JBQ0wsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFDQSxNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFFUUMsVUFBVUMsUUFBZ0IsRUFBVTtRQUMxQyxJQUFJLElBQUksQ0FBQ1AsTUFBTSxDQUFDSSxNQUFNLEVBQUU7WUFDdEIsTUFBTUksWUFBWUQsU0FBU0UsUUFBUSxDQUFDLE9BQU8sTUFBTTtZQUNqRCxPQUFPLEdBQUcsSUFBSSxDQUFDUixPQUFPLEdBQUdNLFdBQVdDLFVBQVUsUUFBUSxFQUFFLElBQUksQ0FBQ1IsTUFBTSxDQUFDSSxNQUFNLEVBQUU7UUFDOUU7UUFDQSxPQUFPLEdBQUcsSUFBSSxDQUFDSCxPQUFPLEdBQUdNLFVBQVU7SUFDckM7SUFFQSxNQUFNRyxpQkFBMkM7UUFDL0MsSUFBSTtZQUNGLE1BQU1DLE1BQU0sSUFBSSxDQUFDTCxTQUFTLENBQUM7WUFDM0IsTUFBTU0sVUFBVSxJQUFJLENBQUNWLGNBQWM7WUFFbkMsTUFBTVcsV0FBVyxNQUFNaEIsNkNBQUtBLENBQUNpQixHQUFHLENBQUNILEtBQUs7Z0JBQ3BDQztnQkFDQUcsUUFBUTtvQkFBRUMsT0FBTztnQkFBRTtZQUNyQjtZQUVBLE9BQU87Z0JBQ0xDLFNBQVM7Z0JBQ1RDLFNBQVM7Z0JBQ1RDLE1BQU07b0JBQ0pDLFVBQVVQLFNBQVNNLElBQUksRUFBRSxDQUFDLFlBQVksSUFBSTtvQkFDMUNFLGFBQWFSLFNBQVNNLElBQUksRUFBRUcsVUFBVUMsU0FBUztnQkFDakQ7WUFDRjtRQUNGLEVBQUUsT0FBT0MsT0FBWTtZQUNuQixPQUFPO2dCQUNMUCxTQUFTO2dCQUNUQyxTQUFTLENBQUMsMkJBQTJCLEVBQUVNLE1BQU1YLFFBQVEsRUFBRU0sTUFBTUQsV0FBV00sTUFBTU4sT0FBTyxFQUFFO1lBQ3pGO1FBQ0Y7SUFDRjtJQUVBLE1BQU1PLGNBQWNDLFdBQTJCLEVBQTRCO1FBQ3pFLElBQUk7WUFDRiw2Q0FBNkM7WUFDN0MsTUFBTUMsYUFBYSxJQUFJLENBQUNDLHVCQUF1QixDQUFDRjtZQUVoRCxNQUFNZixNQUFNLElBQUksQ0FBQ0wsU0FBUyxDQUFDO1lBQzNCLE1BQU1NLFVBQVUsSUFBSSxDQUFDVixjQUFjO1lBRW5DLE1BQU0yQixVQUFVO2dCQUNkRixZQUFZQTtZQUNkO1lBRUEsTUFBTWQsV0FBVyxNQUFNaEIsNkNBQUtBLENBQUNpQyxJQUFJLENBQUNuQixLQUFLa0IsU0FBUztnQkFBRWpCO1lBQVE7WUFFMUQsT0FBTztnQkFDTEssU0FBUztnQkFDVGMsV0FBV2xCLFNBQVNNLElBQUksQ0FBQ2EsR0FBRyxFQUFFQztnQkFDOUJmLFNBQVM7Z0JBQ1RDLE1BQU07b0JBQ0phLEtBQUtuQixTQUFTTSxJQUFJLENBQUNhLEdBQUc7b0JBQ3RCWixVQUFVUCxTQUFTTSxJQUFJLENBQUMsWUFBWTtvQkFDcENlLE9BQU9yQixTQUFTTSxJQUFJLENBQUMsU0FBUztnQkFDaEM7WUFDRjtRQUNGLEVBQUUsT0FBT0ssT0FBWTtZQUNuQixpQ0FBaUM7WUFDakMsSUFBSUEsTUFBTVgsUUFBUSxFQUFFc0IsV0FBVyxLQUFLO2dCQUNsQyxPQUFPLE1BQU0sSUFBSSxDQUFDQyxxQkFBcUIsQ0FBQ1YsYUFBYUYsTUFBTVgsUUFBUSxDQUFDTSxJQUFJO1lBQzFFO1lBRUEsT0FBTztnQkFDTEYsU0FBUztnQkFDVEMsU0FBUyxDQUFDLGtDQUFrQyxFQUFFTSxNQUFNWCxRQUFRLEVBQUVNLE1BQU1ELFdBQVdNLE1BQU1OLE9BQU8sRUFBRTtZQUNoRztRQUNGO0lBQ0Y7SUFFQSxNQUFNa0Isc0JBQXNCVixXQUEyQixFQUFFVyxTQUFjLEVBQTRCO1FBQ2pHLElBQUk7WUFDRixrREFBa0Q7WUFDbEQsTUFBTUMsb0JBQW9CRCxVQUFVRSxlQUFlLEVBQUVQO1lBRXJELElBQUksQ0FBQ00sbUJBQW1CO2dCQUN0QixPQUFPO29CQUNMckIsU0FBUztvQkFDVEMsU0FBUztnQkFDWDtZQUNGO1lBRUEsTUFBTVMsYUFBYSxJQUFJLENBQUNDLHVCQUF1QixDQUFDRjtZQUNoRCxNQUFNZixNQUFNLElBQUksQ0FBQ0wsU0FBUyxDQUFDLENBQUMseUJBQXlCLEVBQUVnQyxrQkFBa0IsUUFBUSxDQUFDO1lBQ2xGLE1BQU0xQixVQUFVLElBQUksQ0FBQ1YsY0FBYztZQUVuQyxNQUFNMkIsVUFBVTtnQkFDZEYsWUFBWUE7WUFDZDtZQUVBLE1BQU05Qiw2Q0FBS0EsQ0FBQ2lDLElBQUksQ0FBQ25CLEtBQUtrQixTQUFTO2dCQUFFakI7WUFBUTtZQUV6QyxPQUFPO2dCQUNMSyxTQUFTO2dCQUNUYyxXQUFXTyxrQkFBa0JMLFFBQVE7Z0JBQ3JDZixTQUFTO2dCQUNUQyxNQUFNO29CQUNKYSxLQUFLTTtvQkFDTEosT0FBTztnQkFDVDtZQUNGO1FBQ0YsRUFBRSxPQUFPVixPQUFZO1lBQ25CLE9BQU87Z0JBQ0xQLFNBQVM7Z0JBQ1RDLFNBQVMsQ0FBQywyQ0FBMkMsRUFBRU0sTUFBTVgsUUFBUSxFQUFFTSxNQUFNRCxXQUFXTSxNQUFNTixPQUFPLEVBQUU7WUFDekc7UUFDRjtJQUNGO0lBRVFVLHdCQUF3QkYsV0FBMkIsRUFBUztRQUNsRSxNQUFNQyxhQUFhLEVBQUU7UUFFckIsa0JBQWtCO1FBQ2xCLElBQUlELFlBQVljLEtBQUssRUFBRTtZQUNyQmIsV0FBV2MsSUFBSSxDQUFDO2dCQUFFQyxVQUFVO2dCQUFTQyxPQUFPakIsWUFBWWMsS0FBSztZQUFDO1FBQ2hFO1FBRUEsa0JBQWtCO1FBQ2xCLElBQUlkLFlBQVlrQixTQUFTLEVBQUU7WUFDekJqQixXQUFXYyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQWFDLE9BQU9qQixZQUFZa0IsU0FBUztZQUFDO1FBQ3hFO1FBRUEsSUFBSWxCLFlBQVltQixRQUFRLEVBQUU7WUFDeEJsQixXQUFXYyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVlDLE9BQU9qQixZQUFZbUIsUUFBUTtZQUFDO1FBQ3RFO1FBRUEsSUFBSW5CLFlBQVlvQixPQUFPLEVBQUU7WUFDdkJuQixXQUFXYyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVdDLE9BQU9qQixZQUFZb0IsT0FBTztZQUFDO1FBQ3BFO1FBRUEsSUFBSXBCLFlBQVlxQixLQUFLLEVBQUU7WUFDckJwQixXQUFXYyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVNDLE9BQU9qQixZQUFZcUIsS0FBSztZQUFDO1FBQ2hFO1FBRUEsSUFBSXJCLFlBQVlzQixPQUFPLEVBQUU7WUFDdkJyQixXQUFXYyxJQUFJLENBQUM7Z0JBQUVDLFVBQVU7Z0JBQVdDLE9BQU9qQixZQUFZc0IsT0FBTztZQUFDO1FBQ3BFO1FBRUEsSUFBSXRCLFlBQVlSLE9BQU8sRUFBRTtZQUN2QlMsV0FBV2MsSUFBSSxDQUFDO2dCQUFFQyxVQUFVO2dCQUFXQyxPQUFPakIsWUFBWVIsT0FBTztZQUFDO1FBQ3BFO1FBRUEsZ0JBQWdCO1FBQ2hCUyxXQUFXYyxJQUFJLENBQUM7WUFDZEMsVUFBVTtZQUNWQyxPQUFPakIsWUFBWXVCLFdBQVcsSUFBSTtRQUNwQztRQUVBdEIsV0FBV2MsSUFBSSxDQUFDO1lBQ2RDLFVBQVU7WUFDVkMsT0FBT2pCLFlBQVl3QixjQUFjLElBQUk7UUFDdkM7UUFFQSxnQkFBZ0I7UUFDaEJ2QixXQUFXYyxJQUFJLENBQUM7WUFDZEMsVUFBVTtZQUNWQyxPQUFPLElBQUlRLE9BQU9DLE9BQU8sR0FBR25CLFFBQVE7UUFDdEM7UUFFQSxPQUFPTjtJQUNUO0lBRUEsTUFBTTBCLFdBQVdiLEtBQWEsRUFBNEI7UUFDeEQsSUFBSTtZQUNGLE1BQU03QixNQUFNLElBQUksQ0FBQ0wsU0FBUyxDQUFDLENBQUMsMkJBQTJCLEVBQUVnRCxtQkFBbUJkLE9BQU8sUUFBUSxDQUFDO1lBQzVGLE1BQU01QixVQUFVLElBQUksQ0FBQ1YsY0FBYztZQUVuQyxNQUFNVyxXQUFXLE1BQU1oQiw2Q0FBS0EsQ0FBQ2lCLEdBQUcsQ0FBQ0gsS0FBSztnQkFBRUM7WUFBUTtZQUVoRCxPQUFPO2dCQUNMSyxTQUFTO2dCQUNUYyxXQUFXbEIsU0FBU00sSUFBSSxDQUFDYSxHQUFHLEVBQUVDO2dCQUM5QmYsU0FBUztnQkFDVEMsTUFBTU4sU0FBU00sSUFBSTtZQUNyQjtRQUNGLEVBQUUsT0FBT0ssT0FBWTtZQUNuQixJQUFJQSxNQUFNWCxRQUFRLEVBQUVzQixXQUFXLEtBQUs7Z0JBQ2xDLE9BQU87b0JBQ0xsQixTQUFTO29CQUNUQyxTQUFTO2dCQUNYO1lBQ0Y7WUFFQSxPQUFPO2dCQUNMRCxTQUFTO2dCQUNUQyxTQUFTLENBQUMsb0NBQW9DLEVBQUVNLE1BQU1YLFFBQVEsRUFBRU0sTUFBTUQsV0FBV00sTUFBTU4sT0FBTyxFQUFFO1lBQ2xHO1FBQ0Y7SUFDRjtJQUVBLE1BQU1xQyxXQUFXeEIsU0FBaUIsRUFBRXlCLFFBQWEsRUFBNEI7UUFDM0UsSUFBSTtZQUNGLE1BQU03QyxNQUFNLElBQUksQ0FBQ0wsU0FBUyxDQUFDO1lBQzNCLE1BQU1NLFVBQVUsSUFBSSxDQUFDVixjQUFjO1lBRW5DLE1BQU0yQixVQUFVO2dCQUNkRixZQUFZO29CQUNWO3dCQUFFOEIsTUFBTTt3QkFBWWQsT0FBT2EsU0FBU0UsUUFBUSxJQUFJO29CQUFpQjtvQkFDakU7d0JBQUVELE1BQU07d0JBQWFkLE9BQU9hLFNBQVNHLFNBQVMsSUFBSTtvQkFBdUI7b0JBQ3pFO3dCQUFFRixNQUFNO3dCQUFZZCxPQUFPYSxTQUFTSSxRQUFRLElBQUk7b0JBQVU7b0JBQzFEO3dCQUFFSCxNQUFNO3dCQUFVZCxPQUFPYSxTQUFTSyxNQUFNLElBQUk7b0JBQUk7b0JBQ2hEO3dCQUFFSixNQUFNO3dCQUFhZCxPQUFPYSxTQUFTTSxTQUFTLElBQUksSUFBSVgsS0FBS0EsS0FBS1ksR0FBRyxLQUFLLEtBQUssS0FBSyxLQUFLLEtBQUssTUFBTVgsT0FBTztvQkFBRztvQkFDNUc7d0JBQUVLLE1BQU07d0JBQVVkLE9BQU87b0JBQXFCO2lCQUMvQztnQkFDRHFCLGNBQWM7b0JBQ1pDLGdCQUFnQjt3QkFBQ0MsU0FBU25DO3FCQUFXO2dCQUN2QztZQUNGO1lBRUEsTUFBTWxCLFdBQVcsTUFBTWhCLDZDQUFLQSxDQUFDaUMsSUFBSSxDQUFDbkIsS0FBS2tCLFNBQVM7Z0JBQUVqQjtZQUFRO1lBRTFELE9BQU87Z0JBQ0xLLFNBQVM7Z0JBQ1RDLFNBQVM7Z0JBQ1RDLE1BQU07b0JBQ0pnRCxRQUFRdEQsU0FBU00sSUFBSSxDQUFDZ0QsTUFBTTtvQkFDNUIvQyxVQUFVUCxTQUFTTSxJQUFJLENBQUNDLFFBQVE7Z0JBQ2xDO1lBQ0Y7UUFDRixFQUFFLE9BQU9JLE9BQVk7WUFDbkIsT0FBTztnQkFDTFAsU0FBUztnQkFDVEMsU0FBUyxDQUFDLCtCQUErQixFQUFFTSxNQUFNWCxRQUFRLEVBQUVNLE1BQU1ELFdBQVdNLE1BQU1OLE9BQU8sRUFBRTtZQUM3RjtRQUNGO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqZXNzZVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxTbWFydEZvcm1EZWZlbmRlclxcc3JjXFxsaWJcXGludGVncmF0aW9uc1xcaHVic3BvdC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEh1YlNwb3RDb25maWcge1xuICBhY2Nlc3NUb2tlbj86IHN0cmluZztcbiAgYXBpS2V5Pzogc3RyaW5nO1xuICBwb3J0YWxJZD86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBIdWJTcG90Q29udGFjdCB7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIGZpcnN0bmFtZT86IHN0cmluZztcbiAgbGFzdG5hbWU/OiBzdHJpbmc7XG4gIGNvbXBhbnk/OiBzdHJpbmc7XG4gIHBob25lPzogc3RyaW5nO1xuICB3ZWJzaXRlPzogc3RyaW5nO1xuICBtZXNzYWdlPzogc3RyaW5nO1xuICBsZWFkX3NvdXJjZT86IHN0cmluZztcbiAgaHNfbGVhZF9zdGF0dXM/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgSHViU3BvdFJlc3BvbnNlIHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgY29udGFjdElkPzogc3RyaW5nO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGRhdGE/OiBhbnk7XG59XG5cbmV4cG9ydCBjbGFzcyBIdWJTcG90SW50ZWdyYXRpb24ge1xuICBwcml2YXRlIGNvbmZpZzogSHViU3BvdENvbmZpZztcbiAgcHJpdmF0ZSBiYXNlVXJsID0gJ2h0dHBzOi8vYXBpLmh1YmFwaS5jb20nO1xuXG4gIGNvbnN0cnVjdG9yKGNvbmZpZzogSHViU3BvdENvbmZpZykge1xuICAgIHRoaXMuY29uZmlnID0gY29uZmlnO1xuICB9XG5cbiAgcHJpdmF0ZSBnZXRBdXRoSGVhZGVycygpOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+IHtcbiAgICBpZiAodGhpcy5jb25maWcuYWNjZXNzVG9rZW4pIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3RoaXMuY29uZmlnLmFjY2Vzc1Rva2VufWAsXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgIH07XG4gICAgfSBlbHNlIGlmICh0aGlzLmNvbmZpZy5hcGlLZXkpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgIH07XG4gICAgfVxuICAgIHRocm93IG5ldyBFcnJvcignTm8gdmFsaWQgYXV0aGVudGljYXRpb24gbWV0aG9kIHByb3ZpZGVkJyk7XG4gIH1cblxuICBwcml2YXRlIGdldEFwaVVybChlbmRwb2ludDogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBpZiAodGhpcy5jb25maWcuYXBpS2V5KSB7XG4gICAgICBjb25zdCBzZXBhcmF0b3IgPSBlbmRwb2ludC5pbmNsdWRlcygnPycpID8gJyYnIDogJz8nO1xuICAgICAgcmV0dXJuIGAke3RoaXMuYmFzZVVybH0ke2VuZHBvaW50fSR7c2VwYXJhdG9yfWhhcGlrZXk9JHt0aGlzLmNvbmZpZy5hcGlLZXl9YDtcbiAgICB9XG4gICAgcmV0dXJuIGAke3RoaXMuYmFzZVVybH0ke2VuZHBvaW50fWA7XG4gIH1cblxuICBhc3luYyB0ZXN0Q29ubmVjdGlvbigpOiBQcm9taXNlPEh1YlNwb3RSZXNwb25zZT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cmwgPSB0aGlzLmdldEFwaVVybCgnL2NvbnRhY3RzL3YxL2xpc3RzL2FsbC9jb250YWN0cy9hbGwnKTtcbiAgICAgIGNvbnN0IGhlYWRlcnMgPSB0aGlzLmdldEF1dGhIZWFkZXJzKCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KHVybCwge1xuICAgICAgICBoZWFkZXJzLFxuICAgICAgICBwYXJhbXM6IHsgY291bnQ6IDEgfVxuICAgICAgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIG1lc3NhZ2U6ICdIdWJTcG90IGNvbm5lY3Rpb24gc3VjY2Vzc2Z1bCcsXG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBwb3J0YWxJZDogcmVzcG9uc2UuZGF0YT8uWydwb3J0YWwtaWQnXSB8fCAndW5rbm93bicsXG4gICAgICAgICAgaGFzQ29udGFjdHM6IHJlc3BvbnNlLmRhdGE/LmNvbnRhY3RzPy5sZW5ndGggPiAwXG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6IGBIdWJTcG90IGNvbm5lY3Rpb24gZmFpbGVkOiAke2Vycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2V9YFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBjcmVhdGVDb250YWN0KGNvbnRhY3REYXRhOiBIdWJTcG90Q29udGFjdCk6IFByb21pc2U8SHViU3BvdFJlc3BvbnNlPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFByZXBhcmUgY29udGFjdCBwcm9wZXJ0aWVzIGZvciBIdWJTcG90IEFQSVxuICAgICAgY29uc3QgcHJvcGVydGllcyA9IHRoaXMuZm9ybWF0Q29udGFjdFByb3BlcnRpZXMoY29udGFjdERhdGEpO1xuICAgICAgXG4gICAgICBjb25zdCB1cmwgPSB0aGlzLmdldEFwaVVybCgnL2NvbnRhY3RzL3YxL2NvbnRhY3QnKTtcbiAgICAgIGNvbnN0IGhlYWRlcnMgPSB0aGlzLmdldEF1dGhIZWFkZXJzKCk7XG5cbiAgICAgIGNvbnN0IHBheWxvYWQgPSB7XG4gICAgICAgIHByb3BlcnRpZXM6IHByb3BlcnRpZXNcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCh1cmwsIHBheWxvYWQsIHsgaGVhZGVycyB9KTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgY29udGFjdElkOiByZXNwb25zZS5kYXRhLnZpZD8udG9TdHJpbmcoKSxcbiAgICAgICAgbWVzc2FnZTogJ0NvbnRhY3QgY3JlYXRlZCBzdWNjZXNzZnVsbHkgaW4gSHViU3BvdCcsXG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICB2aWQ6IHJlc3BvbnNlLmRhdGEudmlkLFxuICAgICAgICAgIHBvcnRhbElkOiByZXNwb25zZS5kYXRhWydwb3J0YWwtaWQnXSxcbiAgICAgICAgICBpc05ldzogcmVzcG9uc2UuZGF0YVsnaXMtbmV3J11cbiAgICAgICAgfVxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAvLyBIYW5kbGUgZHVwbGljYXRlIGNvbnRhY3QgZXJyb3JcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDkpIHtcbiAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMudXBkYXRlRXhpc3RpbmdDb250YWN0KGNvbnRhY3REYXRhLCBlcnJvci5yZXNwb25zZS5kYXRhKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6IGBGYWlsZWQgdG8gY3JlYXRlIEh1YlNwb3QgY29udGFjdDogJHtlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlfWBcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgYXN5bmMgdXBkYXRlRXhpc3RpbmdDb250YWN0KGNvbnRhY3REYXRhOiBIdWJTcG90Q29udGFjdCwgZXJyb3JEYXRhOiBhbnkpOiBQcm9taXNlPEh1YlNwb3RSZXNwb25zZT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBFeHRyYWN0IGV4aXN0aW5nIGNvbnRhY3QgSUQgZnJvbSBlcnJvciByZXNwb25zZVxuICAgICAgY29uc3QgZXhpc3RpbmdDb250YWN0SWQgPSBlcnJvckRhdGEuaWRlbnRpdHlQcm9maWxlPy52aWQ7XG4gICAgICBcbiAgICAgIGlmICghZXhpc3RpbmdDb250YWN0SWQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBtZXNzYWdlOiAnQ29udGFjdCBhbHJlYWR5IGV4aXN0cyBidXQgY291bGQgbm90IHJldHJpZXZlIGNvbnRhY3QgSUQnXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHByb3BlcnRpZXMgPSB0aGlzLmZvcm1hdENvbnRhY3RQcm9wZXJ0aWVzKGNvbnRhY3REYXRhKTtcbiAgICAgIGNvbnN0IHVybCA9IHRoaXMuZ2V0QXBpVXJsKGAvY29udGFjdHMvdjEvY29udGFjdC92aWQvJHtleGlzdGluZ0NvbnRhY3RJZH0vcHJvZmlsZWApO1xuICAgICAgY29uc3QgaGVhZGVycyA9IHRoaXMuZ2V0QXV0aEhlYWRlcnMoKTtcblxuICAgICAgY29uc3QgcGF5bG9hZCA9IHtcbiAgICAgICAgcHJvcGVydGllczogcHJvcGVydGllc1xuICAgICAgfTtcblxuICAgICAgYXdhaXQgYXhpb3MucG9zdCh1cmwsIHBheWxvYWQsIHsgaGVhZGVycyB9KTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgY29udGFjdElkOiBleGlzdGluZ0NvbnRhY3RJZC50b1N0cmluZygpLFxuICAgICAgICBtZXNzYWdlOiAnRXhpc3RpbmcgY29udGFjdCB1cGRhdGVkIHN1Y2Nlc3NmdWxseSBpbiBIdWJTcG90JyxcbiAgICAgICAgZGF0YToge1xuICAgICAgICAgIHZpZDogZXhpc3RpbmdDb250YWN0SWQsXG4gICAgICAgICAgaXNOZXc6IGZhbHNlXG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6IGBGYWlsZWQgdG8gdXBkYXRlIGV4aXN0aW5nIEh1YlNwb3QgY29udGFjdDogJHtlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlfWBcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBmb3JtYXRDb250YWN0UHJvcGVydGllcyhjb250YWN0RGF0YTogSHViU3BvdENvbnRhY3QpOiBhbnlbXSB7XG4gICAgY29uc3QgcHJvcGVydGllcyA9IFtdO1xuXG4gICAgLy8gUmVxdWlyZWQgZmllbGRzXG4gICAgaWYgKGNvbnRhY3REYXRhLmVtYWlsKSB7XG4gICAgICBwcm9wZXJ0aWVzLnB1c2goeyBwcm9wZXJ0eTogJ2VtYWlsJywgdmFsdWU6IGNvbnRhY3REYXRhLmVtYWlsIH0pO1xuICAgIH1cblxuICAgIC8vIE9wdGlvbmFsIGZpZWxkc1xuICAgIGlmIChjb250YWN0RGF0YS5maXJzdG5hbWUpIHtcbiAgICAgIHByb3BlcnRpZXMucHVzaCh7IHByb3BlcnR5OiAnZmlyc3RuYW1lJywgdmFsdWU6IGNvbnRhY3REYXRhLmZpcnN0bmFtZSB9KTtcbiAgICB9XG5cbiAgICBpZiAoY29udGFjdERhdGEubGFzdG5hbWUpIHtcbiAgICAgIHByb3BlcnRpZXMucHVzaCh7IHByb3BlcnR5OiAnbGFzdG5hbWUnLCB2YWx1ZTogY29udGFjdERhdGEubGFzdG5hbWUgfSk7XG4gICAgfVxuXG4gICAgaWYgKGNvbnRhY3REYXRhLmNvbXBhbnkpIHtcbiAgICAgIHByb3BlcnRpZXMucHVzaCh7IHByb3BlcnR5OiAnY29tcGFueScsIHZhbHVlOiBjb250YWN0RGF0YS5jb21wYW55IH0pO1xuICAgIH1cblxuICAgIGlmIChjb250YWN0RGF0YS5waG9uZSkge1xuICAgICAgcHJvcGVydGllcy5wdXNoKHsgcHJvcGVydHk6ICdwaG9uZScsIHZhbHVlOiBjb250YWN0RGF0YS5waG9uZSB9KTtcbiAgICB9XG5cbiAgICBpZiAoY29udGFjdERhdGEud2Vic2l0ZSkge1xuICAgICAgcHJvcGVydGllcy5wdXNoKHsgcHJvcGVydHk6ICd3ZWJzaXRlJywgdmFsdWU6IGNvbnRhY3REYXRhLndlYnNpdGUgfSk7XG4gICAgfVxuXG4gICAgaWYgKGNvbnRhY3REYXRhLm1lc3NhZ2UpIHtcbiAgICAgIHByb3BlcnRpZXMucHVzaCh7IHByb3BlcnR5OiAnbWVzc2FnZScsIHZhbHVlOiBjb250YWN0RGF0YS5tZXNzYWdlIH0pO1xuICAgIH1cblxuICAgIC8vIExlYWQgdHJhY2tpbmdcbiAgICBwcm9wZXJ0aWVzLnB1c2goeyBcbiAgICAgIHByb3BlcnR5OiAnbGVhZF9zb3VyY2UnLCBcbiAgICAgIHZhbHVlOiBjb250YWN0RGF0YS5sZWFkX3NvdXJjZSB8fCAnU21hcnRGb3JtIERlZmVuZGVyJyBcbiAgICB9KTtcblxuICAgIHByb3BlcnRpZXMucHVzaCh7IFxuICAgICAgcHJvcGVydHk6ICdoc19sZWFkX3N0YXR1cycsIFxuICAgICAgdmFsdWU6IGNvbnRhY3REYXRhLmhzX2xlYWRfc3RhdHVzIHx8ICdORVcnIFxuICAgIH0pO1xuXG4gICAgLy8gQWRkIHRpbWVzdGFtcFxuICAgIHByb3BlcnRpZXMucHVzaCh7IFxuICAgICAgcHJvcGVydHk6ICdjcmVhdGVkYXRlJywgXG4gICAgICB2YWx1ZTogbmV3IERhdGUoKS5nZXRUaW1lKCkudG9TdHJpbmcoKSBcbiAgICB9KTtcblxuICAgIHJldHVybiBwcm9wZXJ0aWVzO1xuICB9XG5cbiAgYXN5bmMgZ2V0Q29udGFjdChlbWFpbDogc3RyaW5nKTogUHJvbWlzZTxIdWJTcG90UmVzcG9uc2U+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gdGhpcy5nZXRBcGlVcmwoYC9jb250YWN0cy92MS9jb250YWN0L2VtYWlsLyR7ZW5jb2RlVVJJQ29tcG9uZW50KGVtYWlsKX0vcHJvZmlsZWApO1xuICAgICAgY29uc3QgaGVhZGVycyA9IHRoaXMuZ2V0QXV0aEhlYWRlcnMoKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBheGlvcy5nZXQodXJsLCB7IGhlYWRlcnMgfSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIGNvbnRhY3RJZDogcmVzcG9uc2UuZGF0YS52aWQ/LnRvU3RyaW5nKCksXG4gICAgICAgIG1lc3NhZ2U6ICdDb250YWN0IHJldHJpZXZlZCBzdWNjZXNzZnVsbHknLFxuICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgICBtZXNzYWdlOiAnQ29udGFjdCBub3QgZm91bmQgaW4gSHViU3BvdCdcbiAgICAgICAgfTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6IGBGYWlsZWQgdG8gcmV0cmlldmUgSHViU3BvdCBjb250YWN0OiAke2Vycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2V9YFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBjcmVhdGVEZWFsKGNvbnRhY3RJZDogc3RyaW5nLCBkZWFsRGF0YTogYW55KTogUHJvbWlzZTxIdWJTcG90UmVzcG9uc2U+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gdGhpcy5nZXRBcGlVcmwoJy9kZWFscy92MS9kZWFsJyk7XG4gICAgICBjb25zdCBoZWFkZXJzID0gdGhpcy5nZXRBdXRoSGVhZGVycygpO1xuXG4gICAgICBjb25zdCBwYXlsb2FkID0ge1xuICAgICAgICBwcm9wZXJ0aWVzOiBbXG4gICAgICAgICAgeyBuYW1lOiAnZGVhbG5hbWUnLCB2YWx1ZTogZGVhbERhdGEuZGVhbE5hbWUgfHwgJ1NtYXJ0Rm9ybSBMZWFkJyB9LFxuICAgICAgICAgIHsgbmFtZTogJ2RlYWxzdGFnZScsIHZhbHVlOiBkZWFsRGF0YS5kZWFsU3RhZ2UgfHwgJ2FwcG9pbnRtZW50c2NoZWR1bGVkJyB9LFxuICAgICAgICAgIHsgbmFtZTogJ3BpcGVsaW5lJywgdmFsdWU6IGRlYWxEYXRhLnBpcGVsaW5lIHx8ICdkZWZhdWx0JyB9LFxuICAgICAgICAgIHsgbmFtZTogJ2Ftb3VudCcsIHZhbHVlOiBkZWFsRGF0YS5hbW91bnQgfHwgJzAnIH0sXG4gICAgICAgICAgeyBuYW1lOiAnY2xvc2VkYXRlJywgdmFsdWU6IGRlYWxEYXRhLmNsb3NlRGF0ZSB8fCBuZXcgRGF0ZShEYXRlLm5vdygpICsgMzAgKiAyNCAqIDYwICogNjAgKiAxMDAwKS5nZXRUaW1lKCkgfSxcbiAgICAgICAgICB7IG5hbWU6ICdzb3VyY2UnLCB2YWx1ZTogJ1NtYXJ0Rm9ybSBEZWZlbmRlcicgfVxuICAgICAgICBdLFxuICAgICAgICBhc3NvY2lhdGlvbnM6IHtcbiAgICAgICAgICBhc3NvY2lhdGVkVmlkczogW3BhcnNlSW50KGNvbnRhY3RJZCldXG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MucG9zdCh1cmwsIHBheWxvYWQsIHsgaGVhZGVycyB9KTtcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgbWVzc2FnZTogJ0RlYWwgY3JlYXRlZCBzdWNjZXNzZnVsbHkgaW4gSHViU3BvdCcsXG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBkZWFsSWQ6IHJlc3BvbnNlLmRhdGEuZGVhbElkLFxuICAgICAgICAgIHBvcnRhbElkOiByZXNwb25zZS5kYXRhLnBvcnRhbElkXG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6IGBGYWlsZWQgdG8gY3JlYXRlIEh1YlNwb3QgZGVhbDogJHtlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCBlcnJvci5tZXNzYWdlfWBcbiAgICAgIH07XG4gICAgfVxuICB9XG59XG4iXSwibmFtZXMiOlsiYXhpb3MiLCJIdWJTcG90SW50ZWdyYXRpb24iLCJjb25zdHJ1Y3RvciIsImNvbmZpZyIsImJhc2VVcmwiLCJnZXRBdXRoSGVhZGVycyIsImFjY2Vzc1Rva2VuIiwiYXBpS2V5IiwiRXJyb3IiLCJnZXRBcGlVcmwiLCJlbmRwb2ludCIsInNlcGFyYXRvciIsImluY2x1ZGVzIiwidGVzdENvbm5lY3Rpb24iLCJ1cmwiLCJoZWFkZXJzIiwicmVzcG9uc2UiLCJnZXQiLCJwYXJhbXMiLCJjb3VudCIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiZGF0YSIsInBvcnRhbElkIiwiaGFzQ29udGFjdHMiLCJjb250YWN0cyIsImxlbmd0aCIsImVycm9yIiwiY3JlYXRlQ29udGFjdCIsImNvbnRhY3REYXRhIiwicHJvcGVydGllcyIsImZvcm1hdENvbnRhY3RQcm9wZXJ0aWVzIiwicGF5bG9hZCIsInBvc3QiLCJjb250YWN0SWQiLCJ2aWQiLCJ0b1N0cmluZyIsImlzTmV3Iiwic3RhdHVzIiwidXBkYXRlRXhpc3RpbmdDb250YWN0IiwiZXJyb3JEYXRhIiwiZXhpc3RpbmdDb250YWN0SWQiLCJpZGVudGl0eVByb2ZpbGUiLCJlbWFpbCIsInB1c2giLCJwcm9wZXJ0eSIsInZhbHVlIiwiZmlyc3RuYW1lIiwibGFzdG5hbWUiLCJjb21wYW55IiwicGhvbmUiLCJ3ZWJzaXRlIiwibGVhZF9zb3VyY2UiLCJoc19sZWFkX3N0YXR1cyIsIkRhdGUiLCJnZXRUaW1lIiwiZ2V0Q29udGFjdCIsImVuY29kZVVSSUNvbXBvbmVudCIsImNyZWF0ZURlYWwiLCJkZWFsRGF0YSIsIm5hbWUiLCJkZWFsTmFtZSIsImRlYWxTdGFnZSIsInBpcGVsaW5lIiwiYW1vdW50IiwiY2xvc2VEYXRlIiwibm93IiwiYXNzb2NpYXRpb25zIiwiYXNzb2NpYXRlZFZpZHMiLCJwYXJzZUludCIsImRlYWxJZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/hubspot.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/integrations/index.ts":
/*!***************************************!*\
  !*** ./src/lib/integrations/index.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntegrationManager: () => (/* binding */ IntegrationManager)\n/* harmony export */ });\n/* harmony import */ var _hubspot__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hubspot */ \"(rsc)/./src/lib/integrations/hubspot.ts\");\n\nclass IntegrationManager {\n    static async sendToIntegration(integration, leadData) {\n        try {\n            switch(integration.type){\n                case 'HUBSPOT':\n                    return await this.sendToHubSpot(integration.config, leadData);\n                case 'SALESFORCE':\n                    return await this.sendToSalesforce(integration.config, leadData);\n                case 'MAILCHIMP':\n                    return await this.sendToMailchimp(integration.config, leadData);\n                case 'ZAPIER':\n                    return await this.sendToZapier(integration.config, leadData);\n                case 'WEBHOOK':\n                    return await this.sendToWebhook(integration.config, leadData);\n                default:\n                    return {\n                        success: false,\n                        message: `Unsupported integration type: ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Integration error: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n    static async sendToHubSpot(config, leadData) {\n        const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(config);\n        // Parse name into first and last name if needed\n        const { firstName, lastName } = this.parseName(leadData.name, leadData.firstName, leadData.lastName);\n        const contactData = {\n            email: leadData.email,\n            firstname: firstName,\n            lastname: lastName,\n            company: leadData.company,\n            phone: leadData.phone,\n            website: leadData.website,\n            message: leadData.message,\n            lead_source: leadData.source || 'SmartForm Defender',\n            hs_lead_status: 'NEW'\n        };\n        const result = await hubspot.createContact(contactData);\n        return {\n            success: result.success,\n            message: result.message,\n            contactId: result.contactId,\n            data: result.data,\n            provider: 'HUBSPOT'\n        };\n    }\n    static async sendToSalesforce(config, leadData) {\n        // Placeholder for Salesforce integration\n        return {\n            success: false,\n            message: 'Salesforce integration not implemented yet',\n            provider: 'SALESFORCE'\n        };\n    }\n    static async sendToMailchimp(config, leadData) {\n        // Placeholder for Mailchimp integration\n        return {\n            success: false,\n            message: 'Mailchimp integration not implemented yet',\n            provider: 'MAILCHIMP'\n        };\n    }\n    static async sendToZapier(config, leadData) {\n        try {\n            const response = await fetch(config.webhookUrl, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: 'SmartForm Defender'\n                })\n            });\n            if (response.ok) {\n                return {\n                    success: true,\n                    message: 'Lead sent to Zapier successfully',\n                    provider: 'ZAPIER'\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Zapier webhook failed: ${response.statusText}`,\n                    provider: 'ZAPIER'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Zapier webhook error: ${error.message}`,\n                provider: 'ZAPIER'\n            };\n        }\n    }\n    static async sendToWebhook(config, leadData) {\n        try {\n            const headers = {\n                'Content-Type': 'application/json',\n                ...config.headers\n            };\n            const response = await fetch(config.url, {\n                method: config.method || 'POST',\n                headers,\n                body: JSON.stringify({\n                    ...leadData,\n                    timestamp: new Date().toISOString(),\n                    source: 'SmartForm Defender'\n                })\n            });\n            if (response.ok) {\n                const responseData = await response.text();\n                return {\n                    success: true,\n                    message: 'Lead sent to webhook successfully',\n                    data: responseData,\n                    provider: 'WEBHOOK'\n                };\n            } else {\n                return {\n                    success: false,\n                    message: `Webhook failed: ${response.statusText}`,\n                    provider: 'WEBHOOK'\n                };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Webhook error: ${error.message}`,\n                provider: 'WEBHOOK'\n            };\n        }\n    }\n    static parseName(fullName, firstName, lastName) {\n        if (firstName && lastName) {\n            return {\n                firstName,\n                lastName\n            };\n        }\n        if (fullName) {\n            const parts = fullName.trim().split(' ');\n            return {\n                firstName: parts[0] || '',\n                lastName: parts.slice(1).join(' ') || ''\n            };\n        }\n        return {\n            firstName: firstName || '',\n            lastName: lastName || ''\n        };\n    }\n    static async testIntegration(integration) {\n        try {\n            switch(integration.type){\n                case 'HUBSPOT':\n                    const hubspot = new _hubspot__WEBPACK_IMPORTED_MODULE_0__.HubSpotIntegration(integration.config);\n                    const result = await hubspot.testConnection();\n                    return {\n                        success: result.success,\n                        message: result.message,\n                        data: result.data,\n                        provider: 'HUBSPOT'\n                    };\n                default:\n                    return {\n                        success: false,\n                        message: `Test not implemented for ${integration.type}`,\n                        provider: integration.type\n                    };\n            }\n        } catch (error) {\n            return {\n                success: false,\n                message: `Test failed: ${error.message}`,\n                provider: integration.type\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/integrations/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/spam-detection.ts":
/*!***********************************!*\
  !*** ./src/lib/spam-detection.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSpamScore: () => (/* binding */ calculateSpamScore)\n/* harmony export */ });\n/**\n * Spam Detection Engine\n * Advanced algorithms for detecting spam and bot submissions\n */ async function calculateSpamScore(input) {\n    const reasons = [];\n    let totalScore = 0;\n    const riskFactors = {\n        behavioral: 0,\n        content: 0,\n        technical: 0,\n        temporal: 0\n    };\n    // 1. Behavioral Analysis (30% weight)\n    const behavioralScore = analyzeBehavior(input.behaviorData, input.behaviorScore);\n    riskFactors.behavioral = behavioralScore;\n    totalScore += behavioralScore * 0.3;\n    if (behavioralScore > 0.5) {\n        reasons.push('Suspicious user behavior detected');\n    }\n    // 2. Content Analysis (25% weight)\n    const contentScore = analyzeContent(input.formData);\n    riskFactors.content = contentScore;\n    totalScore += contentScore * 0.25;\n    if (contentScore > 0.5) {\n        reasons.push('Suspicious content patterns detected');\n    }\n    // 3. Technical Fingerprinting (25% weight)\n    const technicalScore = analyzeTechnicalFingerprint(input.fingerprint, input.metadata);\n    riskFactors.technical = technicalScore;\n    totalScore += technicalScore * 0.25;\n    if (technicalScore > 0.5) {\n        reasons.push('Suspicious technical fingerprint');\n    }\n    // 4. Temporal Analysis (20% weight)\n    const temporalScore = analyzeTemporalPatterns(input.behaviorData, input.metadata);\n    riskFactors.temporal = temporalScore;\n    totalScore += temporalScore * 0.2;\n    if (temporalScore > 0.5) {\n        reasons.push('Suspicious timing patterns');\n    }\n    // 5. IP and Geo Analysis\n    const geoData = await analyzeGeoLocation(input.clientIP);\n    if (geoData && input.project.enableGeoValidation) {\n        const geoScore = analyzeGeoRisk(geoData, input.project);\n        totalScore += geoScore * 0.1;\n        if (geoScore > 0.5) {\n            reasons.push(`Submission from restricted location: ${geoData.country}`);\n        }\n    }\n    // 6. Historical Analysis\n    const historicalScore = await analyzeHistoricalData(input.clientIP, input.fingerprint);\n    totalScore += historicalScore * 0.1;\n    if (historicalScore > 0.5) {\n        reasons.push('Previous suspicious activity detected');\n    }\n    // Cap the score at 1.0\n    const finalScore = Math.min(totalScore, 1.0);\n    return {\n        spamScore: finalScore,\n        reasons,\n        geoData,\n        riskFactors\n    };\n}\nfunction analyzeBehavior(behaviorData, behaviorScore) {\n    let score = behaviorScore; // Base score from client-side analysis\n    const { mouseMovements, keystrokes, focusEvents, startTime, interactions } = behaviorData;\n    const timeSpent = Date.now() - startTime;\n    // Mouse movement analysis\n    if (mouseMovements.length === 0) {\n        score += 0.4; // No mouse movement is highly suspicious\n    } else if (mouseMovements.length < 5) {\n        score += 0.2; // Very few movements\n    } else {\n        // Analyze movement patterns\n        const movements = mouseMovements.slice(-20); // Last 20 movements\n        let straightLines = 0;\n        let perfectCurves = 0;\n        for(let i = 2; i < movements.length; i++){\n            const dx1 = movements[i - 1].x - movements[i - 2].x;\n            const dy1 = movements[i - 1].y - movements[i - 2].y;\n            const dx2 = movements[i].x - movements[i - 1].x;\n            const dy2 = movements[i].y - movements[i - 1].y;\n            // Check for perfectly straight lines (bot-like)\n            if (dx1 !== 0 && dy1 !== 0 && dx2 !== 0 && dy2 !== 0) {\n                const slope1 = dy1 / dx1;\n                const slope2 = dy2 / dx2;\n                if (Math.abs(slope1 - slope2) < 0.01) {\n                    straightLines++;\n                }\n            }\n        }\n        if (straightLines > movements.length * 0.7) {\n            score += 0.3; // Too many straight lines\n        }\n    }\n    // Keystroke analysis\n    if (keystrokes.length > 0) {\n        const intervals = keystrokes.map((k)=>k.interval).filter((i)=>i > 0);\n        if (intervals.length > 5) {\n            const avgInterval = intervals.reduce((a, b)=>a + b, 0) / intervals.length;\n            const variance = intervals.reduce((acc, val)=>acc + Math.pow(val - avgInterval, 2), 0) / intervals.length;\n            // Very consistent typing intervals suggest automation\n            if (variance < 100 && avgInterval < 50) {\n                score += 0.3;\n            }\n        }\n    }\n    // Time-based analysis\n    if (timeSpent < 1000) {\n        score += 0.4; // Submitted too quickly\n    } else if (timeSpent < 3000) {\n        score += 0.2; // Still quite fast\n    }\n    // Interaction analysis\n    if (interactions < 2) {\n        score += 0.3; // Very few interactions with form\n    }\n    return Math.min(score, 1.0);\n}\nfunction analyzeContent(formData) {\n    let score = 0;\n    // Common spam patterns\n    const spamPatterns = [\n        /viagra|cialis|pharmacy/i,\n        /make money|earn \\$|work from home/i,\n        /click here|visit now|act now/i,\n        /free trial|limited time|urgent/i,\n        /congratulations|winner|selected/i\n    ];\n    // Check all text fields\n    Object.values(formData).forEach((value)=>{\n        if (typeof value === 'string') {\n            const text = value.toLowerCase();\n            // Check for spam patterns\n            spamPatterns.forEach((pattern)=>{\n                if (pattern.test(text)) {\n                    score += 0.2;\n                }\n            });\n            // Check for excessive URLs\n            const urlCount = (text.match(/https?:\\/\\/[^\\s]+/g) || []).length;\n            if (urlCount > 2) {\n                score += 0.3;\n            }\n            // Check for excessive special characters\n            const specialCharRatio = (text.match(/[^a-zA-Z0-9\\s]/g) || []).length / text.length;\n            if (specialCharRatio > 0.3) {\n                score += 0.2;\n            }\n            // Check for repeated characters\n            if (/(.)\\1{4,}/.test(text)) {\n                score += 0.2;\n            }\n            // Check for all caps\n            if (text.length > 10 && text === text.toUpperCase()) {\n                score += 0.1;\n            }\n        }\n    });\n    // Check for honeypot fields\n    if (formData.website || formData.url || formData.homepage) {\n        score += 0.8; // Honeypot field filled\n    }\n    return Math.min(score, 1.0);\n}\nfunction analyzeTechnicalFingerprint(fingerprint, metadata) {\n    let score = 0;\n    // User agent analysis\n    const userAgent = metadata.userAgent || '';\n    // Check for bot user agents\n    const botPatterns = [\n        /bot|crawler|spider|scraper/i,\n        /curl|wget|python|java/i,\n        /headless|phantom|selenium/i\n    ];\n    if (botPatterns.some((pattern)=>pattern.test(userAgent))) {\n        score += 0.6;\n    }\n    // Check for missing or suspicious browser features\n    if (!fingerprint.cookieEnabled) {\n        score += 0.2;\n    }\n    if (fingerprint.doNotTrack === '1') {\n        score += 0.1; // Slight increase for privacy-conscious users\n    }\n    // Screen resolution analysis\n    if (fingerprint.screen) {\n        const [width, height] = fingerprint.screen.split('x').map(Number);\n        // Common bot resolutions\n        if (width === 1024 && height === 768 || width === 1920 && height === 1080 && !userAgent.includes('Windows')) {\n            score += 0.2;\n        }\n    }\n    // Canvas fingerprint analysis\n    if (fingerprint.canvas) {\n        // Very short canvas fingerprints suggest blocking or automation\n        if (fingerprint.canvas.length < 100) {\n            score += 0.3;\n        }\n    }\n    // Language/timezone mismatch\n    if (fingerprint.language && fingerprint.timezone) {\n        // This would require a lookup table of language-timezone correlations\n        // For now, just check for obvious mismatches\n        if (fingerprint.language.startsWith('en') && !fingerprint.timezone.includes('America') && !fingerprint.timezone.includes('Europe')) {\n            score += 0.1;\n        }\n    }\n    return Math.min(score, 1.0);\n}\nfunction analyzeTemporalPatterns(behaviorData, metadata) {\n    let score = 0;\n    const submissionTime = new Date(metadata.timestamp);\n    const hour = submissionTime.getHours();\n    // Submissions during unusual hours (2-6 AM) are slightly more suspicious\n    if (hour >= 2 && hour <= 6) {\n        score += 0.1;\n    }\n    // Check for rapid-fire submissions (would need database lookup)\n    // This is a placeholder for rate limiting logic\n    return Math.min(score, 1.0);\n}\nasync function analyzeGeoLocation(ip) {\n    try {\n        // In production, you would use a real IP geolocation service\n        // For now, return mock data\n        return {\n            country: 'US',\n            city: 'Unknown'\n        };\n    } catch (error) {\n        console.error('Geo location error:', error);\n        return null;\n    }\n}\nfunction analyzeGeoRisk(geoData, project) {\n    let score = 0;\n    // Check blocked countries\n    if (project.blockedCountries) {\n        const blockedCountries = project.blockedCountries.split(',').map((c)=>c.trim());\n        if (blockedCountries.includes(geoData.country)) {\n            score += 0.8;\n        }\n    }\n    // Check allowed countries\n    if (project.allowedCountries) {\n        const allowedCountries = project.allowedCountries.split(',').map((c)=>c.trim());\n        if (allowedCountries.length > 0 && !allowedCountries.includes(geoData.country)) {\n            score += 0.6;\n        }\n    }\n    return Math.min(score, 1.0);\n}\nasync function analyzeHistoricalData(ip, fingerprint) {\n    try {\n        // In production, check database for previous submissions from this IP/fingerprint\n        // For now, return 0\n        return 0;\n    } catch (error) {\n        console.error('Historical analysis error:', error);\n        return 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/spam-detection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/validation.ts":
/*!*******************************!*\
  !*** ./src/lib/validation.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSpamScore: () => (/* binding */ calculateSpamScore),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateApiKey: () => (/* binding */ generateApiKey),\n/* harmony export */   phoneSchema: () => (/* binding */ phoneSchema),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateFormData: () => (/* binding */ validateFormData),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n// Validation schemas\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email();\nconst phoneSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^[\\+]?[1-9][\\d]{0,15}$/);\n// Utility functions\nfunction generateApiKey() {\n    return 'sfd_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n}\nfunction validateEmail(email) {\n    return emailSchema.safeParse(email).success;\n}\nfunction validatePhone(phone) {\n    const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, '');\n    return phoneSchema.safeParse(cleanPhone).success;\n}\nasync function validateFormData(formData, project) {\n    const errors = {};\n    const warnings = {};\n    // Email validation\n    if (formData.email) {\n        if (!validateEmail(formData.email)) {\n            errors.email = 'Invalid email format';\n        } else if (project.enableEmailValidation) {\n            // Additional email validation would go here\n            const emailValidation = await validateEmailAdvanced(formData.email);\n            if (!emailValidation.valid) {\n                errors.email = emailValidation.message;\n            }\n        }\n    }\n    // Phone validation\n    if (formData.phone) {\n        if (!validatePhone(formData.phone)) {\n            errors.phone = 'Invalid phone format';\n        } else if (project.enablePhoneValidation) {\n            const phoneValidation = await validatePhoneAdvanced(formData.phone);\n            if (!phoneValidation.valid) {\n                errors.phone = phoneValidation.message;\n            }\n        }\n    }\n    // Required field validation\n    const requiredFields = [\n        'email',\n        'name'\n    ];\n    requiredFields.forEach((field)=>{\n        if (!formData[field] || formData[field].trim() === '') {\n            errors[field] = `${field} is required`;\n        }\n    });\n    // Content validation\n    if (formData.message && formData.message.length < 10) {\n        warnings.message = 'Message seems too short';\n    }\n    if (formData.name && formData.name.length < 2) {\n        warnings.name = 'Name seems too short';\n    }\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors,\n        warnings\n    };\n}\nasync function validateEmailAdvanced(email) {\n    try {\n        // In production, you would use a real email validation service\n        // For now, just do basic checks\n        // Check for disposable email domains\n        const disposableDomains = [\n            '10minutemail.com',\n            'tempmail.org',\n            'guerrillamail.com',\n            'mailinator.com'\n        ];\n        const domain = email.split('@')[1];\n        if (disposableDomains.includes(domain)) {\n            return {\n                valid: false,\n                message: 'Disposable email addresses are not allowed'\n            };\n        }\n        // Check for common typos in popular domains\n        const commonDomains = {\n            'gmial.com': 'gmail.com',\n            'gmai.com': 'gmail.com',\n            'yahooo.com': 'yahoo.com',\n            'hotmial.com': 'hotmail.com'\n        };\n        if (commonDomains[domain]) {\n            return {\n                valid: false,\n                message: `Did you mean ${email.replace(domain, commonDomains[domain])}?`\n            };\n        }\n        return {\n            valid: true,\n            message: 'Email is valid'\n        };\n    } catch (error) {\n        return {\n            valid: true,\n            message: 'Could not validate email'\n        };\n    }\n}\nasync function validatePhoneAdvanced(phone) {\n    try {\n        // In production, you would use a phone validation service\n        const cleanPhone = phone.replace(/[\\s\\-\\(\\)]/g, '');\n        // Basic length check\n        if (cleanPhone.length < 7 || cleanPhone.length > 15) {\n            return {\n                valid: false,\n                message: 'Phone number length is invalid'\n            };\n        }\n        return {\n            valid: true,\n            message: 'Phone number is valid'\n        };\n    } catch (error) {\n        return {\n            valid: true,\n            message: 'Could not validate phone number'\n        };\n    }\n}\nfunction calculateSpamScore(data) {\n    let score = 0;\n    // Basic spam detection logic\n    if (data.email && data.email.includes('test')) score += 0.2;\n    if (data.message && data.message.length < 10) score += 0.3;\n    if (data.name && data.name.length < 2) score += 0.2;\n    return Math.min(score, 1);\n}\nfunction formatDate(date) {\n    return date.toLocaleDateString();\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat().format(num);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/validation.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvalidate%2Fsubmission%2Froute&page=%2Fapi%2Fvalidate%2Fsubmission%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvalidate%2Fsubmission%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();