import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    await requireAdmin();

    // Get system statistics
    const [
      totalUsers,
      totalProjects,
      totalSubmissions,
      activeIntegrations
    ] = await Promise.all([
      prisma.user.count(),
      prisma.project.count(),
      prisma.submission.count(),
      prisma.integration.count({
        where: { isActive: true }
      })
    ]);

    return NextResponse.json({
      totalUsers,
      totalProjects,
      totalSubmissions,
      activeIntegrations
    });

  } catch (error) {
    console.error('Admin stats error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Authentication required' || error.message === 'Admin access required') {
        return NextResponse.json(
          { error: error.message },
          { status: 401 }
        );
      }
    }

    return NextResponse.json(
      { error: 'Failed to fetch admin statistics' },
      { status: 500 }
    );
  }
}
