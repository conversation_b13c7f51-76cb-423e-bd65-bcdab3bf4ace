import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { validateApiKey } from '@/lib/auth';

// GET /api/integrations/[id] - Get integration details
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate API key
    const apiKey = request.headers.get('x-api-key');
    const project = await validateApiKey(apiKey);

    if (!project) {
      return NextResponse.json(
        { error: 'Invalid API key' },
        { status: 401 }
      );
    }

    const integration = await prisma.integration.findFirst({
      where: {
        id: params.id,
        projectId: project.id
      },
      include: {
        project: {
          select: {
            name: true,
            domain: true
          }
        }
      }
    });

    if (!integration) {
      return NextResponse.json(
        { error: 'Integration not found' },
        { status: 404 }
      );
    }

    const response = {
      ...integration,
      config: integration.config ? JSON.parse(integration.config) : {}
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Integration fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch integration' },
      { status: 500 }
    );
  }
}

// PUT /api/integrations/[id] - Update integration
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, config, isActive } = body;

    const updateData: any = {
      updatedAt: new Date()
    };

    if (name) updateData.name = name;
    if (config) updateData.config = JSON.stringify(config);
    if (isActive !== undefined) updateData.isActive = isActive;

    const integration = await prisma.integration.update({
      where: {
        id: params.id
      },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      data: {
        ...integration,
        config: integration.config ? JSON.parse(integration.config) : {}
      }
    });

  } catch (error) {
    console.error('Integration update error:', error);
    return NextResponse.json(
      { error: 'Failed to update integration' },
      { status: 500 }
    );
  }
}

// DELETE /api/integrations/[id] - Delete integration
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.integration.delete({
      where: {
        id: params.id
      }
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Integration deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete integration' },
      { status: 500 }
    );
  }
}

// POST /api/integrations/[id]/test - Test integration connection
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const integration = await prisma.integration.findUnique({
      where: {
        id: params.id
      }
    });

    if (!integration) {
      return NextResponse.json(
        { error: 'Integration not found' },
        { status: 404 }
      );
    }

    const config = integration.config ? JSON.parse(integration.config) : {};
    
    // Test the integration based on type
    const testResult = await testIntegrationConnection(integration.type, config);

    // Update last sync time if successful
    if (testResult.success) {
      await prisma.integration.update({
        where: { id: params.id },
        data: { lastSync: new Date() }
      });
    }

    return NextResponse.json(testResult);

  } catch (error) {
    console.error('Integration test error:', error);
    return NextResponse.json(
      { error: 'Failed to test integration' },
      { status: 500 }
    );
  }
}

async function testIntegrationConnection(type: string, config: any) {
  try {
    switch (type) {
      case 'HUBSPOT':
        return await testHubSpotConnection(config);
      case 'SALESFORCE':
        return await testSalesforceConnection(config);
      case 'MAILCHIMP':
        return await testMailchimpConnection(config);
      case 'ZAPIER':
        return await testZapierConnection(config);
      case 'WEBHOOK':
        return await testWebhookConnection(config);
      default:
        return {
          success: false,
          message: 'Unknown integration type'
        };
    }
  } catch (error) {
    return {
      success: false,
      message: `Connection test failed: ${error.message}`
    };
  }
}

async function testHubSpotConnection(config: any) {
  // In production, you would make an actual API call to HubSpot
  // For now, just validate the config
  if (!config.accessToken && !config.apiKey) {
    return {
      success: false,
      message: 'Missing HubSpot credentials'
    };
  }

  return {
    success: true,
    message: 'HubSpot connection successful'
  };
}

async function testSalesforceConnection(config: any) {
  return {
    success: true,
    message: 'Salesforce connection test not implemented yet'
  };
}

async function testMailchimpConnection(config: any) {
  return {
    success: true,
    message: 'Mailchimp connection test not implemented yet'
  };
}

async function testZapierConnection(config: any) {
  return {
    success: true,
    message: 'Zapier webhook test not implemented yet'
  };
}

async function testWebhookConnection(config: any) {
  return {
    success: true,
    message: 'Webhook test not implemented yet'
  };
}
