globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/users/create/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/providers/AuthProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/[id]/edit/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/users/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/create/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/users/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/users/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/projects/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/submissions/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/submissions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/signin/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/projects/new/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/projects/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/MainNavigation.tsx":{"*":{"id":"(ssr)/./src/components/MainNavigation.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\components\\providers\\AuthProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\users\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/users/page.tsx","name":"*","chunks":["app/admin/users/page","static/chunks/app/admin/users/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\users\\[id]\\edit\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/users/[id]/edit/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\users\\create\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/users/create/page.tsx","name":"*","chunks":["app/admin/users/create/page","static/chunks/app/admin/users/create/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\users\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/users/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\analytics\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\dashboard\\projects\\[id]\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\dashboard\\projects\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/projects/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\dashboard\\submissions\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/submissions/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\auth\\signin\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\dashboard\\projects\\new\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/projects/new/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\components\\MainNavigation.tsx":{"id":"(app-pages-browser)/./src/components/MainNavigation.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\users\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\SmartFormDefender\\src\\app\\admin\\users\\create\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/AuthProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/AuthProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/[id]/edit/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/users/[id]/edit/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/settings/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/create/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/users/create/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/users/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/projects/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/projects/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/submissions/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/submissions/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/signin/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/signin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/projects/new/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/projects/new/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/MainNavigation.tsx":{"*":{"id":"(rsc)/./src/components/MainNavigation.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}