{"name": "@types/scheduler", "version": "0.16.8", "description": "TypeScript definitions for scheduler", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}, {"name": "<PERSON>", "githubUsername": "eps1lon", "url": "https://github.com/eps1lon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/scheduler"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "9d7d043580552b4f12920f8562a456a2a9799eee67136f013daf1bf22f17fbb6", "typeScriptVersion": "4.5"}