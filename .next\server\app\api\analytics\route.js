/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/route";
exports.ids = ["app/api/analytics/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/route.ts */ \"(rsc)/./src/app/api/analytics/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/route\",\n        pathname: \"/api/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\api\\\\analytics\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jesse_Documents_augment_projects_SmartFormDefender_src_app_api_analytics_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/analytics/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// GET /api/analytics - Get analytics data\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const projectId = searchParams.get('projectId');\n        const period = searchParams.get('period') || '30'; // days\n        const type = searchParams.get('type') || 'overview';\n        if (!projectId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Project ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const days = parseInt(period);\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        startDate.setHours(0, 0, 0, 0);\n        switch(type){\n            case 'overview':\n                return await getOverviewAnalytics(projectId, startDate);\n            case 'daily':\n                return await getDailyAnalytics(projectId, startDate);\n            case 'spam':\n                return await getSpamAnalytics(projectId, startDate);\n            case 'geo':\n                return await getGeoAnalytics(projectId, startDate);\n            default:\n                return await getOverviewAnalytics(projectId, startDate);\n        }\n    } catch (error) {\n        console.error('Analytics fetch error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch analytics'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getOverviewAnalytics(projectId, startDate) {\n    const [totalSubmissions, spamBlocked, validLeads, recentSubmissions] = await Promise.all([\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.count({\n            where: {\n                projectId,\n                createdAt: {\n                    gte: startDate\n                }\n            }\n        }),\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.count({\n            where: {\n                projectId,\n                isSpam: true,\n                createdAt: {\n                    gte: startDate\n                }\n            }\n        }),\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.count({\n            where: {\n                projectId,\n                isValid: true,\n                isSpam: false,\n                createdAt: {\n                    gte: startDate\n                }\n            }\n        }),\n        _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.findMany({\n            where: {\n                projectId,\n                createdAt: {\n                    gte: startDate\n                }\n            },\n            orderBy: {\n                createdAt: 'desc'\n            },\n            take: 10,\n            select: {\n                id: true,\n                email: true,\n                name: true,\n                isSpam: true,\n                isValid: true,\n                spamScore: true,\n                createdAt: true\n            }\n        })\n    ]);\n    const conversionRate = totalSubmissions > 0 ? validLeads / totalSubmissions * 100 : 0;\n    const spamRate = totalSubmissions > 0 ? spamBlocked / totalSubmissions * 100 : 0;\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        overview: {\n            totalSubmissions,\n            spamBlocked,\n            validLeads,\n            conversionRate: Math.round(conversionRate * 100) / 100,\n            spamRate: Math.round(spamRate * 100) / 100\n        },\n        recentSubmissions\n    });\n}\nasync function getDailyAnalytics(projectId, startDate) {\n    const analytics = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.analytics.findMany({\n        where: {\n            projectId,\n            date: {\n                gte: startDate\n            }\n        },\n        orderBy: {\n            date: 'asc'\n        }\n    });\n    // Fill in missing days with zero values\n    const dailyData = [];\n    const currentDate = new Date(startDate);\n    const endDate = new Date();\n    while(currentDate <= endDate){\n        const dateStr = currentDate.toISOString().split('T')[0];\n        const existing = analytics.find((a)=>a.date.toISOString().split('T')[0] === dateStr);\n        dailyData.push({\n            date: dateStr,\n            totalSubmissions: existing?.totalSubmissions || 0,\n            spamBlocked: existing?.spamBlocked || 0,\n            validLeads: existing?.validLeads || 0,\n            conversionRate: existing?.conversionRate || 0\n        });\n        currentDate.setDate(currentDate.getDate() + 1);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        dailyData\n    });\n}\nasync function getSpamAnalytics(projectId, startDate) {\n    const spamReasons = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.groupBy({\n        by: [\n            'status'\n        ],\n        where: {\n            projectId,\n            createdAt: {\n                gte: startDate\n            }\n        },\n        _count: {\n            status: true\n        }\n    });\n    const spamScoreDistribution = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.findMany({\n        where: {\n            projectId,\n            createdAt: {\n                gte: startDate\n            }\n        },\n        select: {\n            spamScore: true,\n            isSpam: true\n        }\n    });\n    // Group spam scores into buckets\n    const scoreBuckets = {\n        '0.0-0.2': 0,\n        '0.2-0.4': 0,\n        '0.4-0.6': 0,\n        '0.6-0.8': 0,\n        '0.8-1.0': 0\n    };\n    spamScoreDistribution.forEach((submission)=>{\n        const score = submission.spamScore;\n        if (score < 0.2) scoreBuckets['0.0-0.2']++;\n        else if (score < 0.4) scoreBuckets['0.2-0.4']++;\n        else if (score < 0.6) scoreBuckets['0.4-0.6']++;\n        else if (score < 0.8) scoreBuckets['0.6-0.8']++;\n        else scoreBuckets['0.8-1.0']++;\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        spamReasons,\n        scoreBuckets\n    });\n}\nasync function getGeoAnalytics(projectId, startDate) {\n    const geoData = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.submission.groupBy({\n        by: [\n            'country'\n        ],\n        where: {\n            projectId,\n            createdAt: {\n                gte: startDate\n            },\n            country: {\n                not: null\n            }\n        },\n        _count: {\n            country: true\n        },\n        orderBy: {\n            _count: {\n                country: 'desc'\n            }\n        },\n        take: 10\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        geoData\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsR0FBRztBQUVuRSxJQUFJSSxJQUFxQyxFQUFFSCxnQkFBZ0JFLE1BQU0sR0FBR0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcamVzc2VcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcU21hcnRGb3JtRGVmZW5kZXJcXHNyY1xcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnO1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZDtcbn07XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.ts&appDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjesse%5CDocuments%5Caugment-projects%5CSmartFormDefender&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();