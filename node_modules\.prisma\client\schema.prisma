// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  company   String?
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Authentication
  password      String?
  emailVerified DateTime?
  image         String?

  // Subscription
  plan               String    @default("FREE")
  stripeCustomerId   String?
  subscriptionId     String?
  subscriptionStatus String?
  currentPeriodEnd   DateTime?

  // Relations
  accounts     Account[]
  sessions     Session[]
  projects     Project[]
  apiKeys      ApiKey[]
  integrations Integration[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  domain      String
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Configuration
  spamThreshold         Float   @default(0.7)
  enablePhoneValidation Boolean @default(true)
  enableEmailValidation Boolean @default(true)
  enableGeoValidation   Boolean @default(false)
  allowedCountries      String  @default("")
  blockedCountries      String  @default("")

  // Relations
  userId       String
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  submissions  Submission[]
  integrations Integration[]

  @@map("projects")
}

model ApiKey {
  id        String    @id @default(cuid())
  name      String
  key       String    @unique
  isActive  Boolean   @default(true)
  lastUsed  DateTime?
  createdAt DateTime  @default(now())
  expiresAt DateTime?

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model Submission {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())

  // Form data
  formData String
  email    String?
  phone    String?
  name     String?
  company  String?

  // Validation results
  spamScore         Float
  isSpam            Boolean
  isValid           Boolean
  validationResults String

  // Metadata
  ipAddress   String?
  userAgent   String?
  fingerprint String?
  country     String?
  city        String?

  // Processing
  status      String    @default("PENDING")
  processedAt DateTime?
  sentToCrm   Boolean   @default(false)
  crmResponse String?

  // Relations
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("submissions")
}

model Integration {
  id        String   @id @default(cuid())
  type      String
  name      String
  isActive  Boolean  @default(true)
  config    String // Store integration-specific configuration as JSON string
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId    String
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("integrations")
}

model Analytics {
  id        String   @id @default(cuid())
  date      DateTime
  projectId String

  // Metrics
  totalSubmissions Int   @default(0)
  spamBlocked      Int   @default(0)
  validLeads       Int   @default(0)
  conversionRate   Float @default(0)

  // Hourly breakdown
  hourlyData String @default("{}")

  @@unique([date, projectId])
  @@map("analytics")
}

// Note: SQLite doesn't support enums, so we use strings with validation in the application layer
// Plan values: "FREE", "STARTER", "PROFESSIONAL", "AGENCY"
// SubmissionStatus values: "PENDING", "PROCESSED", "SPAM", "VALID", "ERROR"
// IntegrationType values: "HUBSPOT", "SALESFORCE", "MAILCHIMP", "ZAPIER", "WEBHOOK"
