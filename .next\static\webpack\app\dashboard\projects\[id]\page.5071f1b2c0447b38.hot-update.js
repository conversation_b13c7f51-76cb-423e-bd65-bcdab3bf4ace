"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/projects/[id]/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProjectConfigPage() {\n    var _project__count, _project__count1;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const projectId = params.id;\n    const [project, setProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        domain: '',\n        description: '',\n        spamThreshold: 0.7,\n        enableEmailValidation: true,\n        enablePhoneValidation: true,\n        enableGeoValidation: false,\n        allowedCountries: '',\n        blockedCountries: '',\n        isActive: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectConfigPage.useEffect\": ()=>{\n            if (projectId) {\n                loadProject();\n            }\n        }\n    }[\"ProjectConfigPage.useEffect\"], [\n        projectId\n    ]);\n    const loadProject = async ()=>{\n        try {\n            const response = await fetch(\"/api/projects/\".concat(projectId));\n            if (response.ok) {\n                const data = await response.json();\n                const projectData = data.project;\n                console.log('Loaded project data:', projectData);\n                setProject(projectData);\n                setConfig({\n                    name: projectData.name,\n                    domain: projectData.domain,\n                    description: projectData.description || '',\n                    spamThreshold: projectData.spamThreshold,\n                    enableEmailValidation: projectData.enableEmailValidation,\n                    enablePhoneValidation: projectData.enablePhoneValidation,\n                    enableGeoValidation: projectData.enableGeoValidation,\n                    allowedCountries: projectData.allowedCountries || '',\n                    blockedCountries: projectData.blockedCountries || '',\n                    isActive: projectData.isActive\n                });\n            } else {\n                console.error('Failed to load project:', response.status);\n            }\n        } catch (error) {\n            console.error('Failed to load project:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const saveProject = async ()=>{\n        setSaving(true);\n        try {\n            const response = await fetch(\"/api/projects/\".concat(projectId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(config)\n            });\n            if (response.ok) {\n                alert('Project updated successfully!');\n                loadProject(); // Refresh data\n            } else {\n                alert('Failed to update project');\n            }\n        } catch (error) {\n            console.error('Failed to save project:', error);\n            alert('Failed to update project');\n        } finally{\n            setSaving(false);\n        }\n    };\n    const generateJavaScriptSnippet = ()=>{\n        var _project_user_apiKeys_, _project_user_apiKeys, _project_user;\n        const apiKey = (project === null || project === void 0 ? void 0 : (_project_user = project.user) === null || _project_user === void 0 ? void 0 : (_project_user_apiKeys = _project_user.apiKeys) === null || _project_user_apiKeys === void 0 ? void 0 : (_project_user_apiKeys_ = _project_user_apiKeys[0]) === null || _project_user_apiKeys_ === void 0 ? void 0 : _project_user_apiKeys_.key) || 'your-api-key-here';\n        return '<!-- SmartForm Defender -->\\n<script src=\"https://cdn.smartformdefender.com/sdk/v1/smartform.js\"></script>\\n<script>\\n  SmartFormDefender.init({\\n    apiKey: \\''.concat(apiKey, \"',\\n    projectId: '\").concat(projectId, \"',\\n    endpoint: 'https://api.smartformdefender.com/validate/submission'\\n  });\\n</script>\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    }\n    if (!project) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Project not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/dashboard/projects\",\n                        className: \"text-blue-600 hover:text-blue-500 mt-2 inline-block\",\n                        children: \"← Back to Projects\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/projects\",\n                                        className: \"text-blue-600 hover:text-blue-500 text-sm\",\n                                        children: \"← Back to Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900 mt-2\",\n                                        children: project.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Configure project settings and integration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/integrations\",\n                                        className: \"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\",\n                                        children: \"Manage Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/dashboard/projects/\".concat(projectId, \"/analytics\"),\n                                        className: \"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700\",\n                                        children: \"View Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Basic Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Project Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.name,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        name: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Domain\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.domain,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        domain: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"example.com\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: config.description,\n                                                    onChange: (e)=>setConfig((prev)=>({\n                                                                ...prev,\n                                                                description: e.target.value\n                                                            })),\n                                                    rows: 3,\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                    placeholder: \"Describe this project...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Spam Detection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            \"Spam Threshold (\",\n                                                            (config.spamThreshold * 100).toFixed(0),\n                                                            \"%)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        min: \"0\",\n                                                        max: \"1\",\n                                                        step: \"0.1\",\n                                                        value: config.spamThreshold,\n                                                        onChange: (e)=>setConfig((prev)=>({\n                                                                    ...prev,\n                                                                    spamThreshold: parseFloat(e.target.value)\n                                                                })),\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Lenient (0%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Strict (100%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Validation Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"emailValidation\",\n                                                            checked: config.enableEmailValidation,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        enableEmailValidation: e.target.checked\n                                                                    })),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"emailValidation\",\n                                                            className: \"ml-2 block text-sm text-gray-900\",\n                                                            children: \"Enable Email Validation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"phoneValidation\",\n                                                            checked: config.enablePhoneValidation,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        enablePhoneValidation: e.target.checked\n                                                                    })),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"phoneValidation\",\n                                                            className: \"ml-2 block text-sm text-gray-900\",\n                                                            children: \"Enable Phone Validation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"geoValidation\",\n                                                            checked: config.enableGeoValidation,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        enableGeoValidation: e.target.checked\n                                                                    })),\n                                                            className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"geoValidation\",\n                                                            className: \"ml-2 block text-sm text-gray-900\",\n                                                            children: \"Enable Geographic Validation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.enableGeoValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Allowed Countries (comma-separated)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.allowedCountries,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        allowedCountries: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"US, CA, GB\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Blocked Countries (comma-separated)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: config.blockedCountries,\n                                                            onChange: (e)=>setConfig((prev)=>({\n                                                                        ...prev,\n                                                                        blockedCountries: e.target.value\n                                                                    })),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            placeholder: \"CN, RU\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: saveProject,\n                                        disabled: saving,\n                                        className: \"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n                                        children: saving ? 'Saving...' : 'Save Changes'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Installation\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-4\",\n                                            children: \"Add this code to your website's HTML, just before the closing </body> tag:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-900 p-3 rounded text-xs font-mono overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"text-green-400\",\n                                                children: generateJavaScriptSnippet()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>navigator.clipboard.writeText(generateJavaScriptSnippet()),\n                                            className: \"mt-3 w-full px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200\",\n                                            children: \"Copy Code\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"Project Stats\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Total Submissions:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_project__count = project._count) === null || _project__count === void 0 ? void 0 : _project__count.submissions) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Active Integrations:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_project__count1 = project._count) === null || _project__count1 === void 0 ? void 0 : _project__count1.integrations) || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Status:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium \".concat(project.isActive ? 'text-green-600' : 'text-red-600'),\n                                                            children: project.isActive ? 'Active' : 'Inactive'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Created:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: new Date(project.createdAt).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\SmartFormDefender\\\\src\\\\app\\\\dashboard\\\\projects\\\\[id]\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectConfigPage, \"xe+CtWlmcrxFVrscdhVIfTgSbrc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams\n    ];\n});\n_c = ProjectConfigPage;\nvar _c;\n$RefreshReg$(_c, \"ProjectConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/projects/[id]/page.tsx\n"));

/***/ })

});