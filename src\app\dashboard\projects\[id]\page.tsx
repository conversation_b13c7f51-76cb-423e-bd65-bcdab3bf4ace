'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function ProjectConfigPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [config, setConfig] = useState({
    name: '',
    domain: '',
    description: '',
    spamThreshold: 0.7,
    enableEmailValidation: true,
    enablePhoneValidation: true,
    enableGeoValidation: false,
    allowedCountries: '',
    blockedCountries: '',
    isActive: true
  });

  useEffect(() => {
    if (projectId) {
      loadProject();
    }
  }, [projectId]);

  const loadProject = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}`);

      if (response.ok) {
        const data = await response.json();
        const projectData = data.project;

        setProject(projectData);
        setConfig({
          name: projectData.name,
          domain: projectData.domain,
          description: projectData.description || '',
          spamThreshold: projectData.spamThreshold,
          enableEmailValidation: projectData.enableEmailValidation,
          enablePhoneValidation: projectData.enablePhoneValidation,
          enableGeoValidation: projectData.enableGeoValidation,
          allowedCountries: projectData.allowedCountries || '',
          blockedCountries: projectData.blockedCountries || '',
          isActive: projectData.isActive
        });
      } else {
        console.error('Failed to load project:', response.status);
      }
    } catch (error) {
      console.error('Failed to load project:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveProject = async () => {
    setSaving(true);
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        alert('Project updated successfully!');
        loadProject(); // Refresh data
      } else {
        alert('Failed to update project');
      }
    } catch (error) {
      console.error('Failed to save project:', error);
      alert('Failed to update project');
    } finally {
      setSaving(false);
    }
  };

  const generateJavaScriptSnippet = () => {
    const apiKey = project?.user?.apiKeys?.[0]?.key || 'your-api-key-here';
    return `<!-- SmartForm Defender -->
<script src="https://cdn.smartformdefender.com/sdk/v1/smartform.js"></script>
<script>
  SmartFormDefender.init({
    apiKey: '${apiKey}',
    projectId: '${projectId}',
    endpoint: 'https://api.smartformdefender.com/validate/submission'
  });
</script>`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Project not found</h2>
          <Link href="/dashboard/projects" className="text-blue-600 hover:text-blue-500 mt-2 inline-block">
            ← Back to Projects
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <Link href="/dashboard/projects" className="text-blue-600 hover:text-blue-500 text-sm">
                ← Back to Projects
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mt-2">{project.name}</h1>
              <p className="text-gray-600">Configure project settings and integration</p>
            </div>
            <div className="flex space-x-4">
              <Link
                href={`/dashboard/integrations`}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Manage Integrations
              </Link>
              <Link
                href={`/dashboard/projects/${projectId}/analytics`}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                View Analytics
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Configuration Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Settings */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Settings</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Project Name
                  </label>
                  <input
                    type="text"
                    value={config.name}
                    onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Domain
                  </label>
                  <input
                    type="text"
                    value={config.domain}
                    onChange={(e) => setConfig(prev => ({ ...prev, domain: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="example.com"
                  />
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={config.description}
                  onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe this project..."
                />
              </div>
            </div>

            {/* Spam Detection Settings */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Spam Detection</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Spam Threshold ({(config.spamThreshold * 100).toFixed(0)}%)
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={config.spamThreshold}
                    onChange={(e) => setConfig(prev => ({ ...prev, spamThreshold: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Lenient (0%)</span>
                    <span>Strict (100%)</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Validation Settings */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Validation Settings</h2>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="emailValidation"
                    checked={config.enableEmailValidation}
                    onChange={(e) => setConfig(prev => ({ ...prev, enableEmailValidation: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="emailValidation" className="ml-2 block text-sm text-gray-900">
                    Enable Email Validation
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="phoneValidation"
                    checked={config.enablePhoneValidation}
                    onChange={(e) => setConfig(prev => ({ ...prev, enablePhoneValidation: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="phoneValidation" className="ml-2 block text-sm text-gray-900">
                    Enable Phone Validation
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="geoValidation"
                    checked={config.enableGeoValidation}
                    onChange={(e) => setConfig(prev => ({ ...prev, enableGeoValidation: e.target.checked }))}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="geoValidation" className="ml-2 block text-sm text-gray-900">
                    Enable Geographic Validation
                  </label>
                </div>
              </div>

              {config.enableGeoValidation && (
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Allowed Countries (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={config.allowedCountries}
                      onChange={(e) => setConfig(prev => ({ ...prev, allowedCountries: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="US, CA, GB"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Blocked Countries (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={config.blockedCountries}
                      onChange={(e) => setConfig(prev => ({ ...prev, blockedCountries: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="CN, RU"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={saveProject}
                disabled={saving}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Installation Code */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Installation</h3>
              <p className="text-sm text-gray-600 mb-4">
                Add this code to your website's HTML, just before the closing &lt;/body&gt; tag:
              </p>
              <div className="bg-gray-900 p-3 rounded text-xs font-mono overflow-x-auto">
                <pre className="text-green-400">{generateJavaScriptSnippet()}</pre>
              </div>
              <button
                onClick={() => navigator.clipboard.writeText(generateJavaScriptSnippet())}
                className="mt-3 w-full px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
              >
                Copy Code
              </button>
            </div>

            {/* Project Stats */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Project Stats</h3>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total Submissions:</span>
                  <span className="font-medium">{project._count?.submissions || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Active Integrations:</span>
                  <span className="font-medium">{project._count?.integrations || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Status:</span>
                  <span className={`font-medium ${project.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {project.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Created:</span>
                  <span className="font-medium">{new Date(project.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
